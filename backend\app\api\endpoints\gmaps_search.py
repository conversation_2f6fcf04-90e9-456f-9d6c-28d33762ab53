"""
Endpoint para busca no Google Maps
"""
import uuid
from fastapi import APIRouter, HTTPException, BackgroundTasks
from app.models.schemas import GoogleMapsSearchRequest, GoogleMapsSearchResponse
from app.services.job_manager import job_manager
from app.workers.gmaps_worker import start_gmaps_search

router = APIRouter()


@router.post("/start", response_model=GoogleMapsSearchResponse)
async def start_gmaps_search_endpoint(
    request: GoogleMapsSearchRequest,
    background_tasks: BackgroundTasks
):
    """
    Inicia uma busca no Google Maps
    """
    try:
        # Gerar ID único para o job
        job_id = str(uuid.uuid4())
        
        # Criar job no gerenciador
        job_manager.create_job(
            job_id=job_id,
            job_type="gmaps_search",
            parameters={
                "search_for": request.search_for,
                "location": request.location,
                "total": request.total,
                "file_format": request.file_format.value,
                "headless": request.headless
            }
        )
        
        # Iniciar worker em background
        background_tasks.add_task(
            start_gmaps_search,
            job_id=job_id,
            search_for=request.search_for,
            location=request.location,
            total=request.total,
            file_format=request.file_format.value,
            headless=request.headless
        )
        
        return GoogleMapsSearchResponse(
            job_id=job_id,
            message=f"Busca no Google Maps iniciada. Buscando '{request.search_for}' em '{request.location}'"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao iniciar busca no Google Maps: {str(e)}"
        )


@router.post("/cancel/{job_id}")
async def cancel_gmaps_search(job_id: str):
    """
    Cancela uma busca no Google Maps em andamento
    """
    try:
        success = job_manager.cancel_job(job_id)
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Job não encontrado ou não pode ser cancelado"
            )
        
        return {"message": "Busca cancelada com sucesso"}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao cancelar busca: {str(e)}"
        )
