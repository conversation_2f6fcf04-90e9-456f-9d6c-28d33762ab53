import React, { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Box,
  Chip,
  List,
  ListItem,
  ListItemText,
  Paper,
  Button,
  IconButton,
  Tooltip,
} from '@mui/material'
import {
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  Wifi as ConnectedIcon,
  WifiOff as DisconnectedIcon,
} from '@mui/icons-material'
import { useQuery } from 'react-query'
import { jobsApi, filesApi } from '../../services/api'
import { useSnackbar } from 'notistack'
import { useJobWebSocket } from '../../hooks/useWebSocket'

function JobProgress({ jobId, onComplete, title = "Status da Busca" }) {
  const { enqueueSnackbar } = useSnackbar()

  // WebSocket para atualizações em tempo real
  const {
    logs: wsLogs,
    progress: wsProgress,
    status: wsStatus,
    isConnected,
    error: wsError,
    clearLogs,
  } = useJobWebSocket(jobId)

  // Buscar informações do job
  const { data: job, refetch } = useQuery(
    ['job', jobId],
    () => jobsApi.get(jobId),
    {
      refetchInterval: isConnected ? 10000 : 2000, // Menos frequente se WebSocket conectado
      enabled: !!jobId,
      onSuccess: (data) => {
        // Verificar se o job foi concluído
        if (data.status === 'completed' || data.status === 'failed' || data.status === 'cancelled') {
          if (onComplete) {
            onComplete(data)
          }
        }
      },
    }
  )

  // Usar dados do WebSocket quando disponíveis, senão usar dados da API
  const currentJob = job ? {
    ...job,
    progress: wsProgress !== undefined ? wsProgress : job.progress,
    status: wsStatus || job.status,
  } : null

  const currentLogs = wsLogs.length > 0 ? wsLogs : (job?.logs?.slice(-10) || [])

  // Notificar sobre erros de WebSocket
  useEffect(() => {
    if (wsError) {
      enqueueSnackbar(`Erro de conexão: ${wsError}`, { variant: 'warning' })
    }
  }, [wsError, enqueueSnackbar])

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success'
      case 'running':
        return 'info'
      case 'failed':
        return 'error'
      case 'cancelled':
        return 'default'
      default:
        return 'warning'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'pending':
        return 'Pendente'
      case 'running':
        return 'Executando'
      case 'completed':
        return 'Concluído'
      case 'failed':
        return 'Falhou'
      case 'cancelled':
        return 'Cancelado'
      default:
        return status
    }
  }

  const getLogLevelColor = (level) => {
    switch (level) {
      case 'error':
        return 'error'
      case 'warning':
        return 'warning'
      case 'success':
        return 'success'
      default:
        return 'info'
    }
  }

  const handleDownload = async () => {
    if (!currentJob?.result_file) return

    try {
      const filename = currentJob.result_file.split('/').pop()
      const response = await filesApi.download(filename)

      // Criar URL para download
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', filename)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)

      enqueueSnackbar('Download iniciado!', { variant: 'success' })
    } catch (error) {
      enqueueSnackbar(`Erro no download: ${error.message}`, { variant: 'error' })
    }
  }

  if (!currentJob) {
    return (
      <Card>
        <CardContent>
          <Typography>Carregando informações do job...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            {title}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Tooltip title={isConnected ? 'WebSocket conectado' : 'WebSocket desconectado'}>
              <IconButton size="small" color={isConnected ? 'success' : 'default'}>
                {isConnected ? <ConnectedIcon /> : <DisconnectedIcon />}
              </IconButton>
            </Tooltip>
            <Chip
              label={getStatusText(currentJob.status)}
              color={getStatusColor(currentJob.status)}
            />
          </Box>
        </Box>

        {/* Progresso */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Progresso
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {currentJob.progress}%
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={currentJob.progress}
            sx={{ height: 8, borderRadius: 4 }}
          />
        </Box>

        {/* Informações do Job */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="body2" color="text.secondary">
            ID do Job: {currentJob.job_id}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Criado em: {new Date(currentJob.created_at).toLocaleString()}
          </Typography>
          {currentJob.started_at && (
            <Typography variant="body2" color="text.secondary">
              Iniciado em: {new Date(currentJob.started_at).toLocaleString()}
            </Typography>
          )}
          {currentJob.completed_at && (
            <Typography variant="body2" color="text.secondary">
              Concluído em: {new Date(currentJob.completed_at).toLocaleString()}
            </Typography>
          )}
        </Box>

        {/* Botão de Download */}
        {currentJob.result_file && currentJob.status === 'completed' && (
          <Box sx={{ mb: 3 }}>
            <Button
              variant="contained"
              startIcon={<DownloadIcon />}
              onClick={handleDownload}
              fullWidth
            >
              Baixar Resultados
            </Button>
          </Box>
        )}

        {/* Erro */}
        {currentJob.error_message && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="body2" color="error" sx={{ fontWeight: 'bold' }}>
              Erro:
            </Typography>
            <Typography variant="body2" color="error">
              {currentJob.error_message}
            </Typography>
          </Box>
        )}

        {/* Logs */}
        <Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="body2" fontWeight="bold">
              Logs Recentes
            </Typography>
            <Button
              size="small"
              startIcon={<RefreshIcon />}
              onClick={() => refetch()}
            >
              Atualizar
            </Button>
          </Box>

          <Paper variant="outlined" sx={{ maxHeight: 200, overflow: 'auto' }}>
            {currentLogs.length > 0 ? (
              <List dense>
                {currentLogs.map((log, index) => (
                  <ListItem key={index}>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Chip
                            label={log.level}
                            size="small"
                            color={getLogLevelColor(log.level)}
                            sx={{ minWidth: 60 }}
                          />
                          <Typography variant="body2">
                            {log.message}
                          </Typography>
                        </Box>
                      }
                      secondary={new Date(log.timestamp).toLocaleTimeString()}
                    />
                  </ListItem>
                ))}
              </List>
            ) : (
              <Box sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  {isConnected ? 'Aguardando logs...' : 'Nenhum log disponível'}
                </Typography>
              </Box>
            )}
          </Paper>
        </Box>
      </CardContent>
    </Card>
  )
}

export default JobProgress
