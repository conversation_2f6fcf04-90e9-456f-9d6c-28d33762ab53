# 🧪 TeemoFlow - G<PERSON><PERSON> de Testes e Qualidade

Este documento descreve como executar testes e verificações de qualidade no projeto TeemoFlow.

## 📋 Índice

- [Backend - Testes](#backend---testes)
- [Backend - Qualidade de Código](#backend---qualidade-de-código)
- [Frontend - Qualidade de Código](#frontend---qualidade-de-código)
- [Comandos Rápidos](#comandos-rápidos)
- [Configurações](#configurações)

---

## 🔧 Backend - Testes

### Pré-requisitos

```bash
cd backend
pip install -r requirements.txt
```

### Executar Todos os Testes

```bash
# Script automatizado
python run_tests.py

# Ou usando pytest diretamente
python -m pytest tests/ -v
```

### Testes por Categoria

```bash
# Apenas testes unitários
python -m pytest tests/ -m "unit" -v

# Apenas testes de integração
python -m pytest tests/ -m "integration" -v

# Testes de API
python -m pytest tests/ -m "api" -v

# Testes de Workers
python -m pytest tests/ -m "worker" -v
```

### Testes com Cobertura

```bash
# Cobertura completa
python -m pytest tests/ --cov=app --cov-report=term-missing --cov-report=html

# Ver relatório HTML
open htmlcov/index.html  # macOS/Linux
start htmlcov/index.html # Windows
```

### Usando Makefile

```bash
# Ver todos os comandos disponíveis
make help

# Executar testes
make test

# Testes com cobertura
make test-cov

# Testes unitários apenas
make test-unit

# Testes de integração apenas
make test-integration
```

---

## 🔍 Backend - Qualidade de Código

### Verificações Automáticas

```bash
# Script automatizado (recomendado)
python run_quality_checks.py

# Ou usando Makefile
make quality
```

### Verificações Individuais

```bash
# Formatação com Black
python -m black --check app/ tests/
python -m black app/ tests/  # Para formatar

# Ordenação de imports com isort
python -m isort --check-only app/ tests/
python -m isort app/ tests/  # Para corrigir

# Linting com flake8
python -m flake8 app/ tests/

# Verificação de tipos com mypy
python -m mypy app/
```

### Formatação Automática

```bash
# Formatar código automaticamente
python run_quality_checks.py format

# Ou usando Makefile
make format
```

---

## 🎨 Frontend - Qualidade de Código

### Pré-requisitos

```bash
cd frontend
npm install
```

### Verificações Automáticas

```bash
# Script automatizado (recomendado)
node run_quality_checks.js

# Ou usando npm scripts
npm run quality
```

### Verificações Individuais

```bash
# ESLint
npm run lint
npm run lint:fix  # Para corrigir automaticamente

# Prettier
npm run format:check
npm run format  # Para formatar

# Build de produção
npm run build
```

### Formatação Automática

```bash
# Formatar código automaticamente
node run_quality_checks.js format

# Ou usando npm
npm run quality:fix
```

---

## ⚡ Comandos Rápidos

### Verificação Completa do Projeto

```bash
# Backend
cd backend
make ci  # install + quality + test-cov

# Frontend
cd frontend
npm install
npm run quality
npm run build
```

### Desenvolvimento Diário

```bash
# Backend - verificação rápida
cd backend
make dev-check  # format + lint + test

# Frontend - verificação rápida
cd frontend
npm run quality:fix
npm run build
```

### Antes de Commit

```bash
# Backend
cd backend
make format
make quality
make test

# Frontend
cd frontend
npm run quality:fix
npm run build
```

---

## ⚙️ Configurações

### Backend

- **pytest**: `pytest.ini` e `pyproject.toml`
- **Black**: `pyproject.toml`
- **isort**: `pyproject.toml`
- **flake8**: `.flake8`
- **mypy**: `pyproject.toml`

### Frontend

- **ESLint**: `.eslintrc.cjs`
- **Prettier**: `.prettierrc` e `.prettierignore`
- **Package scripts**: `package.json`

---

## 📊 Métricas de Qualidade

### Cobertura de Testes (Backend)

- **Meta**: ≥ 80%
- **Atual**: Configurado para falhar se < 80%
- **Relatório**: `htmlcov/index.html`

### Padrões de Código

#### Backend
- **Formatação**: Black (linha 88 caracteres)
- **Imports**: isort (compatível com Black)
- **Linting**: flake8 (complexidade máxima: 10)
- **Tipos**: mypy (verificação parcial)

#### Frontend
- **Formatação**: Prettier (linha 100 caracteres)
- **Linting**: ESLint (regras React + acessibilidade)
- **Estilo**: Single quotes, sem semicolons

---

## 🚀 Integração Contínua

### Pipeline Sugerido

```yaml
# Exemplo para GitHub Actions
- name: Backend Tests
  run: |
    cd backend
    pip install -r requirements.txt
    make ci

- name: Frontend Quality
  run: |
    cd frontend
    npm install
    npm run quality
    npm run build
```

### Scripts de CI/CD

- **Backend**: `make ci` (install + quality + test-cov)
- **Frontend**: `npm run quality && npm run build`

---

## 🔧 Troubleshooting

### Problemas Comuns

1. **Testes falhando**: Verificar dependências instaladas
2. **Formatação**: Executar `make format` (backend) ou `npm run format` (frontend)
3. **Imports**: Executar `isort` no backend
4. **Build falhando**: Verificar erros de ESLint no frontend

### Limpeza

```bash
# Backend
cd backend
make clean

# Frontend
cd frontend
rm -rf node_modules dist
npm install
```

---

## 📚 Recursos Adicionais

- [pytest Documentation](https://docs.pytest.org/)
- [Black Documentation](https://black.readthedocs.io/)
- [ESLint Rules](https://eslint.org/docs/rules/)
- [Prettier Configuration](https://prettier.io/docs/en/configuration.html)

---

**🎯 Objetivo**: Manter alta qualidade de código com testes abrangentes e padrões consistentes.
