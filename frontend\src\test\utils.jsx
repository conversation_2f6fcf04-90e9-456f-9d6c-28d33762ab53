import React from 'react'
import { render } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from 'react-query'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import { SnackbarProvider } from 'notistack'
import { vi } from 'vitest'

// Tema padrão para testes
const testTheme = createTheme({
  palette: {
    mode: 'light',
  },
})

// Provider customizado para testes (sem Router - será fornecido pelos testes)
export function TestProvider({ children, queryClient, theme = testTheme }) {
  const testQueryClient = queryClient || new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        cacheTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  })

  return (
    <QueryClientProvider client={testQueryClient}>
      <ThemeProvider theme={theme}>
        <SnackbarProvider maxSnack={3}>
          {children}
        </SnackbarProvider>
      </ThemeProvider>
    </QueryClientProvider>
  )
}

// Função de render customizada
export function renderWithProviders(ui, options = {}) {
  const {
    queryClient,
    theme,
    ...renderOptions
  } = options

  const Wrapper = ({ children }) => (
    <TestProvider queryClient={queryClient} theme={theme}>
      {children}
    </TestProvider>
  )

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// Mock de dados para testes
export const mockJobData = {
  id: 'test-job-1',
  type: 'flexible_search',
  status: 'running',
  progress: 50,
  total: 100,
  created_at: '2024-01-01T10:00:00Z',
  parameters: {
    location: 'São Paulo, SP',
    business_type: 'restaurante',
    radius: 5000,
  },
  results: [],
}

export const mockFileData = {
  id: 'test-file-1',
  name: 'test-export.xlsx',
  type: 'export',
  size: 1024,
  created_at: '2024-01-01T10:00:00Z',
  download_url: '/api/files/download/test-file-1',
}

export const mockSearchResults = [
  {
    id: 1,
    name: 'Restaurante Teste',
    address: 'Rua Teste, 123',
    phone: '(11) 99999-9999',
    rating: 4.5,
    website: 'https://teste.com',
  },
  {
    id: 2,
    name: 'Pizzaria Exemplo',
    address: 'Av. Exemplo, 456',
    phone: '(11) 88888-8888',
    rating: 4.2,
    website: 'https://exemplo.com',
  },
]

// Mocks de API
export const createMockApi = () => ({
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
})

// Mock do WebSocket
export const createMockWebSocket = () => ({
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  connect: vi.fn(),
  disconnect: vi.fn(),
  connected: true,
})

// Helpers para testes assíncronos
export const waitForLoadingToFinish = () =>
  new Promise(resolve => setTimeout(resolve, 0))

// Mock de notificações
export const mockEnqueueSnackbar = vi.fn()

// Mock do useNavigate
export const mockNavigate = vi.fn()

// Mock do useLocation
export const mockLocation = {
  pathname: '/',
  search: '',
  hash: '',
  state: null,
}

// Função para criar um QueryClient de teste
export const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      cacheTime: 0,
      staleTime: 0,
    },
    mutations: {
      retry: false,
    },
  },
  logger: {
    log: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
})

// Mock de dados de contexto
export const mockThemeContext = {
  isDarkMode: false,
  toggleTheme: vi.fn(),
  theme: testTheme,
}

// Função para simular eventos de arquivo
export const createMockFile = (name = 'test.xlsx', type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') => {
  const file = new File(['test content'], name, { type })
  return file
}

// Mock de dados de upload
export const mockUploadResponse = {
  success: true,
  file_id: 'test-upload-1',
  filename: 'test.xlsx',
  message: 'Arquivo enviado com sucesso',
}

// Função para aguardar próximo tick
export const nextTick = () => new Promise(resolve => setTimeout(resolve, 0))

// Mock de resposta de API padrão
export const createMockApiResponse = (data, status = 200) => ({
  data,
  status,
  statusText: 'OK',
  headers: {},
  config: {},
})

// Mock de erro de API
export const createMockApiError = (message = 'API Error', status = 500) => {
  const error = new Error(message)
  error.response = {
    data: { detail: message },
    status,
    statusText: 'Internal Server Error',
  }
  return error
}

// Função para limpar todos os mocks
export const clearAllMocks = () => {
  vi.clearAllMocks()
  mockEnqueueSnackbar.mockClear()
  mockNavigate.mockClear()
  mockThemeContext.toggleTheme.mockClear()
}

// Re-export de testing-library utilities
export * from '@testing-library/react'
export { userEvent } from '@testing-library/user-event'
