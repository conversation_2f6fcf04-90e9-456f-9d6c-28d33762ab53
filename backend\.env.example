# Configurações da aplicação
APP_NAME=TeemoFlow
APP_VERSION=2.0.0
DEBUG=True

# Configurações de CORS
ALLOWED_HOSTS=["http://localhost:3000", "http://127.0.0.1:3000"]

# Configurações de banco de dados
DATABASE_URL=sqlite:///./teemoflow.db

# Configurações Redis (para Celery)
REDIS_URL=redis://localhost:6379/0

# Configurações de segurança
SECRET_KEY=your-secret-key-here-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Configurações de arquivos
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=uploads
EXPORT_DIR=exports

# Configurações do Selenium
CHROME_HEADLESS=True
SELENIUM_TIMEOUT=30

# Configurações de logs
LOG_LEVEL=INFO
LOG_FILE=logs/teemoflow.log
