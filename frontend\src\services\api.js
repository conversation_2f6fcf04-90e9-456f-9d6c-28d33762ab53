import axios from 'axios'

// Configurar axios
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
})

// Interceptor para tratamento de erros
api.interceptors.response.use(
  (response) => response.data,
  (error) => {
    console.error('API Error:', error)

    if (error.response) {
      // Erro do servidor
      throw new Error(error.response.data.detail || 'Erro do servidor')
    } else if (error.request) {
      // Erro de rede
      throw new Error('Erro de conexão com o servidor')
    } else {
      // Outro erro
      throw new Error('Erro inesperado')
    }
  }
)

// API para busca flexível
export const flexibleSearchApi = {
  start: (data) => api.post('/flexible-search/start', data),
  cancel: (jobId) => api.post(`/flexible-search/cancel/${jobId}`),
  getSearchTypes: () => api.get('/flexible-search/search-types'),
  getBusinessSuggestions: () => api.get('/flexible-search/business-suggestions'),
}

// API para busca por CEP (compatibilidade)
export const cepSearchApi = {
  start: (data) => api.post('/cep-search/start', data),
  cancel: (jobId) => api.post(`/cep-search/cancel/${jobId}`),
}

// API para Google Maps
export const gmapsSearchApi = {
  start: (data) => api.post('/gmaps-search/start', data),
  cancel: (jobId) => api.post(`/gmaps-search/cancel/${jobId}`),
}

// API para busca automatizada
export const automatedSearchApi = {
  start: (data) => api.post('/automated-search/start', data),
  cancel: (jobId) => api.post(`/automated-search/cancel/${jobId}`),
  pause: (jobId) => api.post(`/automated-search/pause/${jobId}`),
  resume: (jobId) => api.post(`/automated-search/resume/${jobId}`),
}

// API para mensagens
export const messagingApi = {
  uploadContacts: (file) => {
    const formData = new FormData()
    formData.append('file', file)
    return api.post('/messaging/upload-contacts', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
  send: (data) => api.post('/messaging/send', data),
  cancel: (jobId) => api.post(`/messaging/cancel/${jobId}`),
  pause: (jobId) => api.post(`/messaging/pause/${jobId}`),
  resume: (jobId) => api.post(`/messaging/resume/${jobId}`),
  generateVariation: (message) => api.post('/messaging/generate-variation', { message }),
}

// API para jobs
export const jobsApi = {
  list: (params = {}) => api.get('/jobs/', { params }),
  get: (jobId) => api.get(`/jobs/${jobId}`),
  delete: (jobId) => api.delete(`/jobs/${jobId}`),
  cancel: (jobId) => api.post(`/jobs/${jobId}/cancel`),
}

// API para arquivos
export const filesApi = {
  upload: (file) => {
    const formData = new FormData()
    formData.append('file', file)
    return api.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
  listExports: () => api.get('/files/exports'),
  listUploads: () => api.get('/files/uploads'),
  download: (filename) => {
    return axios.get(`/api/files/download/${filename}`, {
      responseType: 'blob',
    })
  },
  deleteExport: (filename) => api.delete(`/files/exports/${filename}`),
  deleteUpload: (filename) => api.delete(`/files/uploads/${filename}`),
}

export default api
