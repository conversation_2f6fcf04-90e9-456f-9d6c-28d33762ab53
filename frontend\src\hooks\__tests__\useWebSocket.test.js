import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useWebSocket } from '../useWebSocket'

// Mock do useSnackbar
vi.mock('notistack', () => ({
  useSnackbar: () => ({
    enqueueSnackbar: vi.fn(),
  }),
}))

// Mock do WebSocket
const mockWebSocket = {
  send: vi.fn(),
  close: vi.fn(),
  readyState: 1, // OPEN
  onopen: null,
  onmessage: null,
  onclose: null,
  onerror: null,
}

// Mock global do WebSocket
global.WebSocket = vi.fn(() => mockWebSocket)
global.WebSocket.CONNECTING = 0
global.WebSocket.OPEN = 1
global.WebSocket.CLOSING = 2
global.WebSocket.CLOSED = 3

describe('useWebSocket', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockWebSocket.readyState = 1 // OPEN
    mockWebSocket.onopen = null
    mockWebSocket.onmessage = null
    mockWebSocket.onclose = null
    mockWebSocket.onerror = null
  })

  describe('Inicialização', () => {
    it('deve retornar estado inicial correto', () => {
      const { result } = renderHook(() => useWebSocket('ws://localhost:8000'))

      expect(result.current.isConnected).toBe(false)
      expect(result.current.lastMessage).toBe(null)
      expect(result.current.error).toBe(null)
      expect(typeof result.current.sendMessage).toBe('function')
      expect(typeof result.current.connect).toBe('function')
      expect(typeof result.current.disconnect).toBe('function')
    })

    it('deve conectar automaticamente quando autoConnect é true', () => {
      renderHook(() => useWebSocket('ws://localhost:8000', { autoConnect: true }))

      expect(global.WebSocket).toHaveBeenCalled()
    })
  })

  describe('Envio de mensagens', () => {
    it('deve enviar mensagem quando conectado', () => {
      const { result } = renderHook(() => useWebSocket('ws://localhost:8000'))

      act(() => {
        const success = result.current.sendMessage('test message')
        expect(success).toBe(true)
        expect(mockWebSocket.send).toHaveBeenCalledWith('test message')
      })
    })

    it('deve serializar objetos para JSON', () => {
      const { result } = renderHook(() => useWebSocket('ws://localhost:8000'))

      act(() => {
        const testObj = { type: 'test', data: 'value' }
        result.current.sendMessage(testObj)
        expect(mockWebSocket.send).toHaveBeenCalledWith(JSON.stringify(testObj))
      })
    })
  })

  describe('Conexão e desconexão', () => {
    it('deve conectar manualmente', () => {
      const { result } = renderHook(() => useWebSocket('ws://localhost:8000', { autoConnect: false }))

      act(() => {
        result.current.connect()
      })

      expect(global.WebSocket).toHaveBeenCalled()
    })

    it('deve desconectar manualmente', () => {
      const { result } = renderHook(() => useWebSocket('ws://localhost:8000'))

      act(() => {
        result.current.disconnect()
      })

      expect(mockWebSocket.close).toHaveBeenCalled()
    })
  })

  describe('Limpeza', () => {
    it('deve limpar recursos ao desmontar', () => {
      const { unmount } = renderHook(() => useWebSocket('ws://localhost:8000'))

      unmount()

      expect(mockWebSocket.close).toHaveBeenCalled()
    })
  })
})
