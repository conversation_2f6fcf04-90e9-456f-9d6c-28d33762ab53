"""
Testes para APIs
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, Mock
import json

# Importar a aplicação FastAPI
from main import app


@pytest.fixture
def client():
    """Cliente de teste FastAPI"""
    return TestClient(app)


@pytest.mark.api
class TestJobsAPI:
    """Testes para API de Jobs"""
    
    @patch('app.api.endpoints.jobs.job_manager')
    def test_list_jobs_empty(self, mock_job_manager, client):
        """Testa listagem de jobs vazia"""
        mock_job_manager.list_jobs.return_value = []
        
        response = client.get("/api/jobs/")
        
        assert response.status_code == 200
        assert response.json() == []
    
    @patch('app.api.endpoints.jobs.job_manager')
    def test_list_jobs_with_data(self, mock_job_manager, client, sample_job_data):
        """Testa listagem de jobs com dados"""
        mock_job = Mock()
        mock_job.dict.return_value = sample_job_data
        mock_job_manager.list_jobs.return_value = [mock_job]
        
        response = client.get("/api/jobs/")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["job_id"] == sample_job_data["job_id"]
    
    @patch('app.api.endpoints.jobs.job_manager')
    def test_get_job_existing(self, mock_job_manager, client, sample_job_data):
        """Testa busca de job existente"""
        mock_job = Mock()
        mock_job.dict.return_value = sample_job_data
        mock_job_manager.get_job.return_value = mock_job
        
        response = client.get(f"/api/jobs/{sample_job_data['job_id']}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["job_id"] == sample_job_data["job_id"]
    
    @patch('app.api.endpoints.jobs.job_manager')
    def test_get_job_not_found(self, mock_job_manager, client):
        """Testa busca de job inexistente"""
        mock_job_manager.get_job.return_value = None
        
        response = client.get("/api/jobs/nonexistent-job")
        
        assert response.status_code == 404
        assert "Job não encontrado" in response.json()["detail"]
    
    @patch('app.api.endpoints.jobs.job_manager')
    def test_cancel_job_success(self, mock_job_manager, client):
        """Testa cancelamento bem-sucedido de job"""
        mock_job_manager.cancel_job.return_value = True
        
        response = client.post("/api/jobs/test-job-123/cancel")
        
        assert response.status_code == 200
        assert response.json()["message"] == "Job cancelado com sucesso"
    
    @patch('app.api.endpoints.jobs.job_manager')
    def test_cancel_job_failure(self, mock_job_manager, client):
        """Testa falha no cancelamento de job"""
        mock_job_manager.cancel_job.return_value = False
        
        response = client.post("/api/jobs/test-job-123/cancel")
        
        assert response.status_code == 400
        assert "Não foi possível cancelar" in response.json()["detail"]
    
    @patch('app.api.endpoints.jobs.job_manager')
    def test_pause_job_success(self, mock_job_manager, client):
        """Testa pausa bem-sucedida de job"""
        mock_job_manager.pause_job.return_value = True
        
        response = client.post("/api/jobs/test-job-123/pause")
        
        assert response.status_code == 200
        assert response.json()["message"] == "Job pausado com sucesso"
    
    @patch('app.api.endpoints.jobs.job_manager')
    def test_resume_job_success(self, mock_job_manager, client):
        """Testa retomada bem-sucedida de job"""
        mock_job_manager.resume_job.return_value = True
        
        response = client.post("/api/jobs/test-job-123/resume")
        
        assert response.status_code == 200
        assert response.json()["message"] == "Job retomado com sucesso"
    
    @patch('app.api.endpoints.jobs.job_manager')
    def test_delete_job_success(self, mock_job_manager, client):
        """Testa exclusão bem-sucedida de job"""
        mock_job_manager.delete_job.return_value = True
        
        response = client.delete("/api/jobs/test-job-123")
        
        assert response.status_code == 200
        assert response.json()["message"] == "Job deletado com sucesso"


@pytest.mark.api
class TestFlexibleSearchAPI:
    """Testes para API de Busca Flexível"""
    
    @patch('app.api.endpoints.flexible_search.start_flexible_search')
    @patch('app.api.endpoints.flexible_search.job_manager')
    def test_start_flexible_search_success(self, mock_job_manager, mock_start_search, client, sample_search_request):
        """Testa início bem-sucedido de busca flexível"""
        mock_job_manager.create_job.return_value = "test-job-123"
        
        response = client.post("/api/flexible-search/start", json=sample_search_request)
        
        assert response.status_code == 200
        data = response.json()
        assert data["job_id"] == "test-job-123"
        assert data["message"] == "Busca flexível iniciada com sucesso"
        mock_start_search.assert_called_once()
    
    def test_start_flexible_search_invalid_data(self, client):
        """Testa início de busca com dados inválidos"""
        invalid_request = {
            "search_term": "",  # Termo vazio
            "location": "São Paulo",
            "max_results": 50
        }
        
        response = client.post("/api/flexible-search/start", json=invalid_request)
        
        assert response.status_code == 422  # Validation error


@pytest.mark.api
class TestAutomatedSearchAPI:
    """Testes para API de Busca Automatizada"""
    
    @patch('app.api.endpoints.automated_search.start_automated_search')
    @patch('app.api.endpoints.automated_search.job_manager')
    def test_start_automated_search_success(self, mock_job_manager, mock_start_search, client, sample_automated_search_request):
        """Testa início bem-sucedido de busca automatizada"""
        mock_job_manager.create_job.return_value = "test-job-123"
        
        response = client.post("/api/automated-search/start", json=sample_automated_search_request)
        
        assert response.status_code == 200
        data = response.json()
        assert data["job_id"] == "test-job-123"
        assert data["message"] == "Busca automatizada iniciada com sucesso"
        mock_start_search.assert_called_once()
    
    def test_start_automated_search_empty_queries(self, client):
        """Testa início de busca automatizada sem consultas"""
        invalid_request = {
            "queries": [],  # Lista vazia
            "file_format": "excel"
        }
        
        response = client.post("/api/automated-search/start", json=invalid_request)
        
        assert response.status_code == 422  # Validation error


@pytest.mark.api
class TestMessagingAPI:
    """Testes para API de Mensagens"""
    
    @patch('app.api.endpoints.messaging.start_messaging')
    @patch('app.api.endpoints.messaging.job_manager')
    def test_start_messaging_success(self, mock_job_manager, mock_start_messaging, client, sample_messaging_request):
        """Testa início bem-sucedido de envio de mensagens"""
        mock_job_manager.create_job.return_value = "test-job-123"
        
        response = client.post("/api/messaging/start", json=sample_messaging_request)
        
        assert response.status_code == 200
        data = response.json()
        assert data["job_id"] == "test-job-123"
        assert data["message"] == "Envio de mensagens iniciado com sucesso"
        mock_start_messaging.assert_called_once()
    
    @patch('app.services.message_generator.MessageGenerator.generate_variations')
    def test_generate_message_variations_success(self, mock_generate, client):
        """Testa geração bem-sucedida de variações de mensagem"""
        mock_generate.return_value = [
            "Olá João, temos uma oferta especial!",
            "Oi João, confira nossa promoção exclusiva!",
            "João, não perca esta oportunidade única!"
        ]
        
        request_data = {
            "template": "Olá {nome}, temos uma oferta especial!",
            "count": 3
        }
        
        response = client.post("/api/messaging/generate-variations", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["variations"]) == 3
        assert all("João" in variation for variation in data["variations"])


@pytest.mark.api
class TestHealthCheck:
    """Testes para Health Check"""
    
    def test_health_check(self, client):
        """Testa endpoint de health check"""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
    
    def test_root_redirect(self, client):
        """Testa redirecionamento da raiz"""
        response = client.get("/", allow_redirects=False)
        
        # Deve retornar alguma resposta válida (pode ser redirect ou página)
        assert response.status_code in [200, 307, 308]


@pytest.mark.api
class TestWebSocketEndpoints:
    """Testes para endpoints WebSocket"""
    
    def test_websocket_job_endpoint_exists(self, client):
        """Testa se o endpoint WebSocket de job existe"""
        # Teste básico para verificar se o endpoint existe
        # WebSocket precisa de teste específico com websocket client
        response = client.get("/api/jobs/ws/test-job-123")
        
        # Deve retornar erro 426 (Upgrade Required) para conexão HTTP normal
        assert response.status_code == 426
    
    def test_websocket_global_endpoint_exists(self, client):
        """Testa se o endpoint WebSocket global existe"""
        response = client.get("/api/jobs/ws/all")
        
        # Deve retornar erro 426 (Upgrade Required) para conexão HTTP normal
        assert response.status_code == 426
