"""
Endpoint para gerenciamento de arquivos
"""
import os
from fastapi import APIRouter, HTTPException, UploadFile, File
from fastapi.responses import FileResponse
from typing import List
from app.core.config import settings
from app.services.file_service import save_upload_file, list_export_files, delete_file

router = APIRouter()


@router.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """
    Faz upload de um arquivo
    """
    try:
        # Validar tamanho do arquivo
        if file.size > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"Arquivo muito grande. Máximo permitido: {settings.MAX_FILE_SIZE / 1024 / 1024:.1f}MB"
            )
        
        # Salvar arquivo
        file_path = await save_upload_file(file)
        
        return {
            "filename": file.filename,
            "file_path": file_path,
            "size": file.size
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao fazer upload do arquivo: {str(e)}"
        )


@router.get("/exports")
async def list_exports():
    """
    Lista arquivos de exportação disponíveis
    """
    try:
        files = list_export_files()
        return {"files": files}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao listar arquivos: {str(e)}"
        )


@router.get("/download/{filename}")
async def download_file(filename: str):
    """
    Faz download de um arquivo de exportação
    """
    try:
        file_path = os.path.join(settings.EXPORT_DIR, filename)
        
        # Verificar se o arquivo existe
        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=404,
                detail="Arquivo não encontrado"
            )
        
        # Verificar se está dentro do diretório permitido
        if not os.path.abspath(file_path).startswith(os.path.abspath(settings.EXPORT_DIR)):
            raise HTTPException(
                status_code=403,
                detail="Acesso negado"
            )
        
        return FileResponse(
            path=file_path,
            filename=filename,
            media_type='application/octet-stream'
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao fazer download do arquivo: {str(e)}"
        )


@router.delete("/exports/{filename}")
async def delete_export_file(filename: str):
    """
    Remove um arquivo de exportação
    """
    try:
        success = delete_file(filename, settings.EXPORT_DIR)
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Arquivo não encontrado"
            )
        
        return {"message": "Arquivo removido com sucesso"}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao remover arquivo: {str(e)}"
        )


@router.get("/uploads")
async def list_uploads():
    """
    Lista arquivos de upload disponíveis
    """
    try:
        files = list_export_files(settings.UPLOAD_DIR)
        return {"files": files}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao listar uploads: {str(e)}"
        )


@router.delete("/uploads/{filename}")
async def delete_upload_file(filename: str):
    """
    Remove um arquivo de upload
    """
    try:
        success = delete_file(filename, settings.UPLOAD_DIR)
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Arquivo não encontrado"
            )
        
        return {"message": "Arquivo removido com sucesso"}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao remover arquivo: {str(e)}"
        )
