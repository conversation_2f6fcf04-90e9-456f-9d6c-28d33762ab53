"""
Configurações e fixtures compartilhadas para testes
"""
import pytest
import asyncio
from unittest.mock import Mo<PERSON>, MagicMock
from fastapi.testclient import TestClient
from app.services.job_manager import JobManager
from app.services.websocket_manager import WebSocketManager
from app.models.schemas import Job<PERSON>tatus, LogLevel


@pytest.fixture(scope="session")
def event_loop():
    """Cria um event loop para toda a sessão de testes"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_job_manager():
    """Mock do JobManager para testes"""
    manager = Mock(spec=JobManager)
    manager._jobs = {}
    manager._lock = MagicMock()
    
    # Configurar métodos básicos
    manager.create_job.return_value = "test-job-123"
    manager.get_job.return_value = None
    manager.list_jobs.return_value = []
    manager.update_job_status.return_value = True
    manager.add_log.return_value = True
    manager.cancel_job.return_value = True
    manager.pause_job.return_value = True
    manager.resume_job.return_value = True
    manager.delete_job.return_value = True
    
    return manager


@pytest.fixture
def mock_websocket_manager():
    """Mock do WebSocketManager para testes"""
    manager = Mock(spec=WebSocketManager)
    manager._job_connections = {}
    manager._global_connections = set()
    
    # Configurar métodos assíncronos
    async def mock_broadcast_log(job_id, log_entry):
        pass
    
    async def mock_broadcast_status_change(job_id, status):
        pass
    
    async def mock_broadcast_job_completion(job_id, success, error_message=None):
        pass
    
    manager.broadcast_log = mock_broadcast_log
    manager.broadcast_status_change = mock_broadcast_status_change
    manager.broadcast_job_completion = mock_broadcast_job_completion
    manager.get_connection_count.return_value = 0
    
    return manager


@pytest.fixture
def sample_job_data():
    """Dados de exemplo para jobs"""
    return {
        "job_id": "test-job-123",
        "job_type": "flexible_search",
        "status": JobStatus.PENDING,
        "progress": 0,
        "created_at": "2024-01-01T10:00:00",
        "started_at": None,
        "completed_at": None,
        "result_file": None,
        "error_message": None,
        "logs": []
    }


@pytest.fixture
def sample_log_entry():
    """Entrada de log de exemplo"""
    return {
        "level": LogLevel.INFO,
        "message": "Test log message",
        "timestamp": "2024-01-01T10:00:00"
    }


@pytest.fixture
def sample_search_request():
    """Requisição de busca de exemplo"""
    return {
        "search_term": "restaurantes",
        "location": "São Paulo, SP",
        "max_results": 50,
        "file_format": "excel",
        "headless": True
    }


@pytest.fixture
def sample_automated_search_request():
    """Requisição de busca automatizada de exemplo"""
    return {
        "queries": [
            {
                "search_term": "restaurantes",
                "location": "São Paulo, SP",
                "max_results": 30
            },
            {
                "search_term": "pizzarias",
                "location": "Rio de Janeiro, RJ",
                "max_results": 20
            }
        ],
        "file_format": "excel",
        "headless": True,
        "interval_between_queries": 5
    }


@pytest.fixture
def sample_messaging_request():
    """Requisição de mensagens de exemplo"""
    return {
        "contacts_file": "contacts.xlsx",
        "message_template": "Olá {nome}, temos uma oferta especial para você!",
        "use_variations": True,
        "delay_between_messages": 3
    }


@pytest.fixture
def mock_selenium_driver():
    """Mock do driver Selenium"""
    driver = Mock()
    
    # Configurar métodos básicos
    driver.get.return_value = None
    driver.quit.return_value = None
    driver.close.return_value = None
    driver.find_element.return_value = Mock()
    driver.find_elements.return_value = []
    driver.execute_script.return_value = None
    driver.current_url = "https://www.google.com/maps"
    driver.page_source = "<html></html>"
    
    # Mock de elementos
    element = Mock()
    element.text = "Test Element"
    element.get_attribute.return_value = "test-value"
    element.click.return_value = None
    element.send_keys.return_value = None
    element.clear.return_value = None
    
    driver.find_element.return_value = element
    driver.find_elements.return_value = [element]
    
    return driver


@pytest.fixture
def mock_webdriver_manager():
    """Mock do WebDriver Manager"""
    manager = Mock()
    manager.install.return_value = "/path/to/chromedriver"
    return manager


@pytest.fixture
def sample_leads_data():
    """Dados de leads de exemplo"""
    return [
        {
            "nome": "Restaurante Teste 1",
            "telefone": "(11) 1234-5678",
            "endereco": "Rua Teste, 123 - São Paulo, SP",
            "website": "https://restaurante1.com",
            "avaliacao": "4.5",
            "total_avaliacoes": "150",
            "categoria": "Restaurante",
            "horario_funcionamento": "Seg-Dom: 11:00-23:00",
            "capturado_em": "2024-01-01 10:00:00"
        },
        {
            "nome": "Restaurante Teste 2",
            "telefone": "(11) 8765-4321",
            "endereco": "Av. Teste, 456 - São Paulo, SP",
            "website": "N/A",
            "avaliacao": "4.2",
            "total_avaliacoes": "89",
            "categoria": "Restaurante",
            "horario_funcionamento": "Seg-Sab: 12:00-22:00",
            "capturado_em": "2024-01-01 10:05:00"
        }
    ]


@pytest.fixture
def temp_file_path(tmp_path):
    """Caminho para arquivo temporário"""
    return tmp_path / "test_file.xlsx"


@pytest.fixture
def mock_file_service():
    """Mock do serviço de arquivos"""
    service = Mock()
    service.export_leads_to_file.return_value = "/path/to/exported/file.xlsx"
    service.save_uploaded_file.return_value = "/path/to/uploaded/file.xlsx"
    service.list_files.return_value = []
    service.delete_file.return_value = True
    return service
