import sys
import time
import traceback
import logging
import os
import pandas as pd
from PyQt6.QtWidgets import (
    QA<PERSON>lication, QWidget, QLabel, QLineEdit, QVBoxLayout, QPushButton,
    QTextBrowser, QMessageBox, QHBoxLayout, QFrame, QProgressBar,
    QFileDialog, QMainWindow, QStatusBar, QGridLayout, QMenu, QMenuBar,
    QTabWidget, QRadioButton, QButtonGroup, QCheckBox, QSpinBox, QComboBox,
    QSplitter, QScrollArea, QTextEdit
)
from PyQt6.QtCore import QThread, pyqtSignal, Qt, QUrl, QTimer, QSize
from PyQt6.QtGui import QIcon, QFont, QPixmap, QDesktopServices, QAction
import logic_bot
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By

# Import our modules
from splash_screen import show_splash
from about_dialog import AboutDialog
from resource_path import resource_path
from google_maps_integration import main_query
from automated_search import AutomatedSearchThread
from message_system import MessageSender, MessageGenerator
from theme import (
    get_application_palette, get_application_stylesheet,
    PRIMARY_COLOR, SECONDARY_COLOR, ACCENT_COLOR, BACKGROUND_COLOR, CARD_COLOR,
    TEXT_PRIMARY, TEXT_SECONDARY, SUCCESS_COLOR, ERROR_COLOR, WARNING_COLOR, INFO_COLOR,
    style_heading_label, style_subheading_label, style_body_label, style_caption_label,
    create_card_frame, style_primary_button, style_secondary_button, style_accent_button,
    get_logo_pixmap
)

# Application version
APP_VERSION = "2.0.0"

class ThreadScraper(QThread):
    sinal_finalizado = pyqtSignal(list)
    sinal_erro = pyqtSignal(str)
    sinal_progresso = pyqtSignal(int, int, dict)
    sinal_log = pyqtSignal(str, str)  # tipo, mensagem
    sinal_verificar_cancelamento = pyqtSignal()
    sinal_cancelado = pyqtSignal(bool)

    def __init__(self, cep, palavra_chave, quantidade, headless_mode=True):
        super().__init__()
        self.cep = cep
        self.palavra_chave = palavra_chave
        self.quantidade = quantidade
        self.cancelado = False
        self.headless_mode = headless_mode

    def run(self):
        try:
            self.sinal_log.emit("info", f"Iniciando o Chrome em modo {'headless' if self.headless_mode else 'visível'}...")

            # Configurar o mecanismo de verificação de cancelamento
            self.sinal_cancelado.connect(self.set_cancelado)

            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            if self.headless_mode:
                chrome_options.add_argument("--headless")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--log-level=3")  # Silencia mensagens de log do Chrome

            try:
                self.sinal_log.emit("info", "Instalando/atualizando o ChromeDriver...")
                service = Service(ChromeDriverManager().install())
                driver = webdriver.Chrome(service=service, options=chrome_options)
            except Exception as e:
                self.sinal_erro.emit(f"Erro ao iniciar o Chrome: {str(e)}")
                return

            # Verificar cancelamento
            self.sinal_verificar_cancelamento.emit()
            if self.cancelado:
                self.sinal_log.emit("aviso", "Operação cancelada pelo usuário")
                driver.quit()
                return

            self.sinal_log.emit("info", "Abrindo o Google Maps...")
            driver.get('https://www.google.com/maps/')

            # Verificar cancelamento
            self.sinal_verificar_cancelamento.emit()
            if self.cancelado:
                self.sinal_log.emit("aviso", "Operação cancelada pelo usuário")
                driver.quit()
                return

            # Buscar por CEP
            if not logic_bot.buscar_cep(driver, self.cep):
                self.sinal_erro.emit("Falha ao buscar o CEP especificado. Verifique se o CEP é válido.")
                driver.quit()
                return

            # Verificar cancelamento
            self.sinal_verificar_cancelamento.emit()
            if self.cancelado:
                self.sinal_log.emit("aviso", "Operação cancelada pelo usuário")
                driver.quit()
                return

            # Buscar por palavra-chave
            if not logic_bot.buscar_palavra_chave(driver, self.palavra_chave):
                self.sinal_erro.emit(f"Não foram encontrados resultados para '{self.palavra_chave}' na região do CEP informado.")
                driver.quit()
                return

            # Verificar cancelamento
            self.sinal_verificar_cancelamento.emit()
            if self.cancelado:
                self.sinal_log.emit("aviso", "Operação cancelada pelo usuário")
                driver.quit()
                return

            # Extrair clientes
            self.sinal_log.emit("info", f"Iniciando extração de {self.quantidade} leads...")
            clientes = logic_bot.extrair_clientes(driver, self.quantidade, self.atualizar_progresso)

            self.sinal_finalizado.emit(clientes)
        except Exception as e:
            error_msg = f"Erro inesperado: {str(e)}\n\n{traceback.format_exc()}"
            self.sinal_erro.emit(error_msg)
            logging.error(error_msg)
        finally:
            if 'driver' in locals():
                self.sinal_log.emit("info", "Fechando o navegador...")
                driver.quit()

    def set_cancelado(self, valor):
        """Define o estado de cancelamento"""
        self.cancelado = valor

    def atualizar_progresso(self, atual, total, cliente):
        self.sinal_progresso.emit(atual, total, cliente)
        self.sinal_log.emit("info", f"Capturado lead {atual}/{total}: {cliente['nome']}")

class ThreadScraperGoogleMaps(QThread):
    """Thread para busca no Google Maps por termo e localização (estilo PROSPECTO)"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_signal = pyqtSignal(str)
    continue_query = pyqtSignal(str, bool)

    def __init__(self, search_for, total, location, save_dir, file_format, headless_mode=True):
        super().__init__()
        self.search_for = search_for
        self.total = total
        self.location = location
        self.save_dir = save_dir
        self.file_format = file_format
        self.headless_mode = headless_mode
        self.continue_response = None

    def run(self):
        try:
            # Callback para atualizar a interface
            def callback(msg, require_response=False):
                self.status_updated.emit(msg)

                # Se precisar de resposta, emitir sinal e aguardar
                if require_response:
                    self.continue_response = None
                    self.continue_query.emit(msg, True)

                    # Aguardar resposta
                    timeout = 30  # segundos
                    start_time = time.time()
                    while self.continue_response is None and time.time() - start_time < timeout:
                        time.sleep(0.5)

                    # Se não houver resposta, assumir "Não"
                    if self.continue_response is None:
                        return "N"

                    return self.continue_response

                return None

            # Executar a consulta
            result = main_query(
                search_for=self.search_for,
                total=self.total,
                location=self.location,
                save_dir=self.save_dir,
                file_format=self.file_format,
                headless_mode=self.headless_mode,
                callback=callback
            )

            self.finished_signal.emit(result)

        except Exception as e:
            error_msg = f"ERRO: {str(e)}"
            self.status_updated.emit(error_msg)
            self.finished_signal.emit(error_msg)

class ProspectoApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.clientes_extraidos = []
        self.cancelar_captura = False
        self.initUI()

    def closeEvent(self, event):
        """Manipula o evento de fechamento da janela"""
        if hasattr(self, 'thread_scraper') and self.thread_scraper.isRunning():
            reply = QMessageBox.question(
                self, 'Confirmar Saída',
                'Uma captura está em andamento. Deseja realmente sair?',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # Atualizar flags de cancelamento
                self.cancelar_captura = True
                self.thread_scraper.cancelado = True

                # Mostrar mensagem de encerramento
                self.statusbar.showMessage("Encerrando aplicação...")
                self.adicionar_log("aviso", "Encerrando aplicação durante captura em andamento...")

                # Tentar encerrar a thread de forma limpa
                self.thread_scraper.quit()

                # Esperar até 3 segundos para a thread terminar
                if self.thread_scraper.wait(3000):
                    self.adicionar_log("info", "Thread de captura encerrada com sucesso.")
                else:
                    self.adicionar_log("aviso", "Não foi possível encerrar a thread de captura normalmente. Forçando encerramento.")
                    self.thread_scraper.terminate()

                # Aceitar o evento de fechamento
                event.accept()
            else:
                # Usuário cancelou o fechamento
                event.ignore()
        else:
            # Não há captura em andamento, pode fechar normalmente
            event.accept()

    def format_cep(self):
        cep = self.entrada_cep.text().replace('-', '')
        if len(cep) > 5:
            formatted_cep = f"{cep[:5]}-{cep[5:]}"
            self.entrada_cep.setText(formatted_cep)
            self.entrada_cep.setCursorPosition(len(formatted_cep))

    def initUI(self):
        # Configurações da janela principal
        self.setWindowTitle(f'PROSPECTO v{APP_VERSION}')
        self.setMinimumSize(1100, 850)
        self.setWindowIcon(QIcon(resource_path('prospecto.png')))

        # Criar menu
        self.criar_menu()

        # Widget central
        widget_central = QWidget()
        self.setCentralWidget(widget_central)

        # Layout principal
        layout_principal = QVBoxLayout(widget_central)
        layout_principal.setContentsMargins(25, 25, 25, 25)  # Aumentado as margens
        layout_principal.setSpacing(20)  # Aumentado o espaçamento entre elementos

        # Adicionar cabeçalho com logo e título
        header_widget = QWidget()
        header_widget.setStyleSheet(f"background-color: {CARD_COLOR}; border-radius: 8px;")
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(20, 10, 20, 10)

        # Logo
        logo_label = QLabel()
        pixmap = QPixmap(resource_path('prospecto.png'))
        scaled_pixmap = pixmap.scaled(80, 80, Qt.AspectRatioMode.KeepAspectRatio,
                                     Qt.TransformationMode.SmoothTransformation)
        logo_label.setPixmap(scaled_pixmap)

        # Título e subtítulo
        title_container = QVBoxLayout()
        title_container.setSpacing(5)  # Aumentado o espaçamento entre título e subtítulo

        app_title = QLabel("PROSPECTO")
        app_title.setStyleSheet(f"font-size: 32px; font-weight: bold; color: {PRIMARY_COLOR};")  # Aumentado o tamanho da fonte

        app_subtitle = QLabel("Captura de Leads do Google Maps")
        app_subtitle.setStyleSheet(f"font-size: 18px; color: {TEXT_SECONDARY}; margin-top: 5px;")

        title_container.addWidget(app_title)
        title_container.addWidget(app_subtitle)

        # Adicionar ao layout do cabeçalho
        header_layout.addWidget(logo_label)
        header_layout.addLayout(title_container)
        header_layout.addStretch()

        layout_principal.addWidget(header_widget)

        # Criar as abas
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet(f"""
            QTabBar::tab {{
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                background-color: #E0E0E0;
            }}
            QTabBar::tab:selected {{
                background-color: {PRIMARY_COLOR};
                color: white;
            }}
        """)

        # Criar as diferentes abas
        self.create_cep_tab()
        self.create_gmaps_tab()
        self.create_automated_search_tab()
        self.create_messaging_tab()

        # Adicionar as abas ao widget de abas
        self.tabs.addTab(self.cep_tab, "Busca por CEP")
        self.tabs.addTab(self.gmaps_tab, "Google Maps")
        self.tabs.addTab(self.automated_search_tab, "Busca Automatizada")
        self.tabs.addTab(self.messaging_tab, "Mensagens")

        # Adicionar ícones às abas
        self.tabs.setTabIcon(0, QIcon.fromTheme("search"))
        self.tabs.setTabIcon(1, QIcon.fromTheme("map-symbolic"))
        self.tabs.setTabIcon(2, QIcon.fromTheme("system-search"))
        self.tabs.setTabIcon(3, QIcon.fromTheme("mail-message"))

        layout_principal.addWidget(self.tabs)

        # Área de log
        log_frame = QFrame()
        log_frame.setObjectName("logFrame")
        log_frame.setStyleSheet(f"background-color: {CARD_COLOR}; border-radius: 8px; border: 1px solid #E0E0E0;")
        log_layout = QVBoxLayout(log_frame)

        log_header = QHBoxLayout()
        log_label = QLabel('Log de operações:')
        log_label.setStyleSheet(f"font-size: 14px; color: {TEXT_PRIMARY}; font-weight: bold;")
        log_header.addWidget(log_label)

        clear_log_button = QPushButton('Limpar Log')
        clear_log_button.setStyleSheet(f"""
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 12px;
        """)
        clear_log_button.setMaximumWidth(100)
        clear_log_button.clicked.connect(self.limpar_log)
        log_header.addWidget(clear_log_button)

        log_layout.addLayout(log_header)

        self.navegador_log = QTextBrowser()
        self.navegador_log.setMaximumHeight(150)
        self.navegador_log.setStyleSheet(f"""
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px;
            font-size: 13px;
        """)
        log_layout.addWidget(self.navegador_log)

        layout_principal.addWidget(log_frame)

        # Barra de status
        self.statusbar = QStatusBar()
        self.setStatusBar(self.statusbar)
        self.statusbar.showMessage("Pronto")
        self.statusbar.setStyleSheet(f"background-color: {BACKGROUND_COLOR}; color: {TEXT_SECONDARY};")

        # Aplicar estilos globais
        self.setStyleSheet(get_application_stylesheet())

    def create_cep_tab(self):
        """Cria a aba de busca por CEP (funcionalidade original do PROSPECTO)"""
        self.cep_tab = QWidget()

        # Criar área de rolagem
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)

        # Widget de conteúdo para a área de rolagem
        content_widget = QWidget()
        cep_layout = QVBoxLayout(content_widget)
        cep_layout.setContentsMargins(20, 20, 20, 20)
        cep_layout.setSpacing(15)

        # Definir o widget de conteúdo para a área de rolagem
        scroll_area.setWidget(content_widget)

        # Adicionar a área de rolagem ao layout principal da aba
        main_layout = QVBoxLayout(self.cep_tab)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll_area)

        # Frame de entrada de dados
        frame_entrada = QFrame()
        frame_entrada.setStyleSheet(f"background-color: {CARD_COLOR}; border-radius: 8px; border: 1px solid #E0E0E0;")
        layout_entrada = QGridLayout(frame_entrada)
        layout_entrada.setContentsMargins(20, 20, 20, 20)
        layout_entrada.setSpacing(15)

        # CEP
        label_cep = QLabel('CEP:')
        label_cep.setMinimumWidth(120)
        label_cep.setStyleSheet("font-size: 14px; font-weight: bold;")
        self.entrada_cep = QLineEdit()
        self.entrada_cep.setMinimumHeight(40)  # Aumentado a altura
        self.entrada_cep.setStyleSheet("padding: 10px; font-size: 14px; border: 1px solid #dee2e6; border-radius: 4px;")
        self.entrada_cep.setPlaceholderText("Digite o CEP (ex: 00000-000)")
        self.entrada_cep.textChanged.connect(self.format_cep)
        layout_entrada.addWidget(label_cep, 0, 0)
        layout_entrada.addWidget(self.entrada_cep, 0, 1)

        # Palavra-chave
        label_palavra_chave = QLabel('Palavra-chave:')
        label_palavra_chave.setMinimumWidth(120)
        label_palavra_chave.setStyleSheet("font-size: 14px; font-weight: bold;")
        self.entrada_palavra_chave = QLineEdit()
        self.entrada_palavra_chave.setMinimumHeight(40)  # Aumentado a altura
        self.entrada_palavra_chave.setStyleSheet("padding: 10px; font-size: 14px; border: 1px solid #dee2e6; border-radius: 4px;")
        self.entrada_palavra_chave.setPlaceholderText("Digite o que deseja buscar (ex: Restaurantes)")
        layout_entrada.addWidget(label_palavra_chave, 1, 0)
        layout_entrada.addWidget(self.entrada_palavra_chave, 1, 1)

        # Quantidade
        label_quantidade = QLabel('Número de Leads:')
        label_quantidade.setMinimumWidth(120)
        label_quantidade.setStyleSheet("font-size: 14px; font-weight: bold;")
        self.entrada_quantidade = QLineEdit()
        self.entrada_quantidade.setMinimumHeight(40)  # Aumentado a altura
        self.entrada_quantidade.setStyleSheet("padding: 10px; font-size: 14px; border: 1px solid #dee2e6; border-radius: 4px;")
        self.entrada_quantidade.setPlaceholderText("Digite a quantidade desejada")
        layout_entrada.addWidget(label_quantidade, 2, 0)
        layout_entrada.addWidget(self.entrada_quantidade, 2, 1)

        # Modo headless
        self.headless_checkbox = QCheckBox("Modo headless (navegador invisível)")
        self.headless_checkbox.setChecked(True)
        layout_entrada.addWidget(self.headless_checkbox, 3, 0, 1, 2)

        # Botões de ação
        botoes_layout = QHBoxLayout()
        botoes_layout.setSpacing(15)

        # Botão iniciar
        self.botao_iniciar = QPushButton('Iniciar Captura')
        self.botao_iniciar.setStyleSheet(f"""
            background-color: {SUCCESS_COLOR};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 12px;
            font-weight: bold;
            font-size: 15px;
        """)
        self.botao_iniciar.setMinimumHeight(40)
        self.botao_iniciar.clicked.connect(self.iniciar_scraping)
        botoes_layout.addWidget(self.botao_iniciar)

        # Botão cancelar
        self.botao_cancelar = QPushButton('Cancelar')
        self.botao_cancelar.setStyleSheet(f"""
            background-color: {ERROR_COLOR};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 12px;
            font-weight: bold;
            font-size: 15px;
        """)
        self.botao_cancelar.setMinimumHeight(40)
        self.botao_cancelar.clicked.connect(self.cancelar_scraping)
        self.botao_cancelar.setEnabled(False)
        botoes_layout.addWidget(self.botao_cancelar)

        layout_entrada.addLayout(botoes_layout, 4, 0, 1, 2)

        cep_layout.addWidget(frame_entrada)

        # Barra de progresso
        progresso_frame = QFrame()
        progresso_frame.setStyleSheet(f"background-color: {CARD_COLOR}; border-radius: 8px; border: 1px solid #E0E0E0;")
        progresso_layout = QVBoxLayout(progresso_frame)

        self.barra_progresso = QProgressBar()
        self.barra_progresso.setFormat(" %p% - %v/%m leads")
        self.barra_progresso.setTextVisible(True)
        self.barra_progresso.setStyleSheet(f"""
            border: 1px solid #dee2e6;
            border-radius: 4px;
            text-align: center;
            background-color: #e9ecef;
            color: {TEXT_PRIMARY};
            height: 25px;
            font-weight: bold;
        """)
        progresso_layout.addWidget(self.barra_progresso)

        cep_layout.addWidget(progresso_frame)

        # Resultados
        resultados_frame = QFrame()
        resultados_frame.setStyleSheet(f"background-color: {CARD_COLOR}; border-radius: 8px; border: 1px solid #E0E0E0;")
        resultados_layout = QVBoxLayout(resultados_frame)

        label_resultados = QLabel('Leads encontrados:')
        label_resultados.setStyleSheet(f"font-size: 16px; color: {TEXT_PRIMARY}; font-weight: bold; padding-top: 10px;")
        resultados_layout.addWidget(label_resultados)

        self.navegador_resultados = QTextBrowser()
        self.navegador_resultados.setStyleSheet(f"""
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-size: 13px;
        """)
        resultados_layout.addWidget(self.navegador_resultados)

        cep_layout.addWidget(resultados_frame, 1)  # Stretch factor para ocupar espaço disponível

        # Botão exportar - com container para centralizar
        botao_exportar_container = QHBoxLayout()

        self.botao_exportar = QPushButton('Exportar para Excel')
        self.botao_exportar.setStyleSheet(f"""
            background-color: {PRIMARY_COLOR};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 12px;
            font-weight: bold;
            font-size: 14px;
        """)
        self.botao_exportar.setIcon(QIcon.fromTheme("document-save"))
        self.botao_exportar.setMinimumHeight(40)
        self.botao_exportar.setMinimumWidth(200)
        self.botao_exportar.clicked.connect(self.exportar_para_xlsx)
        self.botao_exportar.setEnabled(False)

        botao_exportar_container.addStretch()
        botao_exportar_container.addWidget(self.botao_exportar)
        botao_exportar_container.addStretch()

        cep_layout.addLayout(botao_exportar_container)

    def create_gmaps_tab(self):
        """Cria a aba de busca no Google Maps (estilo PROSPECTO)"""
        self.gmaps_tab = QWidget()

        # Criar área de rolagem
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)

        # Widget de conteúdo para a área de rolagem
        content_widget = QWidget()
        gmaps_layout = QVBoxLayout(content_widget)
        gmaps_layout.setContentsMargins(20, 20, 20, 20)
        gmaps_layout.setSpacing(15)

        # Definir o widget de conteúdo para a área de rolagem
        scroll_area.setWidget(content_widget)

        # Adicionar a área de rolagem ao layout principal da aba
        main_layout = QVBoxLayout(self.gmaps_tab)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll_area)

        # Título e descrição
        title_container = QVBoxLayout()
        title_container.setSpacing(5)

        title_label = QLabel("Google Maps")
        title_label.setStyleSheet(f"font-size: 20px; font-weight: bold; color: {PRIMARY_COLOR};")

        desc_label = QLabel("Busque e extraia dados de empresas e profissionais do Google Maps.")
        desc_label.setStyleSheet(f"color: {TEXT_SECONDARY};")

        title_container.addWidget(title_label)
        title_container.addWidget(desc_label)

        gmaps_layout.addLayout(title_container)

        # Formulário de busca
        search_form_card = QFrame()
        search_form_card.setStyleSheet(f"background-color: {CARD_COLOR}; border-radius: 8px; border: 1px solid #E0E0E0;")
        search_form_layout = QVBoxLayout(search_form_card)
        search_form_layout.setContentsMargins(20, 20, 20, 20)

        # Campos de busca
        form_grid = QGridLayout()
        form_grid.setSpacing(15)

        # Termo de busca
        business_label = QLabel("Nome/Termo:")
        business_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        self.business_name = QLineEdit()
        self.business_name.setMinimumHeight(40)  # Aumentado a altura
        self.business_name.setStyleSheet("padding: 10px; font-size: 14px; border: 1px solid #dee2e6; border-radius: 4px;")
        self.business_name.setPlaceholderText("Ex.: 'Restaurante'")
        form_grid.addWidget(business_label, 0, 0)
        form_grid.addWidget(self.business_name, 0, 1)

        # Localização
        location_label = QLabel("Localização:")
        location_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        self.location = QLineEdit()
        self.location.setMinimumHeight(40)  # Aumentado a altura
        self.location.setStyleSheet("padding: 10px; font-size: 14px; border: 1px solid #dee2e6; border-radius: 4px;")
        self.location.setPlaceholderText("Ex.: 'Rio de Janeiro'")
        form_grid.addWidget(location_label, 1, 0)
        form_grid.addWidget(self.location, 1, 1)

        # Total de resultados
        total_label = QLabel("Total de Resultados:")
        self.total_results = QSpinBox()
        self.total_results.setMinimum(1)
        self.total_results.setMaximum(1000)
        self.total_results.setValue(50)
        form_grid.addWidget(total_label, 2, 0)
        form_grid.addWidget(self.total_results, 2, 1)

        # Diretório para salvar
        save_dir_label = QLabel("Diretório para Salvar:")
        save_dir_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        self.save_dir = QLineEdit()
        self.save_dir.setMinimumHeight(40)  # Aumentado a altura
        self.save_dir.setStyleSheet("padding: 10px; font-size: 14px; border: 1px solid #dee2e6; border-radius: 4px; background-color: #f8f9fa;")
        self.save_dir.setReadOnly(True)

        save_dir_layout = QHBoxLayout()
        save_dir_layout.addWidget(self.save_dir)

        select_dir_button = QPushButton()
        select_dir_button.setIcon(QIcon.fromTheme("folder-open"))
        select_dir_button.setToolTip("Selecionar diretório")
        select_dir_button.clicked.connect(self.select_save_dir)
        select_dir_button.setMaximumWidth(40)
        save_dir_layout.addWidget(select_dir_button)

        form_grid.addWidget(save_dir_label, 3, 0)
        form_grid.addLayout(save_dir_layout, 3, 1)

        # Formato do arquivo
        format_label = QLabel("Formato do arquivo:")
        format_layout = QHBoxLayout()

        self.excel_radio = QRadioButton("Excel")
        self.excel_radio.setChecked(True)
        self.csv_radio = QRadioButton("CSV")

        format_layout.addWidget(self.excel_radio)
        format_layout.addWidget(self.csv_radio)
        format_layout.addStretch()

        form_grid.addWidget(format_label, 4, 0)
        form_grid.addLayout(format_layout, 4, 1)

        # Modo headless
        self.gmaps_headless_checkbox = QCheckBox("Modo headless (navegador invisível)")
        self.gmaps_headless_checkbox.setChecked(True)
        form_grid.addWidget(self.gmaps_headless_checkbox, 5, 0, 1, 2)

        search_form_layout.addLayout(form_grid)

        # Botão de busca e progresso
        search_button_layout = QHBoxLayout()

        self.gmaps_search_btn = QPushButton("Executar Consulta")
        self.gmaps_search_btn.setStyleSheet(f"""
            background-color: {SUCCESS_COLOR};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 12px;
            font-weight: bold;
            font-size: 14px;
        """)
        self.gmaps_search_btn.clicked.connect(self.run_gmaps_search)

        self.gmaps_progress = QProgressBar()
        self.gmaps_progress.setTextVisible(False)
        self.gmaps_progress.setMaximumWidth(100)
        self.gmaps_progress.setStyleSheet(f"""
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background-color: #e9ecef;
        """)
        self.gmaps_progress.setVisible(False)

        search_button_layout.addWidget(self.gmaps_search_btn)
        search_button_layout.addWidget(self.gmaps_progress)
        search_button_layout.addStretch()

        search_form_layout.addLayout(search_button_layout)

        # Status
        self.gmaps_status = QLabel("")
        self.gmaps_status.setStyleSheet(f"color: {TEXT_SECONDARY};")
        search_form_layout.addWidget(self.gmaps_status)

        gmaps_layout.addWidget(search_form_card)

        # Resultados
        results_card = QFrame()
        results_card.setStyleSheet(f"background-color: {CARD_COLOR}; border-radius: 8px; border: 1px solid #E0E0E0;")
        results_layout = QVBoxLayout(results_card)
        results_layout.setContentsMargins(20, 20, 20, 20)

        results_title = QLabel("Resultados da Busca")
        results_title.setStyleSheet(f"font-size: 16px; font-weight: bold; color: {TEXT_PRIMARY};")
        results_layout.addWidget(results_title)

        self.gmaps_results = QTextBrowser()
        self.gmaps_results.setStyleSheet(f"""
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-size: 13px;
        """)
        results_layout.addWidget(self.gmaps_results)

        # Botões de exportação
        export_buttons_layout = QHBoxLayout()

        self.gmaps_export_excel = QPushButton("Exportar para Excel")
        self.gmaps_export_excel.setStyleSheet(f"""
            background-color: {PRIMARY_COLOR};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px;
            font-weight: bold;
        """)
        self.gmaps_export_excel.setEnabled(False)
        self.gmaps_export_excel.clicked.connect(self.export_gmaps_results)

        export_buttons_layout.addStretch()
        export_buttons_layout.addWidget(self.gmaps_export_excel)

        results_layout.addLayout(export_buttons_layout)

        gmaps_layout.addWidget(results_card)

    def create_automated_search_tab(self):
        """Cria a aba de busca automatizada (estilo PROSPECTO)"""
        self.automated_search_tab = QWidget()

        # Criar área de rolagem
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)

        # Widget de conteúdo para a área de rolagem
        content_widget = QWidget()
        auto_layout = QVBoxLayout(content_widget)
        auto_layout.setContentsMargins(20, 20, 20, 20)
        auto_layout.setSpacing(15)

        # Definir o widget de conteúdo para a área de rolagem
        scroll_area.setWidget(content_widget)

        # Adicionar a área de rolagem ao layout principal da aba
        main_layout = QVBoxLayout(self.automated_search_tab)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll_area)

        # Título e descrição
        title_container = QVBoxLayout()
        title_container.setSpacing(5)

        title_label = QLabel("Busca Automatizada")
        title_label.setStyleSheet(f"font-size: 20px; font-weight: bold; color: {PRIMARY_COLOR};")

        desc_label = QLabel("Configure múltiplas buscas para serem executadas automaticamente.")
        desc_label.setStyleSheet(f"color: {TEXT_SECONDARY};")

        title_container.addWidget(title_label)
        title_container.addWidget(desc_label)

        auto_layout.addLayout(title_container)

        # Card de configuração
        config_card = QFrame()
        config_card.setStyleSheet(f"background-color: {CARD_COLOR}; border-radius: 8px; border: 1px solid #E0E0E0;")
        config_layout = QVBoxLayout(config_card)
        config_layout.setContentsMargins(20, 20, 20, 20)

        # Título do card
        config_title = QLabel("Consultas de Busca")
        config_title.setStyleSheet(f"font-size: 16px; font-weight: bold; color: {TEXT_PRIMARY};")
        config_layout.addWidget(config_title)

        # Área de rolagem para consultas
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)
        scroll_area.setMinimumHeight(300)

        # Container para os campos de consulta
        scroll_content = QWidget()
        self.queries_container = QVBoxLayout(scroll_content)
        self.queries_container.setSpacing(10)

        scroll_area.setWidget(scroll_content)
        config_layout.addWidget(scroll_area)

        # Lista para armazenar os campos de consulta
        self.query_fields = []

        # Adicionar campos de consulta iniciais
        for i in range(5):  # Começamos com 5 campos, o usuário pode adicionar mais
            self.add_query_field()

        # Botão para adicionar nova consulta
        add_query_button = QPushButton("Adicionar Nova Consulta")
        add_query_button.setIcon(QIcon.fromTheme("list-add"))
        add_query_button.setStyleSheet(f"""
            background-color: {PRIMARY_COLOR};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px;
            font-weight: bold;
        """)
        add_query_button.clicked.connect(self.add_query_field)
        config_layout.addWidget(add_query_button)

        auto_layout.addWidget(config_card)

        # Card de configuração de exportação
        export_card = QFrame()
        export_card.setStyleSheet(f"background-color: {CARD_COLOR}; border-radius: 8px; border: 1px solid #E0E0E0;")
        export_layout = QVBoxLayout(export_card)
        export_layout.setContentsMargins(20, 20, 20, 20)

        # Título do card
        export_title = QLabel("Configurações de Exportação")
        export_title.setStyleSheet(f"font-size: 16px; font-weight: bold; color: {TEXT_PRIMARY};")
        export_layout.addWidget(export_title)

        # Diretório para salvar
        save_dir_layout = QHBoxLayout()
        save_dir_label = QLabel("Diretório para Salvar:")
        self.auto_save_dir = QLineEdit()
        self.auto_save_dir.setReadOnly(True)

        select_dir_button = QPushButton()
        select_dir_button.setIcon(QIcon.fromTheme("folder-open"))
        select_dir_button.setToolTip("Selecionar diretório")
        select_dir_button.clicked.connect(self.select_auto_save_dir)
        select_dir_button.setMaximumWidth(40)

        save_dir_layout.addWidget(save_dir_label)
        save_dir_layout.addWidget(self.auto_save_dir)
        save_dir_layout.addWidget(select_dir_button)

        export_layout.addLayout(save_dir_layout)

        # Formato do arquivo
        format_layout = QHBoxLayout()
        format_label = QLabel("Formato do arquivo:")

        self.auto_excel_radio = QRadioButton("Excel")
        self.auto_excel_radio.setChecked(True)
        self.auto_csv_radio = QRadioButton("CSV")

        format_layout.addWidget(format_label)
        format_layout.addWidget(self.auto_excel_radio)
        format_layout.addWidget(self.auto_csv_radio)
        format_layout.addStretch()

        export_layout.addLayout(format_layout)

        # Modo headless
        self.auto_headless_checkbox = QCheckBox("Modo headless (navegador invisível)")
        self.auto_headless_checkbox.setChecked(True)
        export_layout.addWidget(self.auto_headless_checkbox)

        auto_layout.addWidget(export_card)

        # Card de controles
        controls_card = QFrame()
        controls_card.setStyleSheet(f"background-color: {CARD_COLOR}; border-radius: 8px; border: 1px solid #E0E0E0;")
        controls_layout = QVBoxLayout(controls_card)
        controls_layout.setContentsMargins(20, 20, 20, 20)

        # Título do card
        controls_title = QLabel("Controles de Execução")
        controls_title.setStyleSheet(f"font-size: 16px; font-weight: bold; color: {TEXT_PRIMARY};")
        controls_layout.addWidget(controls_title)

        # Botões de controle
        buttons_layout = QHBoxLayout()

        self.start_button = QPushButton("Iniciar Buscas Automatizadas")
        self.start_button.setIcon(QIcon.fromTheme("media-playback-start"))
        self.start_button.setStyleSheet(f"""
            background-color: {SUCCESS_COLOR};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px;
            font-weight: bold;
        """)
        self.start_button.clicked.connect(self.start_automated_search)

        self.pause_button = QPushButton("Pausar")
        self.pause_button.setIcon(QIcon.fromTheme("media-playback-pause"))
        self.pause_button.setStyleSheet(f"""
            background-color: {WARNING_COLOR};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px;
            font-weight: bold;
        """)
        self.pause_button.clicked.connect(self.pause_resume_automated_search)
        self.pause_button.setEnabled(False)

        self.stop_button = QPushButton("Parar")
        self.stop_button.setIcon(QIcon.fromTheme("media-playback-stop"))
        self.stop_button.setStyleSheet(f"""
            background-color: {ERROR_COLOR};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px;
            font-weight: bold;
        """)
        self.stop_button.clicked.connect(self.stop_automated_search)
        self.stop_button.setEnabled(False)

        buttons_layout.addWidget(self.start_button)
        buttons_layout.addWidget(self.pause_button)
        buttons_layout.addWidget(self.stop_button)

        controls_layout.addLayout(buttons_layout)

        # Barra de progresso
        self.auto_progress = QProgressBar()
        self.auto_progress.setTextVisible(True)
        self.auto_progress.setStyleSheet(f"""
            border: 1px solid #dee2e6;
            border-radius: 4px;
            text-align: center;
            background-color: #e9ecef;
            color: {TEXT_PRIMARY};
            height: 25px;
        """)
        controls_layout.addWidget(self.auto_progress)

        # Status
        self.auto_status = QLabel("")
        self.auto_status.setStyleSheet(f"color: {TEXT_SECONDARY};")
        controls_layout.addWidget(self.auto_status)

        auto_layout.addWidget(controls_card)

    def add_query_field(self):
        """Adiciona um novo campo de consulta à aba de busca automatizada."""
        # Criar container para o campo
        query_container = QFrame()
        query_container.setStyleSheet(f"background-color: white; border-radius: 4px; border: 1px solid #E0E0E0;")
        query_layout = QHBoxLayout(query_container)
        query_layout.setContentsMargins(10, 10, 10, 10)

        # Campo de texto para a consulta
        query_field = QLineEdit()
        query_field.setPlaceholderText("Digite o termo de busca (ex.: 'Restaurantes em São Paulo')")
        query_field.setMinimumHeight(30)

        # Botão para remover o campo
        remove_button = QPushButton()
        remove_button.setIcon(QIcon.fromTheme("list-remove"))
        remove_button.setToolTip("Remover consulta")
        remove_button.setMaximumWidth(30)
        remove_button.setStyleSheet(f"""
            background-color: {ERROR_COLOR};
            color: white;
            border: none;
            border-radius: 4px;
        """)

        # Conectar o botão de remoção
        remove_button.clicked.connect(lambda: self.remove_query_field(query_container))

        # Adicionar widgets ao layout
        query_layout.addWidget(query_field)
        query_layout.addWidget(remove_button)

        # Adicionar ao container principal
        self.queries_container.addWidget(query_container)

        # Armazenar referência ao campo
        self.query_fields.append((query_container, query_field))

        return query_field

    def remove_query_field(self, container):
        """Remove um campo de consulta da aba de busca automatizada."""
        # Remover da lista de campos
        for i, (cont, _) in enumerate(self.query_fields):
            if cont == container:
                self.query_fields.pop(i)
                break

        # Remover da interface
        container.deleteLater()

    def select_auto_save_dir(self):
        """Seleciona o diretório para salvar os resultados da busca automatizada."""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "Selecionar Diretório para Salvar",
            os.path.expanduser("~"),
            QFileDialog.Option.ShowDirsOnly
        )

        if dir_path:
            self.auto_save_dir.setText(dir_path)

    def start_automated_search(self):
        """Inicia a busca automatizada."""
        # Verificar se há consultas
        queries = []
        for _, field in self.query_fields:
            query = field.text().strip()
            if query:
                queries.append(query)

        if not queries:
            QMessageBox.warning(
                self,
                "Nenhuma Consulta",
                "Por favor, adicione pelo menos uma consulta para iniciar a busca automatizada."
            )
            return

        # Verificar diretório de salvamento
        if not self.auto_save_dir.text():
            QMessageBox.warning(
                self,
                "Diretório não Especificado",
                "Por favor, selecione um diretório para salvar os resultados."
            )
            return

        # Configurar interface
        self.start_button.setEnabled(False)
        self.pause_button.setEnabled(True)
        self.stop_button.setEnabled(True)
        self.auto_progress.setValue(0)
        self.auto_status.setText("Iniciando busca automatizada...")
        self.auto_status.setStyleSheet(f"color: {INFO_COLOR};")

        # Obter configurações
        save_dir = self.auto_save_dir.text()
        file_format = "excel" if self.auto_excel_radio.isChecked() else "csv"
        headless_mode = self.auto_headless_checkbox.isChecked()

        # Criar e iniciar thread
        self.auto_thread = AutomatedSearchThread(
            search_queries=queries,
            save_dir=save_dir,
            file_format=file_format,
            headless_mode=headless_mode
        )

        # Conectar sinais
        self.auto_thread.progress_updated.connect(self.auto_progress.setValue)
        self.auto_thread.status_updated.connect(self.update_auto_status)
        self.auto_thread.finished_signal.connect(self.auto_search_finished)
        self.auto_thread.query_completed.connect(self.auto_query_completed)

        # Iniciar thread
        self.auto_thread.start()

        # Adicionar ao log
        self.adicionar_log("info", f"Iniciando busca automatizada com {len(queries)} consultas.")

    def update_auto_status(self, message):
        """Atualiza o status da busca automatizada e adiciona ao log."""
        self.auto_status.setText(message)

        # Adicionar ao log
        if "[ERRO]" in message:
            self.adicionar_log("erro", message)
        elif "[AVISO]" in message:
            self.adicionar_log("aviso", message)
        elif "[INFO]" in message:
            self.adicionar_log("info", message)
        elif "[PROGRESSO]" in message:
            self.adicionar_log("info", message)
        else:
            self.adicionar_log("info", message)

    def auto_query_completed(self, index):
        """Chamado quando uma consulta é concluída."""
        # Atualizar interface
        query_text = self.query_fields[index][1].text()
        self.adicionar_log("sucesso", f"Consulta concluída: '{query_text}'")

        # Destacar visualmente a consulta concluída
        self.query_fields[index][0].setStyleSheet(f"background-color: #E8F5E9; border-radius: 4px; border: 1px solid {SUCCESS_COLOR};")

    def auto_search_finished(self, result):
        """Chamado quando a busca automatizada é finalizada."""
        # Restaurar interface
        self.start_button.setEnabled(True)
        self.pause_button.setEnabled(False)
        self.stop_button.setEnabled(False)

        # Atualizar status
        if "ERRO" in result:
            self.auto_status.setText("Busca automatizada finalizada com erros.")
            self.auto_status.setStyleSheet(f"color: {ERROR_COLOR};")
            self.adicionar_log("erro", result)
        else:
            self.auto_status.setText("Busca automatizada concluída com sucesso!")
            self.auto_status.setStyleSheet(f"color: {SUCCESS_COLOR};")
            self.adicionar_log("sucesso", result)

        # Exibir mensagem
        QMessageBox.information(
            self,
            "Busca Automatizada Concluída",
            result
        )

    def pause_resume_automated_search(self):
        """Pausa ou retoma a busca automatizada."""
        if not hasattr(self, 'auto_thread') or not self.auto_thread.isRunning():
            return

        if self.auto_thread.paused:
            # Retomar
            self.auto_thread.resume()
            self.pause_button.setText("Pausar")
            self.pause_button.setIcon(QIcon.fromTheme("media-playback-pause"))
            self.adicionar_log("info", "Busca automatizada retomada.")
        else:
            # Pausar
            self.auto_thread.pause()
            self.pause_button.setText("Retomar")
            self.pause_button.setIcon(QIcon.fromTheme("media-playback-start"))
            self.adicionar_log("info", "Busca automatizada pausada.")

    def stop_automated_search(self):
        """Para a busca automatizada."""
        if not hasattr(self, 'auto_thread') or not self.auto_thread.isRunning():
            return

        reply = QMessageBox.question(
            self,
            "Parar Busca Automatizada",
            "Tem certeza que deseja parar a busca automatizada?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.auto_thread.stop()
            self.start_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(False)
            self.adicionar_log("aviso", "Busca automatizada interrompida pelo usuário.")

    def create_messaging_tab(self):
        """Cria a aba de mensagens (estilo PROSPECTO)"""
        self.messaging_tab = QWidget()

        # Criar área de rolagem
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)

        # Widget de conteúdo para a área de rolagem
        content_widget = QWidget()
        msg_layout = QVBoxLayout(content_widget)
        msg_layout.setContentsMargins(20, 20, 20, 20)
        msg_layout.setSpacing(15)

        # Definir o widget de conteúdo para a área de rolagem
        scroll_area.setWidget(content_widget)

        # Adicionar a área de rolagem ao layout principal da aba
        main_layout = QVBoxLayout(self.messaging_tab)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll_area)

        # Título e descrição
        title_container = QVBoxLayout()
        title_container.setSpacing(5)

        title_label = QLabel("Mensagens")
        title_label.setStyleSheet(f"font-size: 20px; font-weight: bold; color: {PRIMARY_COLOR};")

        desc_label = QLabel("Envie mensagens para os leads capturados.")
        desc_label.setStyleSheet(f"color: {TEXT_SECONDARY};")

        title_container.addWidget(title_label)
        title_container.addWidget(desc_label)

        msg_layout.addLayout(title_container)

        # Card de importação de contatos
        contacts_card = QFrame()
        contacts_card.setStyleSheet(f"background-color: {CARD_COLOR}; border-radius: 8px; border: 1px solid #E0E0E0;")
        contacts_layout = QVBoxLayout(contacts_card)
        contacts_layout.setContentsMargins(20, 20, 20, 20)

        # Título do card
        contacts_title = QLabel("Importar Contatos")
        contacts_title.setStyleSheet(f"font-size: 16px; font-weight: bold; color: {TEXT_PRIMARY};")
        contacts_layout.addWidget(contacts_title)

        # Descrição
        contacts_desc = QLabel("Importe contatos de um arquivo Excel ou CSV, ou use os leads capturados.")
        contacts_desc.setStyleSheet(f"color: {TEXT_SECONDARY}; margin-bottom: 10px;")
        contacts_layout.addWidget(contacts_desc)

        # Botões de importação
        import_buttons_layout = QHBoxLayout()

        self.import_excel_button = QPushButton("Importar Excel/CSV")
        self.import_excel_button.setIcon(QIcon.fromTheme("document-open"))
        self.import_excel_button.setStyleSheet(f"""
            background-color: {PRIMARY_COLOR};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px;
            font-weight: bold;
        """)
        self.import_excel_button.clicked.connect(self.import_contacts_file)

        self.use_leads_button = QPushButton("Usar Leads Capturados")
        self.use_leads_button.setIcon(QIcon.fromTheme("edit-copy"))
        self.use_leads_button.setStyleSheet(f"""
            background-color: {SECONDARY_COLOR};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px;
            font-weight: bold;
        """)
        self.use_leads_button.clicked.connect(self.use_captured_leads)

        import_buttons_layout.addWidget(self.import_excel_button)
        import_buttons_layout.addWidget(self.use_leads_button)
        import_buttons_layout.addStretch()

        contacts_layout.addLayout(import_buttons_layout)

        # Status da importação
        self.contacts_status = QLabel("")
        self.contacts_status.setStyleSheet(f"color: {TEXT_SECONDARY}; margin-top: 10px;")
        contacts_layout.addWidget(self.contacts_status)

        msg_layout.addWidget(contacts_card)

        # Card de composição de mensagem
        message_card = QFrame()
        message_card.setStyleSheet(f"background-color: {CARD_COLOR}; border-radius: 8px; border: 1px solid #E0E0E0;")
        message_layout = QVBoxLayout(message_card)
        message_layout.setContentsMargins(20, 20, 20, 20)

        # Título do card
        message_title = QLabel("Compor Mensagem")
        message_title.setStyleSheet(f"font-size: 16px; font-weight: bold; color: {TEXT_PRIMARY};")
        message_layout.addWidget(message_title)

        # Descrição
        message_desc = QLabel("Escreva sua mensagem. Use {nome} para incluir o nome do contato.")
        message_desc.setStyleSheet(f"color: {TEXT_SECONDARY}; margin-bottom: 10px;")
        message_layout.addWidget(message_desc)

        # Campo de texto para a mensagem
        self.message_text = QTextEdit()
        self.message_text.setPlaceholderText("Olá {nome}, tudo bem? Gostaria de apresentar nossa empresa...")
        self.message_text.setMinimumHeight(150)
        message_layout.addWidget(self.message_text)

        # Botão para gerar variações com IA
        ai_button_layout = QHBoxLayout()

        self.generate_ai_button = QPushButton("Gerar Variação com IA")
        self.generate_ai_button.setIcon(QIcon.fromTheme("system-run"))
        self.generate_ai_button.setStyleSheet(f"""
            background-color: {ACCENT_COLOR};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px;
            font-weight: bold;
        """)
        self.generate_ai_button.clicked.connect(self.generate_message_variation)

        ai_button_layout.addStretch()
        ai_button_layout.addWidget(self.generate_ai_button)

        message_layout.addLayout(ai_button_layout)

        msg_layout.addWidget(message_card)

        # Card de envio
        send_card = QFrame()
        send_card.setStyleSheet(f"background-color: {CARD_COLOR}; border-radius: 8px; border: 1px solid #E0E0E0;")
        send_layout = QVBoxLayout(send_card)
        send_layout.setContentsMargins(20, 20, 20, 20)

        # Título do card
        send_title = QLabel("Enviar Mensagens")
        send_title.setStyleSheet(f"font-size: 16px; font-weight: bold; color: {TEXT_PRIMARY};")
        send_layout.addWidget(send_title)

        # Configurações de envio
        config_grid = QGridLayout()
        config_grid.setSpacing(10)

        # Intervalo entre mensagens
        interval_label = QLabel("Intervalo entre mensagens (segundos):")
        self.interval_spin = QSpinBox()
        self.interval_spin.setMinimum(10)
        self.interval_spin.setMaximum(120)
        self.interval_spin.setValue(30)
        config_grid.addWidget(interval_label, 0, 0)
        config_grid.addWidget(self.interval_spin, 0, 1)

        # Modo headless
        self.msg_headless_checkbox = QCheckBox("Modo headless (navegador invisível)")
        self.msg_headless_checkbox.setChecked(False)  # Por padrão, visível para o usuário escanear o QR code
        config_grid.addWidget(self.msg_headless_checkbox, 1, 0, 1, 2)

        send_layout.addLayout(config_grid)

        # Botões de controle
        send_buttons_layout = QHBoxLayout()

        self.send_button = QPushButton("Iniciar Envio")
        self.send_button.setIcon(QIcon.fromTheme("mail-send"))
        self.send_button.setStyleSheet(f"""
            background-color: {SUCCESS_COLOR};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px;
            font-weight: bold;
        """)
        self.send_button.clicked.connect(self.start_sending_messages)

        self.pause_msg_button = QPushButton("Pausar")
        self.pause_msg_button.setIcon(QIcon.fromTheme("media-playback-pause"))
        self.pause_msg_button.setStyleSheet(f"""
            background-color: {WARNING_COLOR};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px;
            font-weight: bold;
        """)
        self.pause_msg_button.clicked.connect(self.pause_resume_sending)
        self.pause_msg_button.setEnabled(False)

        self.stop_msg_button = QPushButton("Parar")
        self.stop_msg_button.setIcon(QIcon.fromTheme("media-playback-stop"))
        self.stop_msg_button.setStyleSheet(f"""
            background-color: {ERROR_COLOR};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px;
            font-weight: bold;
        """)
        self.stop_msg_button.clicked.connect(self.stop_sending)
        self.stop_msg_button.setEnabled(False)

        send_buttons_layout.addWidget(self.send_button)
        send_buttons_layout.addWidget(self.pause_msg_button)
        send_buttons_layout.addWidget(self.stop_msg_button)

        send_layout.addLayout(send_buttons_layout)

        # Barra de progresso
        self.msg_progress = QProgressBar()
        self.msg_progress.setTextVisible(True)
        self.msg_progress.setStyleSheet(f"""
            border: 1px solid #dee2e6;
            border-radius: 4px;
            text-align: center;
            background-color: #e9ecef;
            color: {TEXT_PRIMARY};
            height: 25px;
        """)
        send_layout.addWidget(self.msg_progress)

        # Status
        self.msg_status = QLabel("")
        self.msg_status.setStyleSheet(f"color: {TEXT_SECONDARY};")
        send_layout.addWidget(self.msg_status)

        msg_layout.addWidget(send_card)

    def import_contacts_file(self):
        """Importa contatos de um arquivo Excel ou CSV."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Importar Contatos",
            os.path.expanduser("~"),
            "Arquivos Excel (*.xlsx *.xls);;Arquivos CSV (*.csv);;Todos os Arquivos (*)"
        )

        if not file_path:
            return

        try:
            # Determinar o tipo de arquivo
            if file_path.lower().endswith(('.xlsx', '.xls')):
                df = pd.read_excel(file_path)
            elif file_path.lower().endswith('.csv'):
                df = pd.read_csv(file_path, sep=None, engine='python')  # Detecta separador automaticamente
            else:
                raise ValueError("Formato de arquivo não suportado")

            # Verificar se as colunas necessárias existem
            required_columns = ['nome', 'telefone']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                # Tentar mapear colunas comuns
                column_mapping = {}

                # Mapear possíveis nomes para a coluna 'nome'
                name_candidates = ['nome', 'name', 'cliente', 'customer', 'razão social', 'razao social', 'empresa', 'company']
                for candidate in name_candidates:
                    matches = [col for col in df.columns if candidate.lower() in col.lower()]
                    if matches:
                        column_mapping['nome'] = matches[0]
                        break

                # Mapear possíveis nomes para a coluna 'telefone'
                phone_candidates = ['telefone', 'phone', 'celular', 'mobile', 'contato', 'contact', 'tel', 'whatsapp']
                for candidate in phone_candidates:
                    matches = [col for col in df.columns if candidate.lower() in col.lower()]
                    if matches:
                        column_mapping['telefone'] = matches[0]
                        break

                # Se ainda faltam colunas, pedir ao usuário para mapear
                still_missing = [col for col in required_columns if col not in column_mapping]

                if still_missing:
                    QMessageBox.warning(
                        self,
                        "Colunas Ausentes",
                        f"O arquivo não contém as colunas necessárias: {', '.join(still_missing)}.\n\n"
                        "Por favor, verifique se o arquivo tem colunas para nome e telefone."
                    )
                    return

                # Renomear colunas
                df = df.rename(columns=column_mapping)

            # Converter para lista de dicionários
            self.contacts = df.to_dict('records')

            # Atualizar status
            self.contacts_status.setText(f"{len(self.contacts)} contatos importados com sucesso!")
            self.contacts_status.setStyleSheet(f"color: {SUCCESS_COLOR}; margin-top: 10px;")

            # Adicionar ao log
            self.adicionar_log("sucesso", f"Importados {len(self.contacts)} contatos de {os.path.basename(file_path)}")

        except Exception as e:
            self.contacts_status.setText(f"Erro ao importar contatos: {str(e)}")
            self.contacts_status.setStyleSheet(f"color: {ERROR_COLOR}; margin-top: 10px;")
            self.adicionar_log("erro", f"Erro ao importar contatos: {str(e)}")

    def use_captured_leads(self):
        """Usa os leads capturados como contatos para envio de mensagens."""
        if not hasattr(self, 'clientes_extraidos') or not self.clientes_extraidos:
            QMessageBox.warning(
                self,
                "Sem Leads",
                "Não há leads capturados para usar. Por favor, capture leads primeiro."
            )
            return

        # Converter leads para o formato de contatos
        self.contacts = []
        for cliente in self.clientes_extraidos:
            if cliente.get('telefone') and cliente.get('telefone') != "Telefone não disponível":
                self.contacts.append({
                    'nome': cliente.get('nome', 'Cliente'),
                    'telefone': cliente.get('telefone', '')
                })

        if not self.contacts:
            QMessageBox.warning(
                self,
                "Sem Telefones",
                "Nenhum dos leads capturados possui número de telefone. Não é possível enviar mensagens."
            )
            return

        # Atualizar status
        self.contacts_status.setText(f"{len(self.contacts)} contatos obtidos dos leads capturados!")
        self.contacts_status.setStyleSheet(f"color: {SUCCESS_COLOR}; margin-top: 10px;")

        # Adicionar ao log
        self.adicionar_log("sucesso", f"Obtidos {len(self.contacts)} contatos dos leads capturados")

    def generate_message_variation(self):
        """Gera uma variação da mensagem atual usando IA."""
        current_message = self.message_text.toPlainText().strip()

        if not current_message:
            QMessageBox.warning(
                self,
                "Mensagem Vazia",
                "Por favor, escreva uma mensagem antes de gerar variações."
            )
            return

        try:
            # Usar o gerador de mensagens
            new_message = MessageGenerator.generate_variation(current_message)

            # Perguntar ao usuário se deseja usar a nova mensagem
            reply = QMessageBox.question(
                self,
                "Variação Gerada",
                f"Deseja usar a seguinte variação?\n\n{new_message}",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.Yes
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.message_text.setText(new_message)
                self.adicionar_log("info", "Variação de mensagem aplicada")

        except Exception as e:
            QMessageBox.critical(
                self,
                "Erro na Geração",
                f"Ocorreu um erro ao gerar a variação da mensagem:\n\n{str(e)}"
            )
            self.adicionar_log("erro", f"Erro ao gerar variação de mensagem: {str(e)}")

    def start_sending_messages(self):
        """Inicia o envio de mensagens."""
        # Verificar se há contatos
        if not hasattr(self, 'contacts') or not self.contacts:
            QMessageBox.warning(
                self,
                "Sem Contatos",
                "Por favor, importe contatos ou use leads capturados antes de enviar mensagens."
            )
            return

        # Verificar se há mensagem
        message_template = self.message_text.toPlainText().strip()
        if not message_template:
            QMessageBox.warning(
                self,
                "Sem Mensagem",
                "Por favor, escreva uma mensagem antes de iniciar o envio."
            )
            return

        # Confirmar envio
        reply = QMessageBox.question(
            self,
            "Confirmar Envio",
            f"Deseja iniciar o envio de mensagens para {len(self.contacts)} contatos?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        # Configurar interface
        self.send_button.setEnabled(False)
        self.pause_msg_button.setEnabled(True)
        self.stop_msg_button.setEnabled(True)
        self.msg_progress.setValue(0)
        self.msg_progress.setMaximum(len(self.contacts))
        self.msg_status.setText("Iniciando envio de mensagens...")
        self.msg_status.setStyleSheet(f"color: {INFO_COLOR};")

        # Obter configurações
        interval = self.interval_spin.value()
        headless_mode = self.msg_headless_checkbox.isChecked()

        # Criar e iniciar thread
        self.msg_thread = MessageSender(
            contacts=self.contacts,
            message_template=message_template,
            interval=interval,
            headless_mode=headless_mode
        )

        # Conectar sinais
        self.msg_thread.progress_updated.connect(self.msg_progress.setValue)
        self.msg_thread.status_updated.connect(self.update_msg_status)
        self.msg_thread.finished_signal.connect(self.msg_sending_finished)
        self.msg_thread.message_sent.connect(self.message_sent)

        # Iniciar thread
        self.msg_thread.start()

        # Adicionar ao log
        self.adicionar_log("info", f"Iniciando envio de mensagens para {len(self.contacts)} contatos.")

    def update_msg_status(self, message):
        """Atualiza o status do envio de mensagens e adiciona ao log."""
        self.msg_status.setText(message)

        # Adicionar ao log
        if "Erro" in message or "⚠️" in message:
            self.adicionar_log("erro", message)
        elif "✓" in message:
            self.adicionar_log("sucesso", message)
        else:
            self.adicionar_log("info", message)

    def message_sent(self, _):
        """Chamado quando uma mensagem é enviada com sucesso."""
        # Atualizar progresso
        current = self.msg_progress.value() + 1
        self.msg_progress.setValue(current)

    def msg_sending_finished(self, result):
        """Chamado quando o envio de mensagens é finalizado."""
        # Restaurar interface
        self.send_button.setEnabled(True)
        self.pause_msg_button.setEnabled(False)
        self.stop_msg_button.setEnabled(False)

        # Atualizar status
        if "Erro" in result:
            self.msg_status.setText("Envio de mensagens finalizado com erros.")
            self.msg_status.setStyleSheet(f"color: {ERROR_COLOR};")
            self.adicionar_log("erro", result)
        else:
            self.msg_status.setText("Envio de mensagens concluído com sucesso!")
            self.msg_status.setStyleSheet(f"color: {SUCCESS_COLOR};")
            self.adicionar_log("sucesso", result)

        # Exibir mensagem
        QMessageBox.information(
            self,
            "Envio de Mensagens Concluído",
            result
        )

    def pause_resume_sending(self):
        """Pausa ou retoma o envio de mensagens."""
        if not hasattr(self, 'msg_thread') or not self.msg_thread.isRunning():
            return

        if self.msg_thread.paused:
            # Retomar
            self.msg_thread.resume()
            self.pause_msg_button.setText("Pausar")
            self.pause_msg_button.setIcon(QIcon.fromTheme("media-playback-pause"))
            self.adicionar_log("info", "Envio de mensagens retomado.")
        else:
            # Pausar
            self.msg_thread.pause()
            self.pause_msg_button.setText("Retomar")
            self.pause_msg_button.setIcon(QIcon.fromTheme("media-playback-start"))
            self.adicionar_log("info", "Envio de mensagens pausado.")

    def stop_sending(self):
        """Para o envio de mensagens."""
        if not hasattr(self, 'msg_thread') or not self.msg_thread.isRunning():
            return

        reply = QMessageBox.question(
            self,
            "Parar Envio de Mensagens",
            "Tem certeza que deseja parar o envio de mensagens?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.msg_thread.stop()
            self.send_button.setEnabled(True)
            self.pause_msg_button.setEnabled(False)
            self.stop_msg_button.setEnabled(False)
            self.adicionar_log("aviso", "Envio de mensagens interrompido pelo usuário.")

    def criar_menu(self):
        """Cria a barra de menu do aplicativo"""
        menubar = self.menuBar()

        # Menu Arquivo
        menu_arquivo = menubar.addMenu('&Arquivo')

        # Ação Exportar
        acao_exportar = QAction('&Exportar para Excel', self)
        acao_exportar.setShortcut('Ctrl+E')
        acao_exportar.setStatusTip('Exportar leads capturados para Excel')
        acao_exportar.triggered.connect(self.exportar_para_xlsx)
        menu_arquivo.addAction(acao_exportar)

        menu_arquivo.addSeparator()

        # Ação Sair
        acao_sair = QAction('&Sair', self)
        acao_sair.setShortcut('Ctrl+Q')
        acao_sair.setStatusTip('Sair do aplicativo')
        acao_sair.triggered.connect(self.close)
        menu_arquivo.addAction(acao_sair)

        # Menu Ajuda
        menu_ajuda = menubar.addMenu('&Ajuda')

        # Ação Sobre
        acao_sobre = QAction('&Sobre', self)
        acao_sobre.setStatusTip('Informações sobre o aplicativo')
        acao_sobre.triggered.connect(self.mostrar_sobre)
        menu_ajuda.addAction(acao_sobre)

    def mostrar_sobre(self):
        """Exibe o diálogo Sobre"""
        dialog = AboutDialog(self)
        dialog.exec()

    def select_save_dir(self):
        """Seleciona o diretório para salvar os resultados"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "Selecionar Diretório para Salvar",
            os.path.expanduser("~"),
            QFileDialog.Option.ShowDirsOnly
        )

        if dir_path:
            self.save_dir.setText(dir_path)

    def run_gmaps_search(self):
        """Executa a busca no Google Maps (estilo PROSPECTO)"""
        # Verificar se os campos obrigatórios estão preenchidos
        if not self.business_name.text() or not self.location.text() or not self.save_dir.text():
            QMessageBox.warning(
                self,
                "Campos Obrigatórios",
                "Por favor, preencha todos os campos obrigatórios."
            )
            return

        # Desabilitar botão de busca
        self.gmaps_search_btn.setEnabled(False)
        self.gmaps_progress.setValue(0)
        self.gmaps_progress.setVisible(True)

        # Obter valores dos campos
        search_for = f"{self.business_name.text()} {self.location.text()}"
        total = self.total_results.value()
        location = self.location.text()
        save_dir = self.save_dir.text()
        file_format = "excel" if self.excel_radio.isChecked() else "csv"
        headless_mode = self.gmaps_headless_checkbox.isChecked()

        # Limpar resultados anteriores
        self.gmaps_results.clear()
        self.gmaps_status.setText("Iniciando busca...")
        self.gmaps_status.setStyleSheet(f"color: {INFO_COLOR};")

        # Criar e iniciar thread
        self.gmaps_thread = ThreadScraperGoogleMaps(
            search_for=search_for,
            total=total,
            location=location,
            save_dir=save_dir,
            file_format=file_format,
            headless_mode=headless_mode
        )
        self.gmaps_thread.progress_updated.connect(self.gmaps_progress.setValue)
        self.gmaps_thread.status_updated.connect(self.update_gmaps_status)
        self.gmaps_thread.finished_signal.connect(self.gmaps_search_finished)
        self.gmaps_thread.continue_query.connect(self.show_continue_dialog)
        self.gmaps_thread.start()

    def update_gmaps_status(self, message):
        """Atualiza o status da busca no Google Maps e adiciona ao log."""
        self.gmaps_status.setText(message)

        # Adicionar ao log
        if "[ERRO]" in message:
            self.adicionar_log("erro", message)
        elif "[AVISO]" in message:
            self.adicionar_log("aviso", message)
        elif "[INFO]" in message:
            self.adicionar_log("info", message)
        elif "[PROGRESSO]" in message:
            self.adicionar_log("info", message)

            # Adicionar ao resultado
            if "Processando registro" in message:
                # Extrair informações do registro atual
                self.gmaps_results.append(f"<div style='color: {TEXT_SECONDARY};'>{message}</div>")
        else:
            self.adicionar_log("info", message)

    def show_continue_dialog(self, message, require_response):
        """Mostra um diálogo para continuar a busca."""
        if not require_response:
            return

        reply = QMessageBox.question(
            self,
            "Continuar Busca?",
            message,
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.Yes
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.gmaps_thread.continue_response = "S"
        else:
            self.gmaps_thread.continue_response = "N"

    def gmaps_search_finished(self, result):
        """Callback chamado quando a busca no Google Maps é finalizada."""
        self.gmaps_search_btn.setEnabled(True)
        self.gmaps_progress.setVisible(False)

        if "ERRO" not in result:
            self.gmaps_status.setText("Busca concluída com sucesso!")
            self.gmaps_status.setStyleSheet(f"color: {SUCCESS_COLOR};")
            self.gmaps_export_excel.setEnabled(True)

            # Exibir mensagem de sucesso
            QMessageBox.information(
                self,
                "Busca Concluída",
                f"A busca foi concluída com sucesso!\n\n{result}"
            )
        else:
            self.gmaps_status.setText("Erro na busca. Verifique os parâmetros.")
            self.gmaps_status.setStyleSheet(f"color: {ERROR_COLOR};")

            # Exibir mensagem de erro
            QMessageBox.critical(
                self,
                "Erro na Busca",
                f"Ocorreu um erro durante a busca:\n\n{result}"
            )

    def export_gmaps_results(self):
        """Exporta os resultados da busca no Google Maps"""
        # Implementação futura
        QMessageBox.information(
            self,
            "Funcionalidade em Desenvolvimento",
            "A exportação de resultados da busca no Google Maps será implementada em breve."
        )

    def verificar_cancelamento(self):
        """Verifica se o usuário solicitou o cancelamento da operação"""
        if self.cancelar_captura:
            self.thread_scraper.sinal_cancelado.emit(True)
        else:
            self.thread_scraper.sinal_cancelado.emit(False)

    def cancelar_scraping(self):
        """Cancela a operação de scraping em andamento"""
        if hasattr(self, 'thread_scraper') and self.thread_scraper.isRunning():
            reply = QMessageBox.question(
                self, 'Confirmar Cancelamento',
                'Deseja realmente cancelar a captura em andamento?',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # Atualizar flags de cancelamento
                self.cancelar_captura = True
                self.thread_scraper.cancelado = True

                # Atualizar interface
                self.statusbar.showMessage("Cancelando captura...")
                self.adicionar_log("aviso", "Cancelando captura em andamento...")

                # Atualizar estado dos botões
                self.botao_cancelar.setEnabled(False)
                self.botao_iniciar.setEnabled(False)  # Desabilitar até que o cancelamento seja concluído

                # Mostrar mensagem de aguarde
                self.adicionar_log("info", "Aguarde enquanto a operação é finalizada...")

                # Verificar se há resultados parciais para habilitar exportação
                if len(self.clientes_extraidos) > 0:
                    self.botao_exportar.setEnabled(True)
                    self.adicionar_log("info", f"{len(self.clientes_extraidos)} leads foram capturados antes do cancelamento.")

                # Definir um timer para habilitar o botão iniciar após um tempo
                QTimer.singleShot(3000, lambda: self.botao_iniciar.setEnabled(True))

    def limpar_log(self):
        self.navegador_log.clear()
        self.adicionar_log("info", "Log limpo.")

    def adicionar_log(self, tipo, mensagem):
        timestamp = time.strftime("%H:%M:%S")

        if tipo == "info":
            cor = INFO_COLOR  # azul
            icone = "ℹ️"
        elif tipo == "erro":
            cor = ERROR_COLOR  # vermelho
            icone = "❌"
        elif tipo == "aviso":
            cor = WARNING_COLOR  # amarelo
            icone = "⚠️"
        elif tipo == "sucesso":
            cor = SUCCESS_COLOR  # verde
            icone = "✅"
        else:
            cor = TEXT_SECONDARY  # cinza
            icone = "•"

        log_html = f"""
        <div style="margin-bottom: 2px;">
            <span style="color: {TEXT_SECONDARY};">[{timestamp}]</span>
            <span style="color: {cor};">{icone} {mensagem}</span>
        </div>
        """
        self.navegador_log.append(log_html)
        # Rolar para o final
        self.navegador_log.verticalScrollBar().setValue(
            self.navegador_log.verticalScrollBar().maximum()
        )

    def iniciar_scraping(self):
        # Verificar se já existe uma captura em andamento
        if hasattr(self, 'thread_scraper') and self.thread_scraper.isRunning():
            msg_box = QMessageBox(self)
            msg_box.setWindowIcon(QIcon(resource_path('prospecto.png')))
            msg_box.setIcon(QMessageBox.Icon.Warning)
            msg_box.setWindowTitle('Captura em Andamento')
            msg_box.setText('Já existe uma captura em andamento. Por favor, aguarde ou cancele a operação atual.')
            msg_box.exec()
            return

        # Obter valores dos campos
        cep = self.entrada_cep.text().strip()
        palavra_chave = self.entrada_palavra_chave.text().strip()

        # Validação do CEP
        if not cep:
            msg_box = QMessageBox(self)
            msg_box.setWindowIcon(QIcon(resource_path('prospecto.png')))
            msg_box.setIcon(QMessageBox.Icon.Warning)
            msg_box.setWindowTitle('Campo Obrigatório')
            msg_box.setText('Por favor, insira um CEP válido')
            msg_box.exec()
            self.entrada_cep.setFocus()
            return

        # Validar formato do CEP (deve ter 8 ou 9 dígitos, incluindo o hífen)
        cep_limpo = cep.replace('-', '')
        if not cep_limpo.isdigit() or len(cep_limpo) != 8:
            msg_box = QMessageBox(self)
            msg_box.setWindowIcon(QIcon(resource_path('prospecto.png')))
            msg_box.setIcon(QMessageBox.Icon.Warning)
            msg_box.setWindowTitle('CEP Inválido')
            msg_box.setText('Por favor, insira um CEP válido no formato 00000-000 ou 00000000')
            msg_box.exec()
            self.entrada_cep.setFocus()
            return

        # Validação da palavra-chave
        if not palavra_chave:
            msg_box = QMessageBox(self)
            msg_box.setWindowIcon(QIcon(resource_path('prospecto.png')))
            msg_box.setIcon(QMessageBox.Icon.Warning)
            msg_box.setWindowTitle('Campo Obrigatório')
            msg_box.setText('Por favor, insira uma palavra-chave para busca')
            msg_box.exec()
            self.entrada_palavra_chave.setFocus()
            return

        try:
            quantidade = int(self.entrada_quantidade.text())
            if quantidade <= 0:
                raise ValueError("Quantidade deve ser maior que zero")
        except ValueError:
            msg_box = QMessageBox(self)
            msg_box.setWindowIcon(QIcon(resource_path('prospecto.png')))
            msg_box.setIcon(QMessageBox.Icon.Warning)
            msg_box.setWindowTitle('Entrada Inválida')
            msg_box.setText('Por favor, insira um número válido de leads')
            msg_box.exec()
            return

        self.botao_iniciar.setEnabled(False)
        self.botao_cancelar.setEnabled(True)
        self.botao_exportar.setEnabled(False)
        self.navegador_resultados.clear()
        self.barra_progresso.setValue(0)
        self.barra_progresso.setMaximum(quantidade)
        self.statusbar.showMessage("Iniciando captura...")
        self.cancelar_captura = False

        self.adicionar_log("info", f"Iniciando captura com CEP: {cep}, Palavra-chave: {palavra_chave}, Leads: {quantidade}")

        # Obter o valor do modo headless
        headless_mode = self.headless_checkbox.isChecked()
        self.thread_scraper = ThreadScraper(cep, palavra_chave, quantidade, headless_mode)
        self.thread_scraper.sinal_finalizado.connect(self.scraping_finalizado)
        self.thread_scraper.sinal_erro.connect(self.erro_scraping)
        self.thread_scraper.sinal_progresso.connect(self.atualizar_progresso)
        self.thread_scraper.sinal_log.connect(self.adicionar_log)
        self.thread_scraper.sinal_verificar_cancelamento.connect(self.verificar_cancelamento)
        self.thread_scraper.start()

    def scraping_finalizado(self, clientes):
        # Atualizar a lista de clientes extraídos
        self.clientes_extraidos = clientes

        # Atualizar estado dos botões
        self.botao_iniciar.setEnabled(True)
        self.botao_cancelar.setEnabled(False)
        self.botao_exportar.setEnabled(len(clientes) > 0)

        # Atualizar interface
        self.statusbar.showMessage(f"Captura finalizada. {len(clientes)} leads encontrados.")
        self.adicionar_log("info", f"Captura finalizada com sucesso. Total de {len(clientes)} leads encontrados.")

        total_clientes = len(clientes)
        if total_clientes > 0:
            msg = f"Captura concluída! {total_clientes} leads encontrados."
            self.statusbar.showMessage(msg)
            self.adicionar_log("sucesso", msg)

            msg_box = QMessageBox(self)
            msg_box.setWindowIcon(QIcon(resource_path('prospecto.png')))
            msg_box.setIcon(QMessageBox.Icon.Information)
            msg_box.setWindowTitle('Captura Concluída')
            msg_box.setText(f'A captura foi concluída com sucesso! {total_clientes} leads encontrados.')
            msg_box.exec()
        else:
            msg = "Captura concluída sem resultados."
            self.statusbar.showMessage(msg)
            self.adicionar_log("aviso", msg)

            msg_box = QMessageBox(self)
            msg_box.setWindowIcon(QIcon(resource_path('prospecto.png')))
            msg_box.setIcon(QMessageBox.Icon.Information)
            msg_box.setWindowTitle('Sem Resultados')
            msg_box.setText('A captura foi concluída, mas nenhum lead foi encontrado.')
            msg_box.exec()

    def erro_scraping(self, erro):
        # Restaurar estado dos botões
        self.botao_iniciar.setEnabled(True)
        self.botao_cancelar.setEnabled(False)
        self.botao_exportar.setEnabled(len(self.clientes_extraidos) > 0)

        # Atualizar interface
        self.statusbar.showMessage("Erro na captura")
        self.adicionar_log("erro", f"Erro: {erro}")

        # Exibir mensagem de erro para o usuário
        erro_resumido = erro.split('\n')[0] if '\n' in erro else erro
        msg_box = QMessageBox(self)
        msg_box.setWindowIcon(QIcon(resource_path('prospecto.png')))
        msg_box.setIcon(QMessageBox.Icon.Critical)
        msg_box.setWindowTitle('Erro na Captura')
        msg_box.setText(f'Ocorreu um erro durante a captura:\n\n{erro_resumido}')
        msg_box.exec()

    def atualizar_progresso(self, atual, total, cliente):
        self.barra_progresso.setValue(atual)
        porcentagem = int((atual / total) * 100)

        # Formata o resultado de forma mais estruturada e legível
        resultado_html = f"""
        <div style="margin-bottom: 10px; padding: 5px; border-left: 3px solid {SUCCESS_COLOR};">
            <b style="color: {TEXT_PRIMARY}; font-size: 14px;">{cliente['nome']}</b><br>
            <span style="color: {TEXT_SECONDARY};">Telefone: {cliente['telefone']}</span>
            {f'<br><span style="color: {TEXT_SECONDARY};">Endereço: {cliente["endereco"]}</span>' if cliente.get('endereco') != "Endereço não disponível" else ''}
            {f'<br><span style="color: {TEXT_SECONDARY};">Site: <a href="{cliente["site"]}">{cliente["site"]}</a></span>' if cliente.get('site') != "Site não disponível" else ''}
        </div>
        """
        self.navegador_resultados.append(resultado_html)
        self.statusbar.showMessage(f"Capturando leads: {atual} de {total} ({porcentagem}%)")

    def exportar_para_xlsx(self):
        if not self.clientes_extraidos:
            msg_box = QMessageBox(self)
            msg_box.setWindowIcon(QIcon(resource_path('prospecto.png')))
            msg_box.setIcon(QMessageBox.Icon.Warning)
            msg_box.setWindowTitle('Sem Dados')
            msg_box.setText('Não há dados para exportar.')
            msg_box.exec()
            return

        file_dialog = QFileDialog(self)
        file_dialog.setWindowIcon(QIcon(resource_path('prospecto.png')))
        file_dialog.setWindowTitle("Salvar Arquivo")
        file_dialog.setNameFilter("Arquivos Excel (*.xlsx);;Todos os Arquivos (*)")
        file_dialog.setAcceptMode(QFileDialog.AcceptMode.AcceptSave)
        file_dialog.setDefaultSuffix("xlsx")

        if file_dialog.exec():
            nome_arquivo = file_dialog.selectedFiles()[0]

            if nome_arquivo:
                if not nome_arquivo.endswith('.xlsx'):
                    nome_arquivo += '.xlsx'

                try:
                    logic_bot.salvar_clientes(self.clientes_extraidos, nome_arquivo)
                    self.statusbar.showMessage(f"Dados exportados para {nome_arquivo}")
                    self.adicionar_log("sucesso", f"Dados exportados para {nome_arquivo}")

                    msg_box = QMessageBox(self)
                    msg_box.setWindowIcon(QIcon(resource_path('prospecto.png')))
                    msg_box.setIcon(QMessageBox.Icon.Information)
                    msg_box.setWindowTitle('Exportação Concluída')
                    msg_box.setText(f'Dados exportados com sucesso para:\n{nome_arquivo}')
                    msg_box.exec()
                except Exception as e:
                    self.statusbar.showMessage(f"Erro ao exportar: {str(e)}")
                    self.adicionar_log("erro", f"Erro ao exportar: {str(e)}")

                    msg_box = QMessageBox(self)
                    msg_box.setWindowIcon(QIcon(resource_path('prospecto.png')))
                    msg_box.setIcon(QMessageBox.Icon.Critical)
                    msg_box.setWindowTitle('Erro na Exportação')
                    msg_box.setText(f'Ocorreu um erro ao exportar os dados:\n\n{str(e)}')
                    msg_box.exec()


def main():
    app = QApplication(sys.argv)

    # Definir fonte global
    app.setFont(QFont('Segoe UI', 10))

    # Aplicar tema global
    app.setStyle('Fusion')
    app.setPalette(get_application_palette())

    # Configurar ícone do aplicativo globalmente
    app_icon = QIcon(resource_path('prospecto.png'))
    app.setWindowIcon(app_icon)

    # Mostrar splash screen e iniciar aplicativo após o splash
    show_splash(app, 2000)  # 2 segundos

    # Iniciar aplicativo principal
    ex = ProspectoApp()

    # Mostrar a janela principal após um pequeno atraso
    QTimer.singleShot(2100, ex.show)

    sys.exit(app.exec())


if __name__ == '__main__':
    main()
