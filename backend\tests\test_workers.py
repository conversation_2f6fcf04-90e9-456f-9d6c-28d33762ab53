"""
Testes para Workers
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from app.workers.base_worker import BaseSeleniumWorker, safe_find_element
from app.workers.flexible_worker import FlexibleSearchWorker
from app.models.schemas import JobStatus


@pytest.mark.unit
class TestBaseSeleniumWorker:
    """Testes para BaseSeleniumWorker"""

    def test_init(self):
        """Testa inicialização do worker"""
        worker = BaseSeleniumWorker("test-job-123", headless=True)

        assert worker.job_id == "test-job-123"
        assert worker.headless is True
        assert worker.driver is None
        assert worker.cancelled is False
        assert worker.paused is False

    @patch('app.workers.base_worker.webdriver.Chrome')
    @patch('app.workers.base_worker.ChromeDriverManager')
    def test_setup_driver_success(self, mock_driver_manager, mock_chrome):
        """Testa configuração bem-sucedida do driver"""
        mock_driver_manager.return_value.install.return_value = "/path/to/chromedriver"
        mock_driver = Mock()
        mock_chrome.return_value = mock_driver

        worker = BaseSeleniumWorker("test-job-123")
        result = worker.setup_driver()

        assert result is True
        assert worker.driver == mock_driver
        mock_chrome.assert_called_once()

    @patch('app.workers.base_worker.webdriver.Chrome')
    @patch('app.workers.base_worker.ChromeDriverManager')
    def test_setup_driver_failure(self, mock_driver_manager, mock_chrome):
        """Testa falha na configuração do driver"""
        mock_chrome.side_effect = Exception("Driver error")

        worker = BaseSeleniumWorker("test-job-123")
        result = worker.setup_driver()

        assert result is False
        assert worker.driver is None

    def test_cleanup_driver_with_driver(self):
        """Testa limpeza do driver quando existe"""
        worker = BaseSeleniumWorker("test-job-123")
        mock_driver = Mock()
        worker.driver = mock_driver

        worker.cleanup_driver()

        mock_driver.quit.assert_called_once()
        assert worker.driver is None

    def test_cleanup_driver_without_driver(self):
        """Testa limpeza do driver quando não existe"""
        worker = BaseSeleniumWorker("test-job-123")

        # Não deve gerar erro
        worker.cleanup_driver()

        assert worker.driver is None

    @patch('app.workers.base_worker.job_manager')
    def test_set_status(self, mock_job_manager):
        """Testa definição de status"""
        mock_job_manager.update_job_status.return_value = True
        worker = BaseSeleniumWorker("test-job-123")

        worker.set_status(JobStatus.RUNNING)

        mock_job_manager.update_job_status.assert_called_once_with("test-job-123", JobStatus.RUNNING)

    @patch('app.workers.base_worker.job_manager')
    def test_update_progress(self, mock_job_manager):
        """Testa atualização de progresso"""
        mock_job_manager.update_job_progress.return_value = True
        worker = BaseSeleniumWorker("test-job-123")

        worker.update_progress(75)

        mock_job_manager.update_job_progress.assert_called_once_with("test-job-123", 75)

    @patch('app.workers.base_worker.job_manager')
    def test_set_result_file(self, mock_job_manager):
        """Testa definição de arquivo de resultado"""
        mock_job_manager.set_job_result_file.return_value = True
        worker = BaseSeleniumWorker("test-job-123")

        worker.set_result_file("/path/to/result.xlsx")

        mock_job_manager.set_job_result_file.assert_called_once_with("test-job-123", "/path/to/result.xlsx")

    @patch('app.workers.base_worker.job_manager')
    def test_log_info(self, mock_job_manager):
        """Testa log de informação"""
        mock_job_manager.add_log.return_value = True
        worker = BaseSeleniumWorker("test-job-123")

        worker.log_info("Test message")

        mock_job_manager.add_log.assert_called_once()
        args = mock_job_manager.add_log.call_args[0]
        assert args[0] == "test-job-123"
        assert "INFO" in str(args[1])
        assert args[2] == "Test message"

    @patch('app.workers.base_worker.job_manager')
    def test_log_error(self, mock_job_manager):
        """Testa log de erro"""
        mock_job_manager.add_log.return_value = True
        worker = BaseSeleniumWorker("test-job-123")

        worker.log_error("Error message")

        mock_job_manager.add_log.assert_called_once()
        args = mock_job_manager.add_log.call_args[0]
        assert args[0] == "test-job-123"
        assert "ERROR" in str(args[1])
        assert args[2] == "Error message"

    @patch('app.workers.base_worker.job_manager')
    def test_check_cancellation_not_cancelled(self, mock_job_manager):
        """Testa verificação de cancelamento quando não cancelado"""
        mock_job = Mock()
        mock_job.status = JobStatus.RUNNING
        mock_job.logs = []
        mock_job_manager.get_job.return_value = mock_job

        worker = BaseSeleniumWorker("test-job-123")
        result = worker.check_cancellation()

        assert result is False
        assert worker.cancelled is False

    @patch('app.workers.base_worker.job_manager')
    def test_check_cancellation_cancelled(self, mock_job_manager):
        """Testa verificação de cancelamento quando cancelado"""
        mock_job = Mock()
        mock_job.status = JobStatus.CANCELLED
        mock_job.logs = []
        mock_job_manager.get_job.return_value = mock_job

        worker = BaseSeleniumWorker("test-job-123")
        result = worker.check_cancellation()

        assert result is True
        assert worker.cancelled is True

    @patch('app.workers.base_worker.job_manager')
    def test_wait_if_paused(self, mock_job_manager):
        """Testa espera quando pausado"""
        mock_job = Mock()
        mock_job.logs = [
            {"message": "PAUSE_REQUESTED"},
            {"message": "RESUME_REQUESTED"}
        ]
        mock_job_manager.get_job.return_value = mock_job

        worker = BaseSeleniumWorker("test-job-123")

        # Simular que foi pausado e depois retomado
        with patch('time.sleep') as mock_sleep:
            worker.wait_if_paused()
            # Deve ter dormido pelo menos uma vez
            assert mock_sleep.call_count >= 0

    def test_handle_error(self):
        """Testa tratamento de erro"""
        worker = BaseSeleniumWorker("test-job-123")

        with patch.object(worker, 'log_error') as mock_log_error, \
             patch('app.workers.base_worker.job_manager.set_job_error') as mock_set_error:

            error = Exception("Test error")
            worker.handle_error(error)

            mock_log_error.assert_called_once_with("Erro durante execução: Test error")
            mock_set_error.assert_called_once_with("test-job-123", "Erro durante execução: Test error")


@pytest.mark.unit
class TestSafeFindElement:
    """Testes para função safe_find_element"""

    def test_safe_find_element_success(self):
        """Testa busca bem-sucedida de elemento"""
        mock_driver = Mock()
        mock_element = Mock()
        mock_driver.find_element.return_value = mock_element

        result = safe_find_element(mock_driver, "id", "test-id")

        assert result == mock_element
        mock_driver.find_element.assert_called_once()

    def test_safe_find_element_not_found(self):
        """Testa busca de elemento não encontrado"""
        mock_driver = Mock()
        mock_driver.find_element.side_effect = Exception("Element not found")

        result = safe_find_element(mock_driver, "id", "test-id")

        assert result is None

    def test_safe_find_element_with_timeout(self):
        """Testa busca com timeout"""
        mock_driver = Mock()
        mock_element = Mock()

        with patch('selenium.webdriver.support.ui.WebDriverWait') as mock_wait:
            mock_wait.return_value.until.return_value = mock_element

            result = safe_find_element(mock_driver, "id", "test-id", timeout=10)

            assert result == mock_element
            mock_wait.assert_called_once()


@pytest.mark.worker
class TestFlexibleSearchWorker:
    """Testes para FlexibleSearchWorker"""

    def test_init(self):
        """Testa inicialização do FlexibleSearchWorker"""
        worker = FlexibleSearchWorker(
            job_id="test-job-123",
            search_type="city",
            location_query="São Paulo",
            business_type="restaurantes",
            quantidade=50,
            radius_km=10,
            headless=True
        )

        assert worker.job_id == "test-job-123"
        assert worker.search_type == "city"
        assert worker.location_query == "São Paulo"
        assert worker.business_type == "restaurantes"
        assert worker.quantidade == 50
        assert worker.radius_km == 10
        assert worker.leads_capturados == []

    @patch('app.workers.flexible_worker.export_leads_to_file')
    def test_save_results_success(self, mock_export):
        """Testa salvamento bem-sucedido de resultados"""
        mock_export.return_value = "/path/to/result.xlsx"

        worker = FlexibleSearchWorker(
            job_id="test-job-123",
            search_type="city",
            location_query="test",
            business_type="test",
            quantidade=10
        )
        worker.leads_capturados = [{"nome": "Test Lead"}]

        with patch.object(worker, 'set_result_file') as mock_set_result:
            worker.save_results()

            mock_export.assert_called_once()
            mock_set_result.assert_called_once_with("/path/to/result.xlsx")

    def test_lead_ja_capturado_true(self):
        """Testa detecção de lead já capturado"""
        worker = FlexibleSearchWorker(
            job_id="test-job-123",
            search_type="city",
            location_query="test",
            business_type="test",
            quantidade=10
        )

        existing_lead = {"nome": "Test Restaurant", "endereco": "Test Address"}
        worker.leads_capturados = [existing_lead]

        new_lead = {"nome": "Test Restaurant", "endereco": "Test Address"}
        result = worker.lead_ja_capturado(new_lead)

        assert result is True

    def test_lead_ja_capturado_false(self):
        """Testa detecção de lead não capturado"""
        worker = FlexibleSearchWorker(
            job_id="test-job-123",
            search_type="city",
            location_query="test",
            business_type="test",
            quantidade=10
        )

        existing_lead = {"nome": "Test Restaurant 1", "endereco": "Test Address 1"}
        worker.leads_capturados = [existing_lead]

        new_lead = {"nome": "Test Restaurant 2", "endereco": "Test Address 2"}
        result = worker.lead_ja_capturado(new_lead)

        assert result is False

    def test_lead_ja_capturado_invalid_lead(self):
        """Testa detecção com lead inválido"""
        worker = FlexibleSearchWorker(
            job_id="test-job-123",
            search_type="city",
            location_query="test",
            business_type="test",
            quantidade=10
        )

        # Lead sem nome deve ser considerado já capturado (inválido)
        invalid_lead = {"endereco": "Test Address"}
        result = worker.lead_ja_capturado(invalid_lead)

        assert result is True
