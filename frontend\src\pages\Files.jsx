import React, { useState, useEffect } from 'react'
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Grid,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Tooltip,
  Menu,
  MenuItem,
} from '@mui/material'
import {
  Download as DownloadIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Upload as UploadIcon,
  Folder as FolderIcon,
  InsertDriveFile as FileIcon,
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
  CloudUpload as CloudUploadIcon,
} from '@mui/icons-material'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useSnackbar } from 'notistack'
import { filesApi } from '../services/api'
import FileUpload from '../components/FileUpload'

function Files() {
  const { enqueueSnackbar } = useSnackbar()
  const queryClient = useQueryClient()
  const [currentTab, setCurrentTab] = useState(0)
  const [searchTerm, setSearchTerm] = useState('')
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false)
  const [selectedFile, setSelectedFile] = useState(null)
  const [menuAnchor, setMenuAnchor] = useState(null)

  // Query para listar arquivos
  const { data: files = [], isLoading, refetch } = useQuery(
    ['files', currentTab],
    () => {
      const type = currentTab === 0 ? 'exports' : 'uploads'
      return filesApi.list(type)
    },
    {
      refetchInterval: 30000, // Atualizar a cada 30 segundos
    }
  )

  // Mutation para deletar arquivo
  const deleteMutation = useMutation(
    (filename) => filesApi.delete(filename),
    {
      onSuccess: () => {
        enqueueSnackbar('Arquivo deletado com sucesso!', { variant: 'success' })
        queryClient.invalidateQueries(['files'])
      },
      onError: (error) => {
        enqueueSnackbar(
          error.response?.data?.detail || 'Erro ao deletar arquivo',
          { variant: 'error' }
        )
      }
    }
  )

  const handleDownload = (filename) => {
    const type = currentTab === 0 ? 'exports' : 'uploads'
    const url = `${import.meta.env.VITE_API_URL}/${type}/${filename}`
    window.open(url, '_blank')
  }

  const handleDelete = (filename) => {
    if (window.confirm(`Tem certeza que deseja deletar o arquivo "${filename}"?`)) {
      deleteMutation.mutate(filename)
    }
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (timestamp) => {
    return new Date(timestamp * 1000).toLocaleString('pt-BR')
  }

  const getFileIcon = (filename) => {
    const extension = filename.split('.').pop().toLowerCase()
    if (['xlsx', 'xls'].includes(extension)) {
      return <FileIcon sx={{ color: '#1f7a1f' }} />
    } else if (extension === 'csv') {
      return <FileIcon sx={{ color: '#1976d2' }} />
    }
    return <FileIcon />
  }

  const filteredFiles = files.filter(file =>
    file.filename.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleMenuOpen = (event, file) => {
    setMenuAnchor(event.currentTarget)
    setSelectedFile(file)
  }

  const handleMenuClose = () => {
    setMenuAnchor(null)
    setSelectedFile(null)
  }

  return (
    <Box>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Gerenciamento de Arquivos
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Gerencie uploads, downloads e arquivos exportados
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Controles */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
                  <Tab
                    icon={<FolderIcon />}
                    label="Arquivos Exportados"
                    iconPosition="start"
                  />
                  <Tab
                    icon={<CloudUploadIcon />}
                    label="Arquivos Enviados"
                    iconPosition="start"
                  />
                </Tabs>

                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="outlined"
                    startIcon={<UploadIcon />}
                    onClick={() => setUploadDialogOpen(true)}
                  >
                    Upload
                  </Button>
                  <IconButton onClick={refetch} disabled={isLoading}>
                    <RefreshIcon />
                  </IconButton>
                </Box>
              </Box>

              <TextField
                fullWidth
                placeholder="Buscar arquivos..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                sx={{ maxWidth: 400 }}
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Lista de Arquivos */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              {isLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                  <CircularProgress />
                </Box>
              ) : filteredFiles.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <FolderIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary">
                    {searchTerm ? 'Nenhum arquivo encontrado' : 'Nenhum arquivo disponível'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {currentTab === 0
                      ? 'Execute uma busca para gerar arquivos exportados'
                      : 'Faça upload de arquivos de contatos'
                    }
                  </Typography>
                </Box>
              ) : (
                <TableContainer component={Paper} variant="outlined">
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Arquivo</TableCell>
                        <TableCell>Tamanho</TableCell>
                        <TableCell>Criado em</TableCell>
                        <TableCell>Modificado em</TableCell>
                        <TableCell align="right">Ações</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {filteredFiles.map((file) => (
                        <TableRow key={file.filename} hover>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {getFileIcon(file.filename)}
                              <Box>
                                <Typography variant="body2" fontWeight="medium">
                                  {file.filename}
                                </Typography>
                                <Chip
                                  label={file.filename.split('.').pop().toUpperCase()}
                                  size="small"
                                  variant="outlined"
                                  sx={{ mt: 0.5 }}
                                />
                              </Box>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {formatFileSize(file.size)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {formatDate(file.created_at)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {formatDate(file.modified_at)}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                              <Tooltip title="Download">
                                <IconButton
                                  size="small"
                                  onClick={() => handleDownload(file.filename)}
                                  color="primary"
                                >
                                  <DownloadIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Mais opções">
                                <IconButton
                                  size="small"
                                  onClick={(e) => handleMenuOpen(e, file)}
                                >
                                  <MoreVertIcon />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Informações */}
        <Grid item xs={12}>
          <Alert severity="info">
            <Typography variant="body2" fontWeight="bold" gutterBottom>
              Sobre o Gerenciamento de Arquivos:
            </Typography>
            <ul style={{ margin: 0, paddingLeft: 20 }}>
              <li><strong>Arquivos Exportados:</strong> Resultados das buscas em formato Excel ou CSV</li>
              <li><strong>Arquivos Enviados:</strong> Listas de contatos para envio de mensagens</li>
              <li><strong>Download:</strong> Clique no ícone de download para baixar qualquer arquivo</li>
              <li><strong>Exclusão:</strong> Arquivos podem ser deletados permanentemente</li>
              <li><strong>Formatos suportados:</strong> Excel (.xlsx), CSV (.csv)</li>
            </ul>
          </Alert>
        </Grid>
      </Grid>

      {/* Menu de contexto */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => {
          handleDownload(selectedFile?.filename)
          handleMenuClose()
        }}>
          <DownloadIcon sx={{ mr: 1 }} />
          Download
        </MenuItem>
        <MenuItem
          onClick={() => {
            handleDelete(selectedFile?.filename)
            handleMenuClose()
          }}
          sx={{ color: 'error.main' }}
        >
          <DeleteIcon sx={{ mr: 1 }} />
          Deletar
        </MenuItem>
      </Menu>

      {/* Dialog de Upload */}
      <Dialog
        open={uploadDialogOpen}
        onClose={() => setUploadDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Upload de Arquivo</DialogTitle>
        <DialogContent>
          <FileUpload
            onUploadSuccess={() => {
              setUploadDialogOpen(false)
              queryClient.invalidateQueries(['files'])
              enqueueSnackbar('Arquivo enviado com sucesso!', { variant: 'success' })
            }}
            onUploadError={(error) => {
              enqueueSnackbar(error, { variant: 'error' })
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUploadDialogOpen(false)}>
            Cancelar
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default Files
