"""
Worker para busca por CEP
"""
import time
import re
from datetime import datetime
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from app.workers.base_worker import BaseSeleniumWorker, safe_find_element
from app.services.file_service import export_leads_to_file
from app.models.schemas import JobStatus


class CEPSearchWorker(BaseSeleniumWorker):
    """Worker para busca por CEP"""
    
    def __init__(self, job_id: str, cep: str, palavra_chave: str, quantidade: int, headless: bool = True):
        super().__init__(job_id, headless)
        self.cep = cep
        self.palavra_chave = palavra_chave
        self.quantidade = quantidade
        self.leads_capturados = []
    
    def run(self):
        """Executa a busca por CEP"""
        try:
            self.set_status(JobStatus.RUNNING)
            self.log_info(f"Iniciando busca por CEP: {self.cep}, palavra-chave: {self.palavra_chave}")
            
            # Configurar driver
            if not self.setup_driver():
                return
            
            # Abrir Google Maps
            self.log_info("Abrindo Google Maps...")
            self.driver.get('https://www.google.com/maps/')
            time.sleep(3)
            
            if self.check_cancellation():
                return
            
            # Buscar por CEP
            if not self.buscar_cep():
                return
            
            if self.check_cancellation():
                return
            
            # Buscar por palavra-chave
            if not self.buscar_palavra_chave():
                return
            
            if self.check_cancellation():
                return
            
            # Extrair leads
            self.extrair_leads()
            
            # Salvar resultados
            if self.leads_capturados:
                self.salvar_resultados()
                self.set_status(JobStatus.COMPLETED)
                self.log_info(f"Busca concluída com sucesso! {len(self.leads_capturados)} leads capturados.")
            else:
                self.log_warning("Nenhum lead foi capturado.")
                self.set_status(JobStatus.COMPLETED)
            
        except Exception as e:
            self.handle_error(e)
        finally:
            self.cleanup_driver()
    
    def buscar_cep(self) -> bool:
        """Busca pelo CEP no Google Maps"""
        try:
            self.log_info(f"Buscando pelo CEP: {self.cep}")
            
            # Encontrar campo de busca
            search_box = safe_find_element(self.driver, By.XPATH, '//*[@id="searchboxinput"]', 20)
            if not search_box:
                self.log_error("Campo de busca não encontrado")
                return False
            
            search_box.clear()
            
            # Formatar CEP
            cep_formatado = re.sub(r'[^0-9]', '', self.cep)
            if len(cep_formatado) == 8:
                cep_formatado = f"{cep_formatado[:5]}-{cep_formatado[5:]}"
            
            search_box.send_keys(cep_formatado)
            search_box.send_keys(Keys.ENTER)
            
            self.log_info("Aguardando resultados do CEP...")
            time.sleep(5)
            
            # Verificar se o CEP foi encontrado
            try:
                WebDriverWait(self.driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, '//h1[contains(@class, "fontHeadlineLarge")]'))
                )
                self.log_info(f"CEP {self.cep} encontrado com sucesso")
                return True
            except:
                self.log_warning(f"Não foi possível confirmar se o CEP {self.cep} foi encontrado")
                return True  # Continuar mesmo assim
                
        except Exception as e:
            self.log_error(f"Erro ao buscar CEP: {str(e)}")
            return False
    
    def buscar_palavra_chave(self) -> bool:
        """Busca pela palavra-chave na região do CEP"""
        try:
            self.log_info(f"Buscando por: {self.palavra_chave}")
            
            # Encontrar campo de busca
            search_box = safe_find_element(self.driver, By.XPATH, '//*[@id="searchboxinput"]')
            if not search_box:
                self.log_error("Campo de busca não encontrado")
                return False
            
            # Limpar e inserir palavra-chave
            search_box.send_keys(Keys.CONTROL + "a")
            search_box.send_keys(Keys.DELETE)
            search_box.send_keys(self.palavra_chave)
            search_box.send_keys(Keys.ENTER)
            
            self.log_info("Aguardando resultados da busca...")
            time.sleep(5)
            
            # Verificar se há resultados
            try:
                resultados = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_all_elements_located((By.XPATH, '//a[@class="hfpxzc"]'))
                )
                self.log_info(f"Encontrados {len(resultados)} resultados iniciais")
                return True
            except:
                self.log_error(f"Não foram encontrados resultados para '{self.palavra_chave}'")
                return False
                
        except Exception as e:
            self.log_error(f"Erro ao buscar palavra-chave: {str(e)}")
            return False
    
    def extrair_leads(self):
        """Extrai os leads dos resultados"""
        try:
            self.log_info(f"Iniciando extração de {self.quantidade} leads...")
            
            contador = 0
            tentativas_sem_novos = 0
            MAX_TENTATIVAS_SEM_NOVOS = 10
            
            while contador < self.quantidade and tentativas_sem_novos < MAX_TENTATIVAS_SEM_NOVOS:
                if self.check_cancellation():
                    break
                
                self.wait_if_paused()
                
                # Coletar elementos de resultados
                try:
                    elementos = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_all_elements_located((By.XPATH, '//a[@class="hfpxzc"]'))
                    )
                except:
                    self.log_warning("Não foi possível encontrar elementos de resultado")
                    break
                
                leads_antes = len(self.leads_capturados)
                
                # Processar cada elemento
                for i, elemento in enumerate(elementos):
                    if contador >= self.quantidade:
                        break
                    
                    if self.check_cancellation():
                        break
                    
                    try:
                        # Clicar no elemento
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", elemento)
                        time.sleep(0.5)
                        elemento.click()
                        time.sleep(2)
                        
                        # Extrair dados
                        lead = self.extrair_dados_lead()
                        
                        if lead and not self.lead_ja_capturado(lead):
                            self.leads_capturados.append(lead)
                            contador += 1
                            
                            # Atualizar progresso
                            progress = int((contador / self.quantidade) * 100)
                            self.update_progress(progress)
                            
                            self.log_info(f"Lead {contador}/{self.quantidade}: {lead['nome']}")
                        
                    except Exception as e:
                        self.log_warning(f"Erro ao processar elemento {i}: {str(e)}")
                        continue
                
                # Verificar se capturou novos leads
                if len(self.leads_capturados) == leads_antes:
                    tentativas_sem_novos += 1
                    self.log_info(f"Nenhum novo lead capturado. Tentativa {tentativas_sem_novos}/{MAX_TENTATIVAS_SEM_NOVOS}")
                    
                    # Tentar rolar a página para carregar mais resultados
                    self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                    time.sleep(3)
                else:
                    tentativas_sem_novos = 0
            
            self.log_info(f"Extração finalizada. Total de leads capturados: {len(self.leads_capturados)}")
            
        except Exception as e:
            self.log_error(f"Erro durante extração de leads: {str(e)}")
    
    def extrair_dados_lead(self) -> dict:
        """Extrai dados de um lead específico"""
        try:
            lead = {
                'nome': '',
                'telefone': '',
                'endereco': '',
                'site': '',
                'rating': '',
                'reviews_count': '',
                'categoria': '',
                'horario_funcionamento': ''
            }
            
            # Nome do estabelecimento
            try:
                nome_element = self.driver.find_element(By.XPATH, '//h1[@class="DUwDvf lfPIob"]')
                lead['nome'] = nome_element.text.strip()
            except:
                pass
            
            # Telefone
            try:
                telefone_element = self.driver.find_element(By.XPATH, '//button[contains(@data-item-id, "phone")]//div[contains(@class, "fontBodyMedium")]')
                lead['telefone'] = telefone_element.text.strip()
            except:
                pass
            
            # Endereço
            try:
                endereco_element = self.driver.find_element(By.XPATH, '//button[contains(@data-item-id, "address")]//div[contains(@class, "fontBodyMedium")]')
                lead['endereco'] = endereco_element.text.strip()
            except:
                pass
            
            # Website
            try:
                site_element = self.driver.find_element(By.XPATH, '//a[contains(@data-item-id, "authority")]//div[contains(@class, "fontBodyMedium")]')
                lead['site'] = site_element.text.strip()
            except:
                pass
            
            # Rating e número de avaliações
            try:
                rating_element = self.driver.find_element(By.XPATH, '//div[@class="F7nice "]//span[@class="ceNzKf"]')
                lead['rating'] = rating_element.get_attribute('aria-label')
            except:
                pass
            
            return lead if lead['nome'] else None
            
        except Exception as e:
            self.log_warning(f"Erro ao extrair dados do lead: {str(e)}")
            return None
    
    def lead_ja_capturado(self, lead: dict) -> bool:
        """Verifica se o lead já foi capturado"""
        for lead_existente in self.leads_capturados:
            if lead_existente['nome'] == lead['nome']:
                return True
        return False
    
    def salvar_resultados(self):
        """Salva os resultados em arquivo Excel"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"leads_cep_{self.cep}_{timestamp}.xlsx"
            
            file_path = export_leads_to_file(self.leads_capturados, filename, "excel")
            self.set_result_file(file_path)
            
            self.log_info(f"Resultados salvos em: {filename}")
            
        except Exception as e:
            self.log_error(f"Erro ao salvar resultados: {str(e)}")


async def start_cep_search(job_id: str, cep: str, palavra_chave: str, quantidade: int, headless: bool = True):
    """Função para iniciar busca por CEP em background"""
    worker = CEPSearchWorker(job_id, cep, palavra_chave, quantidade, headless)
    worker.run()
