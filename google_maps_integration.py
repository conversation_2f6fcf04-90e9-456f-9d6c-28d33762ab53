"""
Integração do Google Maps para o PROSPECTO.
Sistema de captura de leads e automação de mensagens.
"""
from dataclasses import dataclass, asdict, field
import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import NoSuchElementException, StaleElementReferenceException
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.actions.wheel_input import ScrollOrigin
import time
import os
from datetime import datetime

@dataclass
class Business:
    """Representa um negócio extraído do Google Maps."""
    nome: str = "Nome não disponível"
    endereco: str = "Endereço não disponível"
    telefone: str = "Telefone não disponível"
    site: str = "Site não disponível"
    categoria: str = "Categoria não disponível"
    horario: str = "Horário não disponível"
    avaliacao: str = "Avaliação não disponível"
    total_avaliacoes: str = "Total de avaliações não disponível"
    latitude: str = "Latitude não disponível"
    longitude: str = "Longitude não disponível"
    email: str = "Email não disponível"
    preco: str = "Preço não disponível"
    descricao: str = "Descrição não disponível"
    fotos: list = field(default_factory=list)

@dataclass
class BusinessList:
    """Armazena uma lista de objetos Business e permite salvar os resultados em Excel ou CSV."""
    business_list: list[Business] = field(default_factory=list)

    def dataframe(self):
        """Transforma a lista de negócios em um DataFrame do pandas."""
        return pd.json_normalize((asdict(business) for business in self.business_list), sep="_")

    def save_to_excel(self, filename, save_dir):
        """Salva o DataFrame em um arquivo Excel."""
        if save_dir:
            full_path = f'{save_dir}/{filename}.xlsx'
            self.dataframe().to_excel(full_path, index=False)
            return f"Arquivo salvo em: {full_path}"
        return "Diretório não especificado"

    def save_to_csv(self, filename, save_dir):
        """Salva o DataFrame em um arquivo CSV."""
        if save_dir:
            full_path = f'{save_dir}/{filename}.csv'
            self.dataframe().to_csv(full_path, index=False, sep=';')
            return f"Arquivo salvo em: {full_path}"
        return "Diretório não especificado"

def move_map(navegador, direction='right'):
    """
    Move o mapa na direção especificada.
    direction: 'right', 'left', 'up', 'down'
    """
    # Primeiro clica no mapa para garantir que está focado
    map_element = navegador.find_element(By.CLASS_NAME, 'widget-scene')
    action = ActionChains(navegador)
    action.move_to_element(map_element).click().perform()
    time.sleep(1)

    # Define quantas vezes pressionar a tecla de seta
    presses = 10

    # Mapeia a direção para a tecla correspondente
    key_map = {
        'right': Keys.ARROW_RIGHT,
        'left': Keys.ARROW_LEFT,
        'up': Keys.ARROW_UP,
        'down': Keys.ARROW_DOWN
    }

    # Pressiona a tecla várias vezes para mover o mapa
    key = key_map.get(direction, Keys.ARROW_RIGHT)
    for _ in range(presses):
        action.send_keys(key).perform()
        time.sleep(0.1)

    # Aguarda um pouco para o mapa carregar
    time.sleep(3)

def main_query(search_for, total, location, save_dir, file_format, headless_mode=True, callback=None):
    """
    Executa a consulta no Google Maps e extrai os dados dos estabelecimentos.

    Parâmetros:
      search_for: termo de busca (ex.: "restaurante Rio de Janeiro")
      total: número de resultados desejados
      location: localização para refinar a consulta
      save_dir: diretório onde os resultados serão salvos
      file_format: "excel" ou "csv"
      headless_mode: se True, executa o navegador em modo headless (invisível)
      callback: função opcional para receber atualizações de status
    """
    # Configura o Chrome
    from selenium.webdriver.chrome.options import Options
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-gpu")  # Desativa aceleração por hardware
    chrome_options.add_argument("--enable-unsafe-swiftshader")  # Permite SwiftShader
    chrome_options.add_argument("--disable-software-rasterizer")  # Evita problemas com renderização

    # Adiciona modo headless se solicitado
    if headless_mode:
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    navegador = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)

    if callback:
        callback("[INFO] Abrindo Google Maps...")
    navegador.get("https://www.google.com.br/maps")
    time.sleep(3)

    # Insere a localização e executa a busca inicial
    if callback:
        callback(f"[INFO] Buscando localização: {location}...")
    navegador.find_element(By.XPATH, '//*[@id="searchboxinput"]').send_keys(location)
    time.sleep(2)
    navegador.find_element(By.XPATH, '//*[@id="searchbox-searchbutton"]').click()
    time.sleep(15)
    navegador.find_element(By.XPATH, '//*[@id="searchbox"]/div[2]/button').click()
    time.sleep(5)

    # Realiza a busca do termo combinado (negócio + localização)
    if callback:
        callback(f"[INFO] Buscando termo: {search_for}...")
    navegador.find_element(By.XPATH, '//*[@id="searchboxinput"]').send_keys(search_for)
    navegador.find_element(By.XPATH, '//*[@id="searchbox-searchbutton"]').click()
    time.sleep(10)

    business_list = BusinessList()
    i = 0
    move_count = 0
    max_moves = 4  # Número máximo de movimentos do mapa

    while i < total and move_count < max_moves:
        previously_counted = 0
        stuck_count = 0

        while True:
            list_elem = navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
            if not list_elem:
                if callback:
                    callback("[ERRO] Nenhum elemento encontrado na página. Verifique se a consulta está correta ou se há resultados para esta região.")
                return "[ERRO] Nenhum elemento encontrado na página. Verifique se a consulta está correta ou se há resultados para esta região."

            action = ActionChains(navegador)
            try:
                action.move_to_element(list_elem[-1]).perform()
            except Exception:
                list_elem = navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
                action.move_to_element(list_elem[-1]).perform()
            time.sleep(5)

            scroll_origin = ScrollOrigin.from_element(list_elem[-1])
            action.scroll_from_origin(scroll_origin, 0, 1200).perform()
            time.sleep(20)
            action.scroll_from_origin(scroll_origin, 0, 250).perform()

            current_count = len(list_elem)
            if current_count == previously_counted:
                stuck_count += 1
                if stuck_count >= 3:  # Se ficar preso 3 vezes, consideramos que chegamos ao limite
                    break
            else:
                stuck_count = 0
                previously_counted = current_count

        # Processa os elementos encontrados
        list_elem = navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
        for element in list_elem[i:]:
            if i >= total:
                break

            try:
                time.sleep(2)
                navegador.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                time.sleep(2)
                try:
                    element.click()
                except Exception as click_err:
                    error_msg = f"[ERRO] Falha ao clicar no elemento {i}: {str(click_err)}"
                    if callback:
                        callback(error_msg)
                    print(error_msg)
                    i += 1
                    continue
                time.sleep(6)

                # XPaths para extração dos dados
                name_xpath = '//*[@id="QA0Szd"]/div/div/div[1]/div[3]/div/div[1]/div/div/div[2]/div[2]/div/div[1]/div[1]/h1'
                address_xpath = '//button[@data-item-id="address"]//div[contains(@class, "fontBodyMedium")]'
                website_xpath = '//a[@data-item-id="authority"]//div[contains(@class, "fontBodyMedium")]'
                phone_number_xpath = '//button[contains(@data-item-id, "phone:tel:")]//div[contains(@class, "fontBodyMedium")]'

                # Extrai os dados
                business = Business()

                try:
                    business.nome = navegador.find_element(By.XPATH, name_xpath).text
                except NoSuchElementException:
                    business.nome = f"Nome não disponível {i}"

                try:
                    business.endereco = navegador.find_element(By.XPATH, address_xpath).text
                except NoSuchElementException:
                    business.endereco = "Endereço não disponível"

                try:
                    business.site = navegador.find_element(By.XPATH, website_xpath).text
                except NoSuchElementException:
                    business.site = "Site não disponível"

                try:
                    business.telefone = navegador.find_element(By.XPATH, phone_number_xpath).text
                except NoSuchElementException:
                    business.telefone = "Telefone não disponível"

                # Adiciona o negócio à lista
                business_list.business_list.append(business)
                time.sleep(3)
                i += 1

                if callback:
                    porcentagem = (i / total) * 100
                    callback(f"[PROGRESSO] Processando registro {i} de {total} ({porcentagem:.1f}%)")

            except StaleElementReferenceException:
                error_msg = f"[AVISO] Elemento {i} está desatualizado, tentando próximo registro..."
                if callback:
                    callback(error_msg)
                print(error_msg)
                i += 1
                continue

        # Se ainda não atingimos o total desejado, pergunta se quer continuar
        if i < total:
            if callback:
                msg = f"[INFO] Encontrados {i} de {total} registros desejados.\n[AÇÃO] Deseja continuar a busca movendo o mapa para encontrar mais resultados? (S/N)"
                response = callback(msg, require_response=True)
                if response and response.upper() == 'S':
                    # Move o mapa em diferentes direções a cada iteração
                    directions = ['right', 'down', 'left', 'up']
                    move_map(navegador, directions[move_count % len(directions)])
                    move_count += 1
                    time.sleep(5)  # Aguarda o mapa carregar
                else:
                    break
            else:
                break

    # Finaliza a extração e salva automaticamente os dados
    updated_string = search_for.replace(" ", "_")
    result = None
    if file_format.lower() == "excel":
        result = business_list.save_to_excel(f'maps_data_{updated_string}', save_dir)
    elif file_format.lower() == "csv":
        result = business_list.save_to_csv(f'maps_data_{updated_string}', save_dir)
    else:
        result = "Formato de arquivo inválido."

    if callback:
        callback(result)

    # Fecha o navegador
    navegador.quit()

    return result
