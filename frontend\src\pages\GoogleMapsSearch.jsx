import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Grid,
  Alert,
} from '@mui/material'
import { Search as SearchIcon, Cancel as CancelIcon } from '@mui/icons-material'
import { useForm } from 'react-hook-form'
import { useMutation } from 'react-query'
import { useSnackbar } from 'notistack'
import { gmapsSearchApi } from '../services/api'
import JobProgress from '../components/JobProgress/JobProgress'

function GoogleMapsSearch() {
  const [currentJob, setCurrentJob] = useState(null)
  const { enqueueSnackbar } = useSnackbar()
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm({
    defaultValues: {
      search_for: '',
      location: '',
      total: 50,
      file_format: 'excel',
      headless: true,
    },
  })

  const startSearchMutation = useMutation(
    (data) => gmapsSearchApi.start(data),
    {
      onSuccess: (response) => {
        setCurrentJob({
          job_id: response.job_id,
          status: 'running',
          progress: 0,
        })
        enqueueSnackbar('Busca no Google Maps iniciada!', { variant: 'success' })
      },
      onError: (error) => {
        enqueueSnackbar(`Erro: ${error.message}`, { variant: 'error' })
      },
    }
  )

  const cancelSearchMutation = useMutation(
    (jobId) => gmapsSearchApi.cancel(jobId),
    {
      onSuccess: () => {
        setCurrentJob(null)
        enqueueSnackbar('Busca cancelada!', { variant: 'info' })
      },
      onError: (error) => {
        enqueueSnackbar(`Erro: ${error.message}`, { variant: 'error' })
      },
    }
  )

  const onSubmit = (data) => {
    startSearchMutation.mutate(data)
  }

  const handleCancel = () => {
    if (currentJob) {
      cancelSearchMutation.mutate(currentJob.job_id)
    }
  }

  const handleJobComplete = (job) => {
    setCurrentJob(null)
    if (job.status === 'completed') {
      enqueueSnackbar('Busca concluída!', { variant: 'success' })
    }
  }

  return (
    <Box>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Google Maps Search
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Busca direta no Google Maps por termo e localização
        </Typography>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Configurar Busca
              </Typography>

              <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ mt: 2 }}>
                <TextField
                  fullWidth
                  label="Termo de Busca"
                  placeholder="Ex: Restaurantes, Farmácias, etc."
                  margin="normal"
                  {...register('search_for', {
                    required: 'Termo de busca é obrigatório',
                  })}
                  error={!!errors.search_for}
                  helperText={errors.search_for?.message}
                  disabled={!!currentJob}
                />

                <TextField
                  fullWidth
                  label="Localização"
                  placeholder="Ex: São Paulo, SP"
                  margin="normal"
                  {...register('location', {
                    required: 'Localização é obrigatória',
                  })}
                  error={!!errors.location}
                  helperText={errors.location?.message}
                  disabled={!!currentJob}
                />

                <TextField
                  fullWidth
                  label="Total de Resultados"
                  type="number"
                  margin="normal"
                  {...register('total', {
                    required: 'Total é obrigatório',
                    min: { value: 1, message: 'Mínimo 1' },
                    max: { value: 1000, message: 'Máximo 1000' },
                  })}
                  error={!!errors.total}
                  helperText={errors.total?.message}
                  disabled={!!currentJob}
                />

                <FormControl fullWidth margin="normal">
                  <InputLabel>Formato do Arquivo</InputLabel>
                  <Select
                    {...register('file_format')}
                    disabled={!!currentJob}
                    label="Formato do Arquivo"
                  >
                    <MenuItem value="excel">Excel (.xlsx)</MenuItem>
                    <MenuItem value="csv">CSV (.csv)</MenuItem>
                  </Select>
                </FormControl>

                <FormControlLabel
                  control={
                    <Checkbox
                      {...register('headless')}
                      disabled={!!currentJob}
                    />
                  }
                  label="Modo headless"
                  sx={{ mt: 2, mb: 2 }}
                />

                <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                  <Button
                    type="submit"
                    variant="contained"
                    startIcon={<SearchIcon />}
                    disabled={!!currentJob || startSearchMutation.isLoading}
                    fullWidth
                  >
                    {startSearchMutation.isLoading ? 'Iniciando...' : 'Iniciar Busca'}
                  </Button>

                  {currentJob && (
                    <Button
                      variant="outlined"
                      color="error"
                      startIcon={<CancelIcon />}
                      onClick={handleCancel}
                      disabled={cancelSearchMutation.isLoading}
                    >
                      Cancelar
                    </Button>
                  )}
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          {currentJob ? (
            <JobProgress
              jobId={currentJob.job_id}
              onComplete={handleJobComplete}
            />
          ) : (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Sobre esta funcionalidade
                </Typography>
                
                <Typography variant="body2" paragraph>
                  A busca no Google Maps permite encontrar estabelecimentos por termo e localização específica.
                </Typography>

                <Alert severity="info">
                  Esta busca é mais ampla que a busca por CEP e pode retornar resultados de uma área maior.
                </Alert>
              </CardContent>
            </Card>
          )}
        </Grid>
      </Grid>
    </Box>
  )
}

export default GoogleMapsSearch
