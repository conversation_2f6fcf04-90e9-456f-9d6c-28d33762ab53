"""
Sistema de mensagens para o PROSPECTO.
Sistema de captura de leads e automação de mensagens.
"""
import time
import urllib
import datetime
import threading
import os
import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.common.exceptions import (NoSuchElementException, InvalidArgumentException,
                                        UnexpectedAlertPresentException, TimeoutException)
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait
from PyQt6.QtCore import QThread, pyqtSignal

# Configurações padrão
DEFAULT_WAIT_TIME = 30  # Tempo máximo de espera em segundos
DEFAULT_INTERVAL = 10   # Intervalo entre mensagens em segundos

class MessageSender(QThread):
    """Thread para envio de mensagens via WhatsApp Web."""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_signal = pyqtSignal(str)
    message_sent = pyqtSignal(str)  # Sinal emitido quando uma mensagem é enviada

    def __init__(self, contacts, message_template, interval=DEFAULT_INTERVAL, headless_mode=False):
        """
        Inicializa o enviador de mensagens.

        Args:
            contacts (list): Lista de contatos (dicionários com 'nome' e 'telefone')
            message_template (str): Template da mensagem com placeholders
            interval (int): Intervalo entre mensagens em segundos
            headless_mode (bool): Se True, executa o navegador em modo headless
        """
        super().__init__()
        self.contacts = contacts
        self.message_template = message_template
        self.interval = interval
        self.headless_mode = headless_mode
        self.running = False
        self.paused = False
        self.navegador = None

    def run(self):
        """Executa o envio de mensagens."""
        self.running = True

        try:
            # Configurar o Chrome
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")

            if self.headless_mode:
                chrome_options.add_argument("--headless")
                chrome_options.add_argument("--window-size=1920,1080")
                chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            self.navegador = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)

            # Abrir WhatsApp Web
            self.status_updated.emit("Abrindo WhatsApp Web...")
            self.navegador.get("https://web.whatsapp.com/")

            # Aguardar login
            self.status_updated.emit("Aguardando login no WhatsApp Web. Escaneie o QR Code...")

            # Verificar se o usuário está logado
            try:
                WebDriverWait(self.navegador, 60).until(
                    EC.presence_of_element_located((By.XPATH, '//div[@contenteditable="true"][@data-tab="10"]'))
                )
                self.status_updated.emit("Login realizado com sucesso!")
            except TimeoutException:
                self.status_updated.emit("Tempo esgotado aguardando login. Por favor, tente novamente.")
                self.navegador.quit()
                self.finished_signal.emit("Falha no login do WhatsApp Web")
                self.running = False
                return

            # Enviar mensagens para cada contato
            total_contacts = len(self.contacts)
            for i, contact in enumerate(self.contacts):
                if not self.running:
                    break

                # Aguardar se estiver pausado
                while self.paused and self.running:
                    time.sleep(1)

                # Atualizar progresso
                progress = int(((i + 1) / total_contacts) * 100)
                self.progress_updated.emit(progress)

                # Obter informações do contato
                phone = contact.get('telefone', '').strip()
                name = contact.get('nome', 'Cliente').strip()

                if not phone:
                    self.status_updated.emit(f"Contato {i+1}/{total_contacts}: Telefone não fornecido, pulando...")
                    continue

                # Formatar número de telefone (remover caracteres não numéricos)
                phone = ''.join(filter(str.isdigit, phone))

                # Verificar se o número tem o formato correto
                if not phone or len(phone) < 10:
                    self.status_updated.emit(f"Contato {i+1}/{total_contacts}: Número inválido: {phone}, pulando...")
                    continue

                # Adicionar código do país se não estiver presente
                if not phone.startswith('55'):
                    phone = '55' + phone

                # Personalizar mensagem
                message = self.message_template.format(nome=name)

                # Codificar mensagem para URL
                encoded_message = urllib.parse.quote(message)

                # Criar URL do WhatsApp
                url = f"https://web.whatsapp.com/send?phone={phone}&text={encoded_message}"

                # Enviar mensagem
                self.status_updated.emit(f"Enviando mensagem para {name} ({phone})...")

                try:
                    # Abrir chat
                    self.navegador.get(url)

                    # Aguardar carregamento da página
                    try:
                        # Aguardar o campo de mensagem aparecer
                        WebDriverWait(self.navegador, DEFAULT_WAIT_TIME).until(
                            EC.presence_of_element_located((By.XPATH, '//div[@contenteditable="true"][@data-tab="10"]'))
                        )

                        # Aguardar um pouco para garantir que a página carregou completamente
                        time.sleep(3)

                        # Enviar mensagem pressionando Enter
                        send_button = self.navegador.find_element(By.XPATH, '//span[@data-icon="send"]')
                        send_button.click()

                        # Aguardar envio da mensagem
                        time.sleep(2)

                        self.status_updated.emit(f"✓ Mensagem enviada para {name} ({phone})")
                        self.message_sent.emit(phone)

                    except TimeoutException:
                        self.status_updated.emit(f"⚠️ Tempo esgotado ao tentar enviar mensagem para {phone}")
                    except Exception as e:
                        self.status_updated.emit(f"⚠️ Erro ao enviar mensagem para {phone}: {str(e)}")

                except Exception as e:
                    self.status_updated.emit(f"⚠️ Erro ao processar contato {phone}: {str(e)}")

                # Aguardar intervalo entre mensagens
                if i < total_contacts - 1:  # Não aguardar após o último contato
                    for j in range(self.interval, 0, -1):
                        if not self.running or self.paused:
                            break
                        self.status_updated.emit(f"Aguardando {j} segundos para o próximo envio...")
                        time.sleep(1)

            self.status_updated.emit("Envio de mensagens concluído!")
            self.finished_signal.emit("Envio de mensagens concluído com sucesso")

        except Exception as e:
            self.status_updated.emit(f"Erro: {str(e)}")
            self.finished_signal.emit(f"Erro durante o envio de mensagens: {str(e)}")
        finally:
            if self.navegador:
                self.navegador.quit()
            self.running = False

    def pause(self):
        """Pausa o envio de mensagens."""
        self.paused = True
        self.status_updated.emit("Envio de mensagens pausado.")

    def resume(self):
        """Retoma o envio de mensagens."""
        self.paused = False
        self.status_updated.emit("Envio de mensagens retomado.")

    def stop(self):
        """Para o envio de mensagens."""
        self.running = False
        self.status_updated.emit("Envio de mensagens interrompido.")

class MessageGenerator:
    """Classe para geração de variações de mensagens."""

    @staticmethod
    def generate_variation(example_message, temperature=0.7):
        """
        Gera uma variação da mensagem de exemplo.

        Args:
            example_message (str): Mensagem de exemplo
            temperature (float): Temperatura para geração (0.0 a 1.0)

        Returns:
            str: Variação da mensagem
        """
        # Implementação básica - em um ambiente real, usaria a API do OpenAI
        import random

        # Lista de saudações
        greetings = [
            "Olá", "Oi", "Bom dia", "Boa tarde", "Boa noite", "Prezado(a)", "Caro(a)"
        ]

        # Lista de introduções
        intros = [
            "tudo bem?", "como vai?", "espero que esteja bem.", "como está?", "espero encontrá-lo(a) bem."
        ]

        # Extrair o corpo da mensagem (remover saudação e introdução)
        parts = example_message.split(',', 2)
        if len(parts) >= 3:
            body = parts[2].strip()
        else:
            body = example_message

        # Gerar nova saudação e introdução
        new_greeting = random.choice(greetings)
        new_intro = random.choice(intros)

        # Montar nova mensagem
        new_message = f"{new_greeting}, {{nome}}, {new_intro} {body}"

        return new_message
