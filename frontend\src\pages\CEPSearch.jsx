import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  FormControlLabel,
  Checkbox,
  Grid,
  Alert,
  LinearProgress,
  Paper,
  List,
  ListItem,
  ListItemText,
  Chip,
} from '@mui/material'
import { Search as SearchIcon, Cancel as CancelIcon } from '@mui/icons-material'
import { useForm } from 'react-hook-form'
import { useMutation } from 'react-query'
import { useSnackbar } from 'notistack'
import { cepSearchApi } from '../services/api'
import JobProgress from '../components/JobProgress/JobProgress'

function CEPSearch() {
  const [currentJob, setCurrentJob] = useState(null)
  const { enqueueSnackbar } = useSnackbar()
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    defaultValues: {
      cep: '',
      palavra_chave: '',
      quantidade: 50,
      headless: true,
    },
  })

  // Mutation para iniciar busca
  const startSearchMutation = useMutation(
    (data) => cepSearchApi.start(data),
    {
      onSuccess: (response) => {
        setCurrentJob({
          job_id: response.job_id,
          status: 'running',
          progress: 0,
        })
        enqueueSnackbar('Busca por CEP iniciada com sucesso!', { variant: 'success' })
      },
      onError: (error) => {
        enqueueSnackbar(`Erro ao iniciar busca: ${error.message}`, { variant: 'error' })
      },
    }
  )

  // Mutation para cancelar busca
  const cancelSearchMutation = useMutation(
    (jobId) => cepSearchApi.cancel(jobId),
    {
      onSuccess: () => {
        setCurrentJob(null)
        enqueueSnackbar('Busca cancelada com sucesso!', { variant: 'info' })
      },
      onError: (error) => {
        enqueueSnackbar(`Erro ao cancelar busca: ${error.message}`, { variant: 'error' })
      },
    }
  )

  const onSubmit = (data) => {
    // Validar CEP
    const cepClean = data.cep.replace(/\D/g, '')
    if (cepClean.length !== 8) {
      enqueueSnackbar('CEP deve ter 8 dígitos', { variant: 'error' })
      return
    }

    startSearchMutation.mutate({
      ...data,
      cep: data.cep,
    })
  }

  const handleCancel = () => {
    if (currentJob) {
      cancelSearchMutation.mutate(currentJob.job_id)
    }
  }

  const handleJobComplete = (job) => {
    setCurrentJob(null)
    if (job.status === 'completed') {
      enqueueSnackbar('Busca concluída com sucesso!', { variant: 'success' })
    } else if (job.status === 'failed') {
      enqueueSnackbar('Busca falhou. Verifique os logs.', { variant: 'error' })
    }
  }

  const formatCEP = (value) => {
    const numbers = value.replace(/\D/g, '')
    if (numbers.length <= 5) {
      return numbers
    }
    return `${numbers.slice(0, 5)}-${numbers.slice(5, 8)}`
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Busca por CEP
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Capture leads do Google Maps por CEP e palavra-chave
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Formulário */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Configurar Busca
              </Typography>

              <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ mt: 2 }}>
                <TextField
                  fullWidth
                  label="CEP"
                  placeholder="00000-000"
                  margin="normal"
                  {...register('cep', {
                    required: 'CEP é obrigatório',
                    pattern: {
                      value: /^\d{5}-?\d{3}$/,
                      message: 'CEP deve ter formato 00000-000',
                    },
                  })}
                  onChange={(e) => {
                    e.target.value = formatCEP(e.target.value)
                  }}
                  error={!!errors.cep}
                  helperText={errors.cep?.message}
                  disabled={!!currentJob}
                />

                <TextField
                  fullWidth
                  label="Palavra-chave"
                  placeholder="Ex: Restaurantes, Farmácias, etc."
                  margin="normal"
                  {...register('palavra_chave', {
                    required: 'Palavra-chave é obrigatória',
                    minLength: {
                      value: 2,
                      message: 'Palavra-chave deve ter pelo menos 2 caracteres',
                    },
                  })}
                  error={!!errors.palavra_chave}
                  helperText={errors.palavra_chave?.message}
                  disabled={!!currentJob}
                />

                <TextField
                  fullWidth
                  label="Quantidade de Leads"
                  type="number"
                  margin="normal"
                  {...register('quantidade', {
                    required: 'Quantidade é obrigatória',
                    min: {
                      value: 1,
                      message: 'Quantidade deve ser pelo menos 1',
                    },
                    max: {
                      value: 1000,
                      message: 'Quantidade máxima é 1000',
                    },
                  })}
                  error={!!errors.quantidade}
                  helperText={errors.quantidade?.message}
                  disabled={!!currentJob}
                />

                <FormControlLabel
                  control={
                    <Checkbox
                      {...register('headless')}
                      disabled={!!currentJob}
                    />
                  }
                  label="Modo headless (navegador invisível)"
                  sx={{ mt: 2, mb: 2 }}
                />

                <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                  <Button
                    type="submit"
                    variant="contained"
                    startIcon={<SearchIcon />}
                    disabled={!!currentJob || startSearchMutation.isLoading}
                    fullWidth
                  >
                    {startSearchMutation.isLoading ? 'Iniciando...' : 'Iniciar Busca'}
                  </Button>

                  {currentJob && (
                    <Button
                      variant="outlined"
                      color="error"
                      startIcon={<CancelIcon />}
                      onClick={handleCancel}
                      disabled={cancelSearchMutation.isLoading}
                    >
                      Cancelar
                    </Button>
                  )}
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Status e Progresso */}
        <Grid item xs={12} md={6}>
          {currentJob ? (
            <JobProgress
              jobId={currentJob.job_id}
              onComplete={handleJobComplete}
            />
          ) : (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Como funciona
                </Typography>
                
                <List>
                  <ListItem>
                    <ListItemText
                      primary="1. Digite o CEP"
                      secondary="Informe o CEP da região onde deseja buscar leads"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="2. Palavra-chave"
                      secondary="Digite o tipo de negócio que procura (ex: restaurantes)"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="3. Quantidade"
                      secondary="Defina quantos leads deseja capturar (máximo 1000)"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="4. Modo headless"
                      secondary="Navegador invisível para melhor performance"
                    />
                  </ListItem>
                </List>

                <Alert severity="info" sx={{ mt: 2 }}>
                  Os leads capturados incluem nome, telefone, endereço, site e outras informações disponíveis.
                </Alert>
              </CardContent>
            </Card>
          )}
        </Grid>
      </Grid>
    </Box>
  )
}

export default CEPSearch
