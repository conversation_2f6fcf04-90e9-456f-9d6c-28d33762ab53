# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    # filevers and prodvers should be always a tuple with four items: (1, 2, 3, 4)
    # Set not needed items to zero 0.
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    # Contains a bitmask that specifies the valid bits 'flags'r
    mask=0x3f,
    # Contains a bitmask that specifies the Boolean attributes of the file.
    flags=0x0,
    # The operating system for which this file was designed.
    # 0x4 - NT and there is no need to change it.
    OS=0x40004,
    # The general type of file.
    # 0x1 - the file is an application.
    fileType=0x1,
    # The function of the file.
    # 0x0 - the function is not defined for this fileType
    subtype=0x0,
    # Creation date and time stamp.
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Lead Capture Tools'),
        StringStruct(u'FileDescription', u'Ferramenta para captura de leads do Google Maps'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'LeadFinder'),
        StringStruct(u'LegalCopyright', u'Copyright © 2023-2024 Lead Capture Tools'),
        StringStruct(u'LegalTrademarks', u'Lead Capture Tools é uma marca registrada'),
        StringStruct(u'OriginalFilename', u'LeadFinder.exe'),
        StringStruct(u'ProductName', u'Lead Finder'),
        StringStruct(u'ProductVersion', u'*******'),
        StringStruct(u'Comments', u'Ferramenta para profissionais de marketing e vendas'),
        StringStruct(u'CompanyWebsite', u'https://www.leadcapturetools.com')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)