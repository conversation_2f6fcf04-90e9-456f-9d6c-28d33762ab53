import React from 'react'
import { Routes, Route } from 'react-router-dom'
import { Box } from '@mui/material'

import Layout from './components/Layout/Layout'
import Dashboard from './pages/Dashboard'
import FlexibleSearch from './pages/FlexibleSearch'
import CEPSearch from './pages/CEPSearch'
import GoogleMapsSearch from './pages/GoogleMapsSearch'
import AutomatedSearch from './pages/AutomatedSearch'
import Messaging from './pages/Messaging'
import Jobs from './pages/Jobs'
import Files from './pages/Files'

function App() {
  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/flexible-search" element={<FlexibleSearch />} />
          <Route path="/cep-search" element={<CEPSearch />} />
          <Route path="/gmaps-search" element={<GoogleMapsSearch />} />
          <Route path="/automated-search" element={<AutomatedSearch />} />
          <Route path="/messaging" element={<Messaging />} />
          <Route path="/jobs" element={<Jobs />} />
          <Route path="/files" element={<Files />} />
        </Routes>
      </Layout>
    </Box>
  )
}

export default App
