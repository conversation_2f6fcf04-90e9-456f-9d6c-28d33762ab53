"""
Testes para Services
"""
import pytest
from unittest.mock import Mock, patch, mock_open
import pandas as pd
from pathlib import Path

from app.services.file_service import export_leads_to_file, save_upload_file, list_export_files, delete_file
from app.services.message_generator import MessageGenerator


@pytest.mark.unit
class TestFileService:
    """Testes para serviço de arquivos"""

    @patch('app.services.file_service.pd.DataFrame.to_excel')
    @patch('app.services.file_service.os.makedirs')
    @patch('app.services.file_service.os.path.exists')
    def test_export_leads_to_excel_success(self, mock_exists, mock_makedirs, mock_to_excel, sample_leads_data):
        """Testa exportação bem-sucedida para Excel"""
        mock_exists.return_value = False  # Diretório não existe
        mock_to_excel.return_value = None

        result = export_leads_to_file(sample_leads_data, "test_export", "excel")

        assert result is not None
        assert result.endswith(".xlsx")
        assert "test_export" in result
        mock_makedirs.assert_called_once()
        mock_to_excel.assert_called_once()

    @patch('app.services.file_service.pd.DataFrame.to_csv')
    @patch('app.services.file_service.os.makedirs')
    @patch('app.services.file_service.os.path.exists')
    def test_export_leads_to_csv_success(self, mock_exists, mock_makedirs, mock_to_csv, sample_leads_data):
        """Testa exportação bem-sucedida para CSV"""
        mock_exists.return_value = True  # Diretório existe
        mock_to_csv.return_value = None

        result = export_leads_to_file(sample_leads_data, "test_export", "csv")

        assert result is not None
        assert result.endswith(".csv")
        assert "test_export" in result
        mock_makedirs.assert_not_called()  # Não deve criar diretório
        mock_to_csv.assert_called_once()

    def test_export_leads_empty_data(self):
        """Testa exportação com dados vazios"""
        result = export_leads_to_file([], "test_export", "excel")

        assert result is None

    def test_export_leads_invalid_format(self, sample_leads_data):
        """Testa exportação com formato inválido"""
        with pytest.raises(ValueError, match="Formato não suportado"):
            export_leads_to_file(sample_leads_data, "test_export", "invalid_format")

    @patch('app.services.file_service.aiofiles.open', new_callable=mock_open)
    @patch('app.services.file_service.os.makedirs')
    @patch('app.services.file_service.os.path.exists')
    async def test_save_uploaded_file_success(self, mock_exists, mock_makedirs, mock_file_open):
        """Testa salvamento bem-sucedido de arquivo enviado"""
        mock_exists.return_value = False

        # Mock do arquivo enviado
        mock_file = Mock()
        mock_file.filename = "test_upload.xlsx"
        mock_file.read = Mock(return_value=b"file content")

        result = await save_upload_file(mock_file)

        assert result is not None
        assert "test_upload" in result or result.endswith(".xlsx")
        mock_makedirs.assert_called_once()

    @patch('app.services.file_service.os.listdir')
    @patch('app.services.file_service.os.path.exists')
    @patch('app.services.file_service.os.stat')
    def test_list_files_success(self, mock_stat, mock_exists, mock_listdir):
        """Testa listagem bem-sucedida de arquivos"""
        mock_exists.return_value = True
        mock_listdir.return_value = ["file1.xlsx", "file2.csv", "not_a_file.txt"]

        # Mock do os.stat
        mock_stat_result = Mock()
        mock_stat_result.st_size = 1024
        mock_stat_result.st_ctime = 1640995200  # 2022-01-01
        mock_stat_result.st_mtime = 1640995200
        mock_stat.return_value = mock_stat_result

        result = list_export_files("test_directory")

        assert len(result) >= 0  # Pode filtrar alguns arquivos
        assert all("filename" in file for file in result)
        assert all("size" in file for file in result)
        assert all("created_at" in file for file in result)

    @patch('app.services.file_service.os.path.exists')
    def test_list_files_directory_not_exists(self, mock_exists):
        """Testa listagem quando diretório não existe"""
        mock_exists.return_value = False

        result = list_export_files("nonexistent_directory")

        assert result == []

    @patch('app.services.file_service.os.remove')
    @patch('app.services.file_service.os.path.exists')
    def test_delete_file_success(self, mock_exists, mock_remove):
        """Testa exclusão bem-sucedida de arquivo"""
        mock_exists.return_value = True
        mock_remove.return_value = None

        result = delete_file("test_file.xlsx", "test_directory")

        assert result is True
        mock_remove.assert_called_once()

    @patch('app.services.file_service.os.path.exists')
    def test_delete_file_not_exists(self, mock_exists):
        """Testa exclusão de arquivo inexistente"""
        mock_exists.return_value = False

        result = delete_file("nonexistent_file.xlsx", "test_directory")

        assert result is False

    @patch('app.services.file_service.os.remove')
    @patch('app.services.file_service.os.path.exists')
    def test_delete_file_error(self, mock_exists, mock_remove):
        """Testa erro na exclusão de arquivo"""
        mock_exists.return_value = True
        mock_remove.side_effect = OSError("Permission denied")

        result = delete_file("test_file.xlsx", "test_directory")

        assert result is False


@pytest.mark.unit
class TestMessageGenerator:
    """Testes para gerador de mensagens"""

    def test_init(self):
        """Testa inicialização do gerador"""
        generator = MessageGenerator()

        assert generator is not None
        assert hasattr(generator, 'synonyms')
        assert hasattr(generator, 'greetings')
        assert hasattr(generator, 'closings')

    def test_generate_variations_basic(self):
        """Testa geração básica de variações"""
        generator = MessageGenerator()
        template = "Olá {nome}, temos uma oferta especial para você!"

        variations = generator.generate_variations(template, count=3)

        assert len(variations) == 3
        assert all(isinstance(variation, str) for variation in variations)
        assert all("{nome}" in variation for variation in variations)

    def test_generate_variations_with_synonyms(self):
        """Testa geração de variações com sinônimos"""
        generator = MessageGenerator()
        template = "Olá {nome}, temos uma oferta especial!"

        variations = generator.generate_variations(template, count=5)

        # Deve haver variações diferentes
        assert len(set(variations)) > 1
        assert all("{nome}" in variation for variation in variations)

    def test_generate_variations_empty_template(self):
        """Testa geração com template vazio"""
        generator = MessageGenerator()

        variations = generator.generate_variations("", count=3)

        assert len(variations) == 3
        assert all(len(variation) > 0 for variation in variations)

    def test_generate_variations_zero_count(self):
        """Testa geração com count zero"""
        generator = MessageGenerator()
        template = "Teste"

        variations = generator.generate_variations(template, count=0)

        assert len(variations) == 0

    def test_generate_variations_large_count(self):
        """Testa geração com count muito grande"""
        generator = MessageGenerator()
        template = "Olá {nome}!"

        variations = generator.generate_variations(template, count=100)

        # Deve retornar no máximo 50 variações (limite interno)
        assert len(variations) <= 50

    def test_replace_synonyms(self):
        """Testa substituição de sinônimos"""
        generator = MessageGenerator()
        text = "Temos uma oferta especial"

        result = generator._replace_synonyms(text)

        assert isinstance(result, str)
        assert len(result) > 0

    def test_add_greeting_and_closing(self):
        """Testa adição de saudação e fechamento"""
        generator = MessageGenerator()
        text = "Mensagem principal"

        result = generator._add_greeting_and_closing(text)

        assert isinstance(result, str)
        assert len(result) > len(text)  # Deve ser maior que o texto original

    def test_vary_punctuation(self):
        """Testa variação de pontuação"""
        generator = MessageGenerator()
        text = "Olá! Como vai?"

        result = generator._vary_punctuation(text)

        assert isinstance(result, str)
        assert len(result) > 0

    def test_generate_variations_with_placeholders(self):
        """Testa geração preservando placeholders"""
        generator = MessageGenerator()
        template = "Olá {nome}, sua idade é {idade} anos"

        variations = generator.generate_variations(template, count=3)

        assert all("{nome}" in variation for variation in variations)
        assert all("{idade}" in variation for variation in variations)

    def test_generate_variations_consistency(self):
        """Testa consistência na geração de variações"""
        generator = MessageGenerator()
        template = "Mensagem de teste"

        # Gerar múltiplas vezes
        variations1 = generator.generate_variations(template, count=5)
        variations2 = generator.generate_variations(template, count=5)

        # Deve gerar variações (podem ser diferentes entre execuções)
        assert len(variations1) == 5
        assert len(variations2) == 5
        assert all(isinstance(v, str) for v in variations1)
        assert all(isinstance(v, str) for v in variations2)
