#!/usr/bin/env python3
"""
Script para executar testes do backend
"""
import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Executa um comando e exibe o resultado"""
    print(f"\n{'='*60}")
    print(f"🔄 {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True,
            cwd=Path(__file__).parent
        )
        
        if result.stdout:
            print(result.stdout)
        
        print(f"✅ {description} - SUCESSO")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - FALHOU")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False


def main():
    """Função principal"""
    print("🧪 TeemoFlow - Executando Testes Backend")
    print("=" * 60)
    
    # Verificar se estamos no diretório correto
    if not os.path.exists("tests"):
        print("❌ Diretório 'tests' não encontrado!")
        print("Execute este script a partir do diretório backend/")
        sys.exit(1)
    
    # Lista de comandos de teste
    test_commands = [
        {
            "command": "python -m pytest --version",
            "description": "Verificando instalação do pytest"
        },
        {
            "command": "python -m pytest tests/ -v --tb=short",
            "description": "Executando todos os testes"
        },
        {
            "command": "python -m pytest tests/ -v --tb=short --cov=app --cov-report=term-missing",
            "description": "Executando testes com cobertura"
        },
        {
            "command": "python -m pytest tests/test_job_manager.py -v",
            "description": "Testando JobManager"
        },
        {
            "command": "python -m pytest tests/test_workers.py -v",
            "description": "Testando Workers"
        },
        {
            "command": "python -m pytest tests/test_services.py -v",
            "description": "Testando Services"
        },
        {
            "command": "python -m pytest tests/test_api.py -v",
            "description": "Testando APIs"
        }
    ]
    
    # Executar comandos
    success_count = 0
    total_count = len(test_commands)
    
    for test_cmd in test_commands:
        if run_command(test_cmd["command"], test_cmd["description"]):
            success_count += 1
    
    # Resumo final
    print(f"\n{'='*60}")
    print(f"📊 RESUMO DOS TESTES")
    print(f"{'='*60}")
    print(f"✅ Sucessos: {success_count}/{total_count}")
    print(f"❌ Falhas: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print(f"\n🎉 TODOS OS TESTES PASSARAM!")
        return 0
    else:
        print(f"\n⚠️  ALGUNS TESTES FALHARAM!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
