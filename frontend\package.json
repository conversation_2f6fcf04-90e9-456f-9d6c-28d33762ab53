{"name": "teemoflow-frontend", "private": true, "version": "2.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "lint": "eslint . --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx,ts,tsx --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "quality": "npm run lint && npm run format:check && npm run test:run", "quality:fix": "npm run lint:fix && npm run format", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.0", "@mui/material": "^5.14.0", "@mui/x-data-grid": "^6.18.0", "axios": "^1.6.0", "date-fns": "^2.30.0", "notistack": "^3.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.0", "react-query": "^3.39.0", "react-router-dom": "^6.20.0", "recharts": "^2.8.0", "socket.io-client": "^4.7.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^14.1.0", "@testing-library/user-event": "^14.5.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.0", "@vitest/coverage-v8": "^1.0.0", "@vitest/ui": "^1.0.0", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "jsdom": "^23.2.0", "msw": "^2.0.0", "prettier": "^3.0.0", "vite": "^4.5.0", "vitest": "^1.0.0"}}