"""
Testes de integração
"""
import pytest
import asyncio
from unittest.mock import patch, Mock
from fastapi.testclient import TestClient

from main import app
from app.services.job_manager import JobManager
from app.services.websocket_manager import WebSocketManager
from app.models.schemas import JobStatus, LogLevel


@pytest.mark.integration
class TestJobWorkflow:
    """Testes de integração para fluxo completo de jobs"""
    
    def test_complete_job_lifecycle(self):
        """Testa ciclo completo de vida de um job"""
        manager = JobManager()
        
        # 1. Criar job
        job_id = manager.create_job("test_job", {"param": "value"})
        assert job_id is not None
        
        # 2. Verificar status inicial
        job = manager.get_job(job_id)
        assert job.status == JobStatus.PENDING
        assert job.progress == 0
        
        # 3. Iniciar job
        manager.update_job_status(job_id, JobStatus.RUNNING)
        job = manager.get_job(job_id)
        assert job.status == JobStatus.RUNNING
        assert job.started_at is not None
        
        # 4. Atualizar progresso
        manager.update_job_progress(job_id, 25)
        manager.add_log(job_id, LogLevel.INFO, "Progresso 25%")
        
        manager.update_job_progress(job_id, 50)
        manager.add_log(job_id, LogLevel.INFO, "Progresso 50%")
        
        manager.update_job_progress(job_id, 75)
        manager.add_log(job_id, LogLevel.INFO, "Progresso 75%")
        
        job = manager.get_job(job_id)
        assert job.progress == 75
        assert len(job.logs) == 3
        
        # 5. Definir arquivo de resultado
        manager.set_job_result_file(job_id, "/path/to/result.xlsx")
        job = manager.get_job(job_id)
        assert job.result_file == "/path/to/result.xlsx"
        
        # 6. Completar job
        manager.update_job_progress(job_id, 100)
        manager.update_job_status(job_id, JobStatus.COMPLETED)
        manager.add_log(job_id, LogLevel.INFO, "Job concluído com sucesso")
        
        job = manager.get_job(job_id)
        assert job.status == JobStatus.COMPLETED
        assert job.progress == 100
        assert job.completed_at is not None
        assert len(job.logs) == 4
    
    def test_job_cancellation_workflow(self):
        """Testa fluxo de cancelamento de job"""
        manager = JobManager()
        
        # Criar e iniciar job
        job_id = manager.create_job("test_job", {})
        manager.update_job_status(job_id, JobStatus.RUNNING)
        manager.update_job_progress(job_id, 30)
        
        # Cancelar job
        result = manager.cancel_job(job_id)
        assert result is True
        
        job = manager.get_job(job_id)
        assert job.status == JobStatus.CANCELLED
        assert job.completed_at is not None
        assert any("cancelado" in log["message"].lower() for log in job.logs)
    
    def test_job_error_workflow(self):
        """Testa fluxo de erro em job"""
        manager = JobManager()
        
        # Criar e iniciar job
        job_id = manager.create_job("test_job", {})
        manager.update_job_status(job_id, JobStatus.RUNNING)
        manager.update_job_progress(job_id, 40)
        
        # Simular erro
        manager.add_log(job_id, LogLevel.ERROR, "Erro durante execução")
        manager.set_job_error(job_id, "Erro de teste")
        manager.update_job_status(job_id, JobStatus.FAILED)
        
        job = manager.get_job(job_id)
        assert job.status == JobStatus.FAILED
        assert job.error_message == "Erro de teste"
        assert job.completed_at is not None
        assert any(log["level"] == LogLevel.ERROR for log in job.logs)


@pytest.mark.integration
class TestAPIIntegration:
    """Testes de integração para APIs"""
    
    @pytest.fixture
    def client(self):
        """Cliente de teste"""
        return TestClient(app)
    
    @patch('app.api.endpoints.flexible_search.start_flexible_search')
    def test_flexible_search_api_integration(self, mock_start_search, client):
        """Testa integração completa da API de busca flexível"""
        # Dados de requisição
        request_data = {
            "search_term": "restaurantes",
            "location": "São Paulo, SP",
            "max_results": 50,
            "file_format": "excel",
            "headless": True
        }
        
        # Iniciar busca
        response = client.post("/api/flexible-search/start", json=request_data)
        assert response.status_code == 200
        
        data = response.json()
        job_id = data["job_id"]
        assert job_id is not None
        
        # Verificar job criado
        response = client.get(f"/api/jobs/{job_id}")
        assert response.status_code == 200
        
        job_data = response.json()
        assert job_data["job_id"] == job_id
        assert job_data["job_type"] == "flexible_search"
        assert job_data["status"] == "pending"
    
    @patch('app.api.endpoints.automated_search.start_automated_search')
    def test_automated_search_api_integration(self, mock_start_search, client):
        """Testa integração completa da API de busca automatizada"""
        request_data = {
            "queries": [
                {
                    "search_term": "restaurantes",
                    "location": "São Paulo, SP",
                    "max_results": 30
                },
                {
                    "search_term": "pizzarias",
                    "location": "Rio de Janeiro, RJ",
                    "max_results": 20
                }
            ],
            "file_format": "excel",
            "headless": True,
            "interval_between_queries": 5
        }
        
        # Iniciar busca automatizada
        response = client.post("/api/automated-search/start", json=request_data)
        assert response.status_code == 200
        
        data = response.json()
        job_id = data["job_id"]
        
        # Verificar job criado
        response = client.get(f"/api/jobs/{job_id}")
        assert response.status_code == 200
        
        job_data = response.json()
        assert job_data["job_type"] == "automated_search"
    
    def test_job_management_api_integration(self, client):
        """Testa integração completa de gerenciamento de jobs"""
        # Listar jobs (inicialmente vazio)
        response = client.get("/api/jobs/")
        assert response.status_code == 200
        initial_jobs = response.json()
        
        # Criar job via API (usando busca flexível)
        with patch('app.api.endpoints.flexible_search.start_flexible_search'):
            request_data = {
                "search_term": "test",
                "location": "test",
                "max_results": 10,
                "file_format": "excel"
            }
            
            response = client.post("/api/flexible-search/start", json=request_data)
            assert response.status_code == 200
            job_id = response.json()["job_id"]
        
        # Listar jobs (deve ter 1 a mais)
        response = client.get("/api/jobs/")
        assert response.status_code == 200
        jobs = response.json()
        assert len(jobs) == len(initial_jobs) + 1
        
        # Pausar job
        response = client.post(f"/api/jobs/{job_id}/pause")
        assert response.status_code == 200
        
        # Retomar job
        response = client.post(f"/api/jobs/{job_id}/resume")
        assert response.status_code == 200
        
        # Cancelar job
        response = client.post(f"/api/jobs/{job_id}/cancel")
        assert response.status_code == 200
        
        # Verificar status cancelado
        response = client.get(f"/api/jobs/{job_id}")
        assert response.status_code == 200
        job_data = response.json()
        assert job_data["status"] == "cancelled"


@pytest.mark.integration
@pytest.mark.slow
class TestWebSocketIntegration:
    """Testes de integração para WebSocket"""
    
    @pytest.mark.asyncio
    async def test_websocket_manager_integration(self):
        """Testa integração do WebSocket Manager"""
        manager = WebSocketManager()
        
        # Mock de WebSocket
        mock_websocket = Mock()
        mock_websocket.send_text = Mock()
        
        # Adicionar conexão
        await manager.add_job_connection("test-job-123", mock_websocket)
        assert manager.get_connection_count("test-job-123") == 1
        
        # Broadcast de log
        log_entry = {
            "level": "INFO",
            "message": "Test log",
            "timestamp": "2024-01-01T10:00:00"
        }
        
        await manager.broadcast_log("test-job-123", log_entry)
        
        # Verificar se mensagem foi enviada
        mock_websocket.send_text.assert_called()
        
        # Remover conexão
        await manager.remove_job_connection("test-job-123", mock_websocket)
        assert manager.get_connection_count("test-job-123") == 0
    
    @pytest.mark.asyncio
    async def test_job_websocket_integration(self):
        """Testa integração entre JobManager e WebSocket"""
        job_manager = JobManager()
        ws_manager = WebSocketManager()
        
        # Mock de WebSocket
        mock_websocket = Mock()
        mock_websocket.send_text = Mock()
        
        # Criar job
        job_id = job_manager.create_job("test_job", {})
        
        # Conectar WebSocket
        await ws_manager.add_job_connection(job_id, mock_websocket)
        
        # Adicionar log (deve notificar WebSocket)
        job_manager.add_log(job_id, LogLevel.INFO, "Test message")
        
        # Atualizar status (deve notificar WebSocket)
        job_manager.update_job_status(job_id, JobStatus.RUNNING)
        
        # Verificar se WebSocket foi notificado
        # Note: Como as notificações são assíncronas, apenas verificamos se não houve erro
        assert True


@pytest.mark.integration
class TestFileServiceIntegration:
    """Testes de integração para serviço de arquivos"""
    
    def test_export_and_list_files_integration(self, tmp_path):
        """Testa integração entre exportação e listagem de arquivos"""
        # Dados de teste
        leads_data = [
            {
                "nome": "Restaurante Teste",
                "telefone": "(11) 1234-5678",
                "endereco": "Rua Teste, 123",
                "website": "https://teste.com"
            }
        ]
        
        # Configurar diretório temporário
        with patch('app.services.file_service.EXPORTS_DIR', str(tmp_path)):
            # Exportar arquivo
            from app.services.file_service import export_leads_to_file, list_files
            
            file_path = export_leads_to_file(leads_data, "test_integration", "excel")
            assert file_path is not None
            assert file_path.endswith(".xlsx")
            
            # Listar arquivos
            files = list_files(str(tmp_path))
            assert len(files) == 1
            assert files[0]["filename"].endswith(".xlsx")
            assert files[0]["size"] > 0
    
    def test_upload_and_delete_integration(self, tmp_path):
        """Testa integração entre upload e exclusão de arquivos"""
        from app.services.file_service import delete_file
        
        # Criar arquivo temporário
        test_file = tmp_path / "test_upload.xlsx"
        test_file.write_text("test content")
        
        # Verificar que arquivo existe
        assert test_file.exists()
        
        # Deletar arquivo
        result = delete_file("test_upload.xlsx", str(tmp_path))
        assert result is True
        
        # Verificar que arquivo foi deletado
        assert not test_file.exists()
