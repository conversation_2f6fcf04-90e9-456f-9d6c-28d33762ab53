"""
Configurações da aplicação TeemoFlow
"""
from typing import List
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings


class Settings(BaseSettings):
    """Configurações da aplicação"""

    # Configurações básicas
    APP_NAME: str = "TeemoFlow"
    APP_VERSION: str = "2.0.0"
    DEBUG: bool = True

    # Configurações de CORS
    ALLOWED_HOSTS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]

    # Configurações de banco de dados
    DATABASE_URL: str = "sqlite:///./teemoflow.db"

    # Configurações Redis (para Celery)
    REDIS_URL: str = "redis://localhost:6379/0"

    # Configurações de segurança
    SECRET_KEY: str = "your-secret-key-here-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # Configurações de arquivos
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    UPLOAD_DIR: str = "uploads"
    EXPORT_DIR: str = "exports"

    # Configurações do Selenium
    CHROME_HEADLESS: bool = True
    SELENIUM_TIMEOUT: int = 30

    # Configurações de logs
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/teemoflow.log"

    class Config:
        env_file = ".env"
        case_sensitive = True


# Instância global das configurações
settings = Settings()
