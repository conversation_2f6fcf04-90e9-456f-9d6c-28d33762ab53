"""
Serviço para geração de variações de mensagens
"""
import random
import re
from typing import List, Optional


class MessageVariationGenerator:
    """Gerador de variações de mensagens"""
    
    def __init__(self):
        # Sinônimos e variações para palavras comuns
        self.synonyms = {
            'olá': ['oi', 'olá', 'e aí', 'opa', 'salve'],
            'oi': ['olá', 'oi', 'e aí', 'opa', 'salve'],
            'bom dia': ['bom dia', 'boa manhã', 'um bom dia'],
            'boa tarde': ['boa tarde', 'uma boa tarde', 'boa tardinha'],
            'boa noite': ['boa noite', 'uma boa noite', 'boa noitinha'],
            'como vai': ['como vai', 'como está', 'tudo bem', 'tudo certo', 'como anda'],
            'tudo bem': ['tudo bem', 'tudo certo', 'como vai', 'como está'],
            'espero': ['espero', 'desejo', 'torço'],
            'gostaria': ['gostaria', 'queria', 'tenho interesse'],
            'queria': ['queria', 'gostaria', 'tenho interesse'],
            'obrigado': ['obrigado', 'muito obrigado', 'agradeço'],
            'obrigada': ['obrigada', 'muito obrigada', 'agradeço'],
            'abraço': ['abraço', 'abraços', 'um abraço', 'forte abraço'],
            'atenciosamente': ['atenciosamente', 'cordialmente', 'respeitosamente'],
            'aguardo': ['aguardo', 'fico no aguardo', 'espero'],
            'retorno': ['retorno', 'resposta', 'feedback', 'posicionamento'],
            'contato': ['contato', 'conversa', 'papo', 'diálogo'],
            'informações': ['informações', 'detalhes', 'dados', 'esclarecimentos'],
            'serviços': ['serviços', 'trabalhos', 'atividades'],
            'empresa': ['empresa', 'negócio', 'estabelecimento'],
            'oportunidade': ['oportunidade', 'chance', 'possibilidade'],
            'interesse': ['interesse', 'curiosidade', 'vontade'],
            'disponível': ['disponível', 'livre', 'com tempo'],
            'conversar': ['conversar', 'bater um papo', 'trocar uma ideia', 'dialogar'],
            'falar': ['falar', 'conversar', 'bater um papo', 'dialogar'],
        }
        
        # Estruturas de frases alternativas
        self.phrase_structures = {
            'greeting': [
                '{greeting} {name}!',
                '{greeting}, {name}!',
                '{greeting} {name}, {how_are_you}?',
                '{name}, {greeting}!',
            ],
            'introduction': [
                'Meu nome é {sender}',
                'Eu sou {sender}',
                'Me chamo {sender}',
                'Aqui é {sender}',
                'Fala {name}, aqui é {sender}',
            ],
            'purpose': [
                'Estou entrando em contato para {purpose}',
                'Gostaria de {purpose}',
                'Tenho interesse em {purpose}',
                'Queria {purpose}',
                'O motivo do contato é {purpose}',
            ],
            'closing': [
                '{closing}!',
                '{closing} e {thanks}!',
                '{thanks} e {closing}!',
                '{closing}. {thanks}!',
            ]
        }
        
        # Conectores e transições
        self.connectors = [
            'e', 'além disso', 'também', 'ainda', 'inclusive',
            'por isso', 'dessa forma', 'assim', 'portanto'
        ]
        
        # Emojis apropriados para negócios
        self.business_emojis = ['😊', '👋', '📞', '💼', '✨', '🤝', '📩', '🎯']
    
    def generate_variations(self, original_message: str, count: int = 5) -> List[str]:
        """Gera variações de uma mensagem original"""
        variations = []
        
        # Sempre incluir a mensagem original
        variations.append(original_message)
        
        for i in range(count - 1):
            try:
                variation = self._create_variation(original_message, i)
                if variation and variation not in variations:
                    variations.append(variation)
            except Exception:
                continue
        
        # Se não conseguiu gerar variações suficientes, criar algumas básicas
        while len(variations) < count:
            basic_variation = self._create_basic_variation(original_message, len(variations))
            if basic_variation not in variations:
                variations.append(basic_variation)
            else:
                break
        
        return variations[:count]
    
    def _create_variation(self, message: str, variation_index: int) -> str:
        """Cria uma variação específica da mensagem"""
        # Aplicar diferentes tipos de variação baseado no índice
        if variation_index % 4 == 0:
            return self._synonym_variation(message)
        elif variation_index % 4 == 1:
            return self._structure_variation(message)
        elif variation_index % 4 == 2:
            return self._emoji_variation(message)
        else:
            return self._tone_variation(message)
    
    def _synonym_variation(self, message: str) -> str:
        """Cria variação substituindo palavras por sinônimos"""
        words = message.split()
        new_words = []
        
        for word in words:
            # Remover pontuação para busca
            clean_word = re.sub(r'[^\w]', '', word.lower())
            
            # Buscar sinônimo
            if clean_word in self.synonyms:
                synonyms = self.synonyms[clean_word]
                # Escolher sinônimo diferente da palavra original
                available_synonyms = [s for s in synonyms if s != clean_word]
                if available_synonyms:
                    new_word = random.choice(available_synonyms)
                    # Manter capitalização original
                    if word[0].isupper():
                        new_word = new_word.capitalize()
                    # Manter pontuação
                    punctuation = re.findall(r'[^\w]', word)
                    if punctuation:
                        new_word += ''.join(punctuation)
                    new_words.append(new_word)
                else:
                    new_words.append(word)
            else:
                new_words.append(word)
        
        return ' '.join(new_words)
    
    def _structure_variation(self, message: str) -> str:
        """Cria variação mudando a estrutura das frases"""
        # Detectar padrões comuns e reestruturar
        
        # Mover cumprimentos
        if message.lower().startswith(('olá', 'oi', 'bom dia', 'boa tarde', 'boa noite')):
            parts = message.split('.', 1)
            if len(parts) > 1:
                greeting = parts[0].strip()
                rest = parts[1].strip()
                return f"{rest}. {greeting}!"
        
        # Inverter ordem de frases
        sentences = message.split('.')
        if len(sentences) > 2:
            sentences = [s.strip() for s in sentences if s.strip()]
            if len(sentences) >= 2:
                # Trocar primeira e segunda frase
                sentences[0], sentences[1] = sentences[1], sentences[0]
                return '. '.join(sentences) + '.'
        
        return message
    
    def _emoji_variation(self, message: str) -> str:
        """Adiciona ou modifica emojis na mensagem"""
        # Remover emojis existentes
        clean_message = re.sub(r'[^\w\s\.,!?;:\-\(\)]', '', message)
        
        # Adicionar emoji no início ou fim
        if random.choice([True, False]):
            emoji = random.choice(self.business_emojis)
            return f"{emoji} {clean_message}"
        else:
            emoji = random.choice(self.business_emojis)
            return f"{clean_message} {emoji}"
    
    def _tone_variation(self, message: str) -> str:
        """Varia o tom da mensagem (mais formal/informal)"""
        # Tornar mais informal
        informal_replacements = {
            'gostaria': 'queria',
            'poderia': 'pode',
            'atenciosamente': 'abraço',
            'cordialmente': 'abraços',
            'solicito': 'peço',
            'informações': 'info',
            'oportunidade': 'chance',
        }
        
        # Tornar mais formal
        formal_replacements = {
            'queria': 'gostaria',
            'pode': 'poderia',
            'abraço': 'atenciosamente',
            'abraços': 'cordialmente',
            'peço': 'solicito',
            'info': 'informações',
            'chance': 'oportunidade',
        }
        
        # Escolher direção aleatoriamente
        replacements = random.choice([informal_replacements, formal_replacements])
        
        result = message
        for old, new in replacements.items():
            result = re.sub(r'\b' + old + r'\b', new, result, flags=re.IGNORECASE)
        
        return result
    
    def _create_basic_variation(self, message: str, index: int) -> str:
        """Cria variação básica quando outras falham"""
        variations = [
            f"📱 {message}",
            f"{message} 😊",
            message.replace('!', '.'),
            message.replace('.', '!'),
            f"Oi! {message[message.find(' ')+1:] if ' ' in message else message}",
        ]
        
        if index < len(variations):
            return variations[index]
        
        return message


# Instância global do gerador
message_generator = MessageVariationGenerator()


def generate_message_variations(original_message: str, count: int = 5) -> List[str]:
    """Função principal para gerar variações de mensagem"""
    try:
        return message_generator.generate_variations(original_message, count)
    except Exception as e:
        print(f"Erro ao gerar variações: {e}")
        return [original_message]


def generate_message_variation(original_message: str) -> str:
    """Gera uma única variação da mensagem"""
    try:
        variations = generate_message_variations(original_message, 2)
        return variations[1] if len(variations) > 1 else variations[0]
    except Exception as e:
        print(f"Erro ao gerar variação: {e}")
        return original_message


# Exemplos de templates de mensagem
MESSAGE_TEMPLATES = {
    'apresentacao_servicos': """Olá {nome}! 😊

Meu nome é [SEU NOME] e trabalho com [SEU SERVIÇO].

Gostaria de apresentar nossos serviços e verificar se há interesse em uma conversa.

Aguardo seu retorno!

Abraços!""",
    
    'oferta_produto': """Oi {nome}! 👋

Espero que esteja tudo bem!

Tenho uma oportunidade interessante para apresentar e gostaria de saber se você teria interesse em conhecer.

Quando seria um bom momento para conversarmos?

Obrigado!""",
    
    'networking': """Olá {nome}!

Vi seu perfil/empresa e fiquei interessado em trocar uma ideia sobre possíveis parcerias.

Você teria disponibilidade para uma conversa rápida?

Aguardo seu contato!

Atenciosamente.""",
    
    'follow_up': """Oi {nome}!

Retomando nosso último contato, gostaria de saber se ainda há interesse em [ASSUNTO].

Fico no aguardo de seu retorno.

Abraço!""",
}
