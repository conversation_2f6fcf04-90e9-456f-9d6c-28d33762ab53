#!/usr/bin/env node

/**
 * Script para executar testes frontend do TeemoFlow
 * 
 * Uso:
 *   node run_tests.js [opções]
 * 
 * Opções:
 *   --watch, -w     Executar em modo watch
 *   --coverage, -c  Executar com cobertura
 *   --ui           Executar com interface gráfica
 *   --help, -h     Mostrar ajuda
 */

const { spawn } = require('child_process')
const path = require('path')

// Cores para output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function showHelp() {
  log('\n🧪 TeemoFlow - Frontend Tests', 'cyan')
  log('================================', 'cyan')
  log('\nUso: node run_tests.js [opções]\n')
  
  log('Opções:', 'yellow')
  log('  --watch, -w      Executar em modo watch (desenvolvimento)')
  log('  --coverage, -c   Executar com relatório de cobertura')
  log('  --ui            Executar com interface gráfica do Vitest')
  log('  --run           Executar uma vez e sair (padrão para CI)')
  log('  --help, -h      Mostrar esta ajuda')
  
  log('\nExemplos:', 'yellow')
  log('  node run_tests.js                # Executar todos os testes')
  log('  node run_tests.js --watch        # Modo desenvolvimento')
  log('  node run_tests.js --coverage     # Com cobertura')
  log('  node run_tests.js --ui           # Interface gráfica')
  
  log('\nScripts npm equivalentes:', 'yellow')
  log('  npm test                         # Modo watch')
  log('  npm run test:run                 # Executar uma vez')
  log('  npm run test:coverage            # Com cobertura')
  log('  npm run test:ui                  # Interface gráfica')
  
  log('\n📊 Cobertura mínima configurada: 70%', 'magenta')
  log('📁 Relatório HTML: coverage/index.html', 'magenta')
  console.log()
}

function parseArgs() {
  const args = process.argv.slice(2)
  const options = {
    watch: false,
    coverage: false,
    ui: false,
    run: false,
    help: false,
  }

  for (const arg of args) {
    switch (arg) {
      case '--watch':
      case '-w':
        options.watch = true
        break
      case '--coverage':
      case '-c':
        options.coverage = true
        break
      case '--ui':
        options.ui = true
        break
      case '--run':
        options.run = true
        break
      case '--help':
      case '-h':
        options.help = true
        break
      default:
        log(`⚠️  Opção desconhecida: ${arg}`, 'yellow')
        break
    }
  }

  // Se nenhuma opção específica foi passada, usar modo run por padrão
  if (!options.watch && !options.coverage && !options.ui && !options.help) {
    options.run = true
  }

  return options
}

function buildCommand(options) {
  const baseCmd = 'npx'
  const args = ['vitest']

  if (options.ui) {
    args.push('--ui')
  } else if (options.coverage) {
    args.push('run', '--coverage')
  } else if (options.run) {
    args.push('run')
  } else if (options.watch) {
    // Modo watch é padrão do vitest, não precisa adicionar nada
  }

  return { cmd: baseCmd, args }
}

function runTests(options) {
  const { cmd, args } = buildCommand(options)
  
  log('\n🧪 Iniciando testes frontend...', 'cyan')
  log(`📁 Diretório: ${process.cwd()}`, 'blue')
  log(`🔧 Comando: ${cmd} ${args.join(' ')}`, 'blue')
  
  if (options.coverage) {
    log('📊 Cobertura será gerada em: coverage/', 'magenta')
  }
  
  if (options.ui) {
    log('🌐 Interface será aberta no navegador', 'magenta')
  }
  
  console.log()

  const child = spawn(cmd, args, {
    stdio: 'inherit',
    shell: true,
    cwd: process.cwd(),
  })

  child.on('error', (error) => {
    log(`❌ Erro ao executar testes: ${error.message}`, 'red')
    process.exit(1)
  })

  child.on('close', (code) => {
    if (code === 0) {
      log('\n✅ Testes executados com sucesso!', 'green')
      
      if (options.coverage) {
        log('📊 Relatório de cobertura disponível em: coverage/index.html', 'green')
        log('💡 Abra o arquivo no navegador para ver detalhes', 'blue')
      }
    } else {
      log(`\n❌ Testes falharam (código: ${code})`, 'red')
      log('💡 Verifique os erros acima e corrija os problemas', 'yellow')
    }
    
    process.exit(code)
  })

  // Capturar Ctrl+C para finalizar graciosamente
  process.on('SIGINT', () => {
    log('\n⏹️  Interrompendo testes...', 'yellow')
    child.kill('SIGINT')
  })
}

function checkEnvironment() {
  const requiredFiles = [
    'package.json',
    'vitest.config.js',
    'src/test/setup.js',
  ]

  const missingFiles = requiredFiles.filter(file => {
    try {
      require('fs').accessSync(path.join(process.cwd(), file))
      return false
    } catch {
      return true
    }
  })

  if (missingFiles.length > 0) {
    log('❌ Arquivos necessários não encontrados:', 'red')
    missingFiles.forEach(file => log(`   - ${file}`, 'red'))
    log('\n💡 Certifique-se de estar no diretório frontend/', 'yellow')
    process.exit(1)
  }
}

function showTestInfo() {
  log('\n📋 Informações dos testes:', 'cyan')
  log('==========================', 'cyan')
  log('🔧 Framework: Vitest + Testing Library')
  log('🎯 Ambiente: jsdom (simula navegador)')
  log('📊 Cobertura: v8 (nativa do Node.js)')
  log('🎨 UI: @vitest/ui (interface gráfica)')
  log('🔍 Mocks: vi (built-in do Vitest)')
  
  log('\n📁 Estrutura de testes:', 'yellow')
  log('  src/__tests__/           # Testes de componentes principais')
  log('  src/components/**/*.test.jsx  # Testes de componentes')
  log('  src/pages/**/*.test.jsx       # Testes de páginas')
  log('  src/hooks/**/*.test.js        # Testes de hooks')
  log('  src/services/**/*.test.js     # Testes de serviços')
  log('  src/contexts/**/*.test.jsx    # Testes de contextos')
  
  log('\n🎯 Cobertura configurada:', 'magenta')
  log('  Branches: 70%')
  log('  Functions: 70%')
  log('  Lines: 70%')
  log('  Statements: 70%')
  console.log()
}

// Função principal
function main() {
  const options = parseArgs()

  if (options.help) {
    showHelp()
    showTestInfo()
    return
  }

  // Verificar ambiente
  checkEnvironment()

  // Executar testes
  runTests(options)
}

// Executar se chamado diretamente
if (require.main === module) {
  main()
}

module.exports = {
  parseArgs,
  buildCommand,
  runTests,
  showHelp,
  showTestInfo,
}
