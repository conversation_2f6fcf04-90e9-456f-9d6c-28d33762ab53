import React, { useState, useEffect } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Grid,
  Alert,
  Autocomplete,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material'
import {
  Search as SearchIcon,
  Cancel as CancelIcon,
  ExpandMore as ExpandMoreIcon,
  LocationOn as LocationIcon,
  Business as BusinessIcon,
} from '@mui/icons-material'
import { useForm, Controller } from 'react-hook-form'
import { useMutation, useQuery } from 'react-query'
import { useSnackbar } from 'notistack'
import { flexibleSearchApi } from '../services/api'
import JobProgress from '../components/JobProgress/JobProgress'

function FlexibleSearch() {
  const [currentJob, setCurrentJob] = useState(null)
  const { enqueueSnackbar } = useSnackbar()
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
    watch,
    setValue,
  } = useForm({
    defaultValues: {
      search_type: 'cep',
      location_query: '',
      business_type: '',
      quantidade: 50,
      radius_km: null,
      headless: true,
    },
  })

  const searchType = watch('search_type')

  // Buscar tipos de busca disponíveis
  const { data: searchTypes } = useQuery(
    'search-types',
    () => flexibleSearchApi.getSearchTypes()
  )

  // Buscar sugestões de negócios
  const { data: businessSuggestions } = useQuery(
    'business-suggestions',
    () => flexibleSearchApi.getBusinessSuggestions()
  )

  // Mutation para iniciar busca
  const startSearchMutation = useMutation(
    (data) => flexibleSearchApi.start(data),
    {
      onSuccess: (response) => {
        setCurrentJob({
          job_id: response.job_id,
          status: 'running',
          progress: 0,
        })
        enqueueSnackbar('Busca iniciada com sucesso!', { variant: 'success' })
      },
      onError: (error) => {
        enqueueSnackbar(`Erro ao iniciar busca: ${error.message}`, { variant: 'error' })
      },
    }
  )

  // Mutation para cancelar busca
  const cancelSearchMutation = useMutation(
    (jobId) => flexibleSearchApi.cancel(jobId),
    {
      onSuccess: () => {
        setCurrentJob(null)
        enqueueSnackbar('Busca cancelada com sucesso!', { variant: 'info' })
      },
      onError: (error) => {
        enqueueSnackbar(`Erro ao cancelar busca: ${error.message}`, { variant: 'error' })
      },
    }
  )

  const onSubmit = (data) => {
    startSearchMutation.mutate(data)
  }

  const handleCancel = () => {
    if (currentJob) {
      cancelSearchMutation.mutate(currentJob.job_id)
    }
  }

  const handleJobComplete = (job) => {
    setCurrentJob(null)
    if (job.status === 'completed') {
      enqueueSnackbar('Busca concluída com sucesso!', { variant: 'success' })
    } else if (job.status === 'failed') {
      enqueueSnackbar('Busca falhou. Verifique os logs.', { variant: 'error' })
    }
  }

  const getPlaceholder = () => {
    const type = searchTypes?.search_types?.find(t => t.value === searchType)
    return type?.placeholder || 'Digite a localização'
  }

  const getLocationDescription = () => {
    const type = searchTypes?.search_types?.find(t => t.value === searchType)
    return type?.description || ''
  }

  const getAllBusinessOptions = () => {
    if (!businessSuggestions?.categories) return []
    
    const options = []
    businessSuggestions.categories.forEach(category => {
      category.businesses.forEach(business => {
        options.push({
          label: business,
          category: category.category
        })
      })
    })
    return options
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Busca Flexível
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Capture leads usando diferentes tipos de localização: CEP, cidade, bairro, endereço ou coordenadas
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Formulário */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Configurar Busca
              </Typography>

              <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ mt: 2 }}>
                {/* Tipo de Busca */}
                <FormControl fullWidth margin="normal">
                  <InputLabel>Tipo de Localização</InputLabel>
                  <Controller
                    name="search_type"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        disabled={!!currentJob}
                        label="Tipo de Localização"
                      >
                        {searchTypes?.search_types?.map((type) => (
                          <MenuItem key={type.value} value={type.value}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <LocationIcon fontSize="small" />
                              <Box>
                                <Typography variant="body2" fontWeight="bold">
                                  {type.label}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {type.description}
                                </Typography>
                              </Box>
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                    )}
                  />
                </FormControl>

                {/* Localização */}
                <TextField
                  fullWidth
                  label="Localização"
                  placeholder={getPlaceholder()}
                  margin="normal"
                  {...register('location_query', {
                    required: 'Localização é obrigatória',
                  })}
                  error={!!errors.location_query}
                  helperText={errors.location_query?.message || getLocationDescription()}
                  disabled={!!currentJob}
                />

                {/* Tipo de Negócio */}
                <Controller
                  name="business_type"
                  control={control}
                  rules={{ required: 'Tipo de negócio é obrigatório' }}
                  render={({ field }) => (
                    <Autocomplete
                      {...field}
                      options={getAllBusinessOptions()}
                      groupBy={(option) => option.category}
                      getOptionLabel={(option) => typeof option === 'string' ? option : option.label}
                      freeSolo
                      disabled={!!currentJob}
                      onChange={(_, value) => {
                        const businessType = typeof value === 'string' ? value : value?.label || ''
                        field.onChange(businessType)
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Tipo de Negócio"
                          placeholder="Ex: restaurantes, farmácias, etc."
                          margin="normal"
                          error={!!errors.business_type}
                          helperText={errors.business_type?.message}
                        />
                      )}
                      renderOption={(props, option) => (
                        <Box component="li" {...props}>
                          <BusinessIcon sx={{ mr: 1, fontSize: 16 }} />
                          {option.label}
                        </Box>
                      )}
                    />
                  )}
                />

                {/* Quantidade */}
                <TextField
                  fullWidth
                  label="Quantidade de Leads"
                  type="number"
                  margin="normal"
                  {...register('quantidade', {
                    required: 'Quantidade é obrigatória',
                    min: { value: 1, message: 'Mínimo 1' },
                    max: { value: 1000, message: 'Máximo 1000' },
                  })}
                  error={!!errors.quantidade}
                  helperText={errors.quantidade?.message}
                  disabled={!!currentJob}
                />

                {/* Raio (opcional) */}
                <TextField
                  fullWidth
                  label="Raio de Busca (km) - Opcional"
                  type="number"
                  margin="normal"
                  {...register('radius_km', {
                    min: { value: 1, message: 'Mínimo 1 km' },
                    max: { value: 50, message: 'Máximo 50 km' },
                  })}
                  error={!!errors.radius_km}
                  helperText={errors.radius_km?.message || 'Deixe vazio para busca sem limite de raio'}
                  disabled={!!currentJob}
                />

                {/* Modo headless */}
                <FormControlLabel
                  control={
                    <Checkbox
                      {...register('headless')}
                      disabled={!!currentJob}
                    />
                  }
                  label="Modo headless (navegador invisível)"
                  sx={{ mt: 2, mb: 2 }}
                />

                {/* Botões */}
                <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                  <Button
                    type="submit"
                    variant="contained"
                    startIcon={<SearchIcon />}
                    disabled={!!currentJob || startSearchMutation.isLoading}
                    fullWidth
                  >
                    {startSearchMutation.isLoading ? 'Iniciando...' : 'Iniciar Busca'}
                  </Button>

                  {currentJob && (
                    <Button
                      variant="outlined"
                      color="error"
                      startIcon={<CancelIcon />}
                      onClick={handleCancel}
                      disabled={cancelSearchMutation.isLoading}
                    >
                      Cancelar
                    </Button>
                  )}
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Status e Progresso */}
        <Grid item xs={12} md={6}>
          {currentJob ? (
            <JobProgress
              jobId={currentJob.job_id}
              onComplete={handleJobComplete}
            />
          ) : (
            <Box>
              {/* Exemplos de uso */}
              <Card sx={{ mb: 2 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Exemplos de Uso
                  </Typography>
                  
                  {searchTypes?.search_types?.map((type) => (
                    <Accordion key={type.value}>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography variant="body2" fontWeight="bold">
                          {type.label}: {type.description}
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Typography variant="body2" color="text.secondary">
                          <strong>Exemplo:</strong> {type.example}
                        </Typography>
                      </AccordionDetails>
                    </Accordion>
                  ))}
                </CardContent>
              </Card>

              {/* Sugestões de negócios */}
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Sugestões de Negócios
                  </Typography>
                  
                  {businessSuggestions?.categories?.map((category) => (
                    <Box key={category.category} sx={{ mb: 2 }}>
                      <Typography variant="body2" fontWeight="bold" gutterBottom>
                        {category.category}
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {category.businesses.map((business) => (
                          <Chip
                            key={business}
                            label={business}
                            size="small"
                            onClick={() => setValue('business_type', business)}
                            sx={{ cursor: 'pointer' }}
                          />
                        ))}
                      </Box>
                    </Box>
                  ))}
                </CardContent>
              </Card>
            </Box>
          )}
        </Grid>
      </Grid>
    </Box>
  )
}

export default FlexibleSearch
