import React from 'react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen } from '@testing-library/react'
import { MemoryRouter } from 'react-router-dom'
import { renderWithProviders } from '../test/utils'
import App from '../App'

// Mock dos componentes de página
vi.mock('../pages/Dashboard', () => ({
  default: () => <div data-testid="dashboard-page">Dashboard</div>
}))

vi.mock('../pages/FlexibleSearch', () => ({
  default: () => <div data-testid="flexible-search-page">Flexible Search</div>
}))

vi.mock('../pages/CEPSearch', () => ({
  default: () => <div data-testid="cep-search-page">CEP Search</div>
}))

vi.mock('../pages/GoogleMapsSearch', () => ({
  default: () => <div data-testid="gmaps-search-page">Google Maps Search</div>
}))

vi.mock('../pages/AutomatedSearch', () => ({
  default: () => <div data-testid="automated-search-page">Automated Search</div>
}))

vi.mock('../pages/Messaging', () => ({
  default: () => <div data-testid="messaging-page">Messaging</div>
}))

vi.mock('../pages/Jobs', () => ({
  default: () => <div data-testid="jobs-page">Jobs</div>
}))

vi.mock('../pages/Files', () => ({
  default: () => <div data-testid="files-page">Files</div>
}))

// Mock do Layout
vi.mock('../components/Layout/Layout', () => ({
  default: ({ children }) => (
    <div data-testid="layout">
      <nav data-testid="navigation">Navigation</nav>
      <main data-testid="main-content">{children}</main>
    </div>
  )
}))

describe('App', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Estrutura básica', () => {
    it('deve renderizar layout principal', () => {
      renderWithProviders(
        <MemoryRouter initialEntries={['/']}>
          <App />
        </MemoryRouter>
      )

      expect(screen.getByTestId('layout')).toBeInTheDocument()
      expect(screen.getByTestId('navigation')).toBeInTheDocument()
      expect(screen.getByTestId('main-content')).toBeInTheDocument()
    })

    it('deve ter estrutura de Box com flex layout', () => {
      renderWithProviders(
        <MemoryRouter initialEntries={['/']}>
          <App />
        </MemoryRouter>
      )

      const appContainer = screen.getByTestId('layout').parentElement
      expect(appContainer).toHaveStyle({
        display: 'flex',
        minHeight: '100vh'
      })
    })
  })

  describe('Roteamento', () => {
    it('deve renderizar Dashboard na rota raiz', () => {
      renderWithProviders(
        <MemoryRouter initialEntries={['/']}>
          <App />
        </MemoryRouter>
      )

      expect(screen.getByTestId('dashboard-page')).toBeInTheDocument()
      expect(screen.getByText('Dashboard')).toBeInTheDocument()
    })

    it('deve renderizar FlexibleSearch na rota /flexible-search', () => {
      renderWithProviders(
        <MemoryRouter initialEntries={['/flexible-search']}>
          <App />
        </MemoryRouter>
      )

      expect(screen.getByTestId('flexible-search-page')).toBeInTheDocument()
      expect(screen.getByText('Flexible Search')).toBeInTheDocument()
    })

    it('deve renderizar CEPSearch na rota /cep-search', () => {
      renderWithProviders(
        <MemoryRouter initialEntries={['/cep-search']}>
          <App />
        </MemoryRouter>
      )

      expect(screen.getByTestId('cep-search-page')).toBeInTheDocument()
      expect(screen.getByText('CEP Search')).toBeInTheDocument()
    })

    it('deve renderizar GoogleMapsSearch na rota /gmaps-search', () => {
      renderWithProviders(
        <MemoryRouter initialEntries={['/gmaps-search']}>
          <App />
        </MemoryRouter>
      )

      expect(screen.getByTestId('gmaps-search-page')).toBeInTheDocument()
      expect(screen.getByText('Google Maps Search')).toBeInTheDocument()
    })

    it('deve renderizar AutomatedSearch na rota /automated-search', () => {
      renderWithProviders(
        <MemoryRouter initialEntries={['/automated-search']}>
          <App />
        </MemoryRouter>
      )

      expect(screen.getByTestId('automated-search-page')).toBeInTheDocument()
      expect(screen.getByText('Automated Search')).toBeInTheDocument()
    })

    it('deve renderizar Messaging na rota /messaging', () => {
      renderWithProviders(
        <MemoryRouter initialEntries={['/messaging']}>
          <App />
        </MemoryRouter>
      )

      expect(screen.getByTestId('messaging-page')).toBeInTheDocument()
      expect(screen.getByText('Messaging')).toBeInTheDocument()
    })

    it('deve renderizar Jobs na rota /jobs', () => {
      renderWithProviders(
        <MemoryRouter initialEntries={['/jobs']}>
          <App />
        </MemoryRouter>
      )

      expect(screen.getByTestId('jobs-page')).toBeInTheDocument()
      expect(screen.getByText('Jobs')).toBeInTheDocument()
    })

    it('deve renderizar Files na rota /files', () => {
      renderWithProviders(
        <MemoryRouter initialEntries={['/files']}>
          <App />
        </MemoryRouter>
      )

      expect(screen.getByTestId('files-page')).toBeInTheDocument()
      expect(screen.getByText('Files')).toBeInTheDocument()
    })
  })

  describe('Navegação', () => {
    it('deve navegar entre rotas corretamente', () => {
      const { rerender } = renderWithProviders(
        <MemoryRouter initialEntries={['/']}>
          <App />
        </MemoryRouter>
      )

      // Verificar rota inicial
      expect(screen.getByTestId('dashboard-page')).toBeInTheDocument()

      // Navegar para outra rota
      rerender(
        <MemoryRouter initialEntries={['/jobs']}>
          <App />
        </MemoryRouter>
      )

      expect(screen.getByTestId('jobs-page')).toBeInTheDocument()
      expect(screen.queryByTestId('dashboard-page')).not.toBeInTheDocument()
    })

    it('deve manter layout ao navegar entre páginas', () => {
      const { rerender } = renderWithProviders(
        <MemoryRouter initialEntries={['/']}>
          <App />
        </MemoryRouter>
      )

      expect(screen.getByTestId('layout')).toBeInTheDocument()

      // Navegar para outra página
      rerender(
        <MemoryRouter initialEntries={['/messaging']}>
          <App />
        </MemoryRouter>
      )

      // Layout deve permanecer
      expect(screen.getByTestId('layout')).toBeInTheDocument()
      expect(screen.getByTestId('navigation')).toBeInTheDocument()
    })
  })

  describe('Rotas inválidas', () => {
    it('deve lidar com rotas não existentes', () => {
      renderWithProviders(
        <MemoryRouter initialEntries={['/rota-inexistente']}>
          <App />
        </MemoryRouter>
      )

      // Layout deve estar presente mesmo com rota inválida
      expect(screen.getByTestId('layout')).toBeInTheDocument()
      
      // Nenhuma página específica deve ser renderizada
      expect(screen.queryByTestId('dashboard-page')).not.toBeInTheDocument()
      expect(screen.queryByTestId('jobs-page')).not.toBeInTheDocument()
    })
  })

  describe('Estrutura de componentes', () => {
    it('deve ter hierarquia correta de componentes', () => {
      renderWithProviders(
        <MemoryRouter initialEntries={['/']}>
          <App />
        </MemoryRouter>
      )

      const layout = screen.getByTestId('layout')
      const mainContent = screen.getByTestId('main-content')
      const page = screen.getByTestId('dashboard-page')

      // Verificar hierarquia
      expect(layout).toContainElement(mainContent)
      expect(mainContent).toContainElement(page)
    })

    it('deve aplicar estilos de container corretos', () => {
      renderWithProviders(
        <MemoryRouter initialEntries={['/']}>
          <App />
        </MemoryRouter>
      )

      const appContainer = screen.getByTestId('layout').parentElement
      
      // Verificar se tem as classes do Material-UI Box
      expect(appContainer).toHaveClass('MuiBox-root')
    })
  })

  describe('Lazy loading (preparação futura)', () => {
    it('deve renderizar todas as páginas sem lazy loading por enquanto', () => {
      const routes = [
        '/',
        '/flexible-search',
        '/cep-search',
        '/gmaps-search',
        '/automated-search',
        '/messaging',
        '/jobs',
        '/files'
      ]

      routes.forEach(route => {
        renderWithProviders(
          <MemoryRouter initialEntries={[route]}>
            <App />
          </MemoryRouter>
        )

        // Todas as páginas devem renderizar imediatamente
        expect(screen.getByTestId('layout')).toBeInTheDocument()
      })
    })
  })

  describe('Acessibilidade', () => {
    it('deve ter estrutura semântica básica', () => {
      renderWithProviders(
        <MemoryRouter initialEntries={['/']}>
          <App />
        </MemoryRouter>
      )

      // Verificar se tem elementos semânticos
      expect(screen.getByTestId('navigation')).toBeInTheDocument()
      expect(screen.getByTestId('main-content')).toBeInTheDocument()
    })

    it('deve ser navegável por teclado', () => {
      renderWithProviders(
        <MemoryRouter initialEntries={['/']}>
          <App />
        </MemoryRouter>
      )

      const layout = screen.getByTestId('layout')
      
      // Verificar se não há elementos que impedem navegação por teclado
      expect(layout).not.toHaveAttribute('tabindex', '-1')
    })
  })
})
