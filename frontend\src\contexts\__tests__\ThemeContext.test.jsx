import React from 'react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { ThemeProvider, useTheme } from '../ThemeContext'

// Mock do localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
})

describe('ThemeContext', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('ThemeProvider', () => {
    it('deve fornecer tema claro por padrão', () => {
      mockLocalStorage.getItem.mockReturnValue(null)

      const wrapper = ({ children }) => (
        <ThemeProvider>{children}</ThemeProvider>
      )

      const { result } = renderHook(() => useTheme(), { wrapper })

      expect(result.current.isDarkMode).toBe(false)
      expect(result.current.theme.palette.mode).toBe('light')
    })

    it('deve carregar tema escuro do localStorage', () => {
      mockLocalStorage.getItem.mockReturnValue('true')

      const wrapper = ({ children }) => (
        <ThemeProvider>{children}</ThemeProvider>
      )

      const { result } = renderHook(() => useTheme(), { wrapper })

      expect(result.current.isDarkMode).toBe(true)
      expect(result.current.theme.palette.mode).toBe('dark')
    })

    it('deve alternar entre temas', () => {
      mockLocalStorage.getItem.mockReturnValue(null)

      const wrapper = ({ children }) => (
        <ThemeProvider>{children}</ThemeProvider>
      )

      const { result } = renderHook(() => useTheme(), { wrapper })

      // Inicialmente claro
      expect(result.current.isDarkMode).toBe(false)

      // Alternar para escuro
      act(() => {
        result.current.toggleTheme()
      })

      expect(result.current.isDarkMode).toBe(true)
      expect(result.current.theme.palette.mode).toBe('dark')
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('darkMode', 'true')

      // Alternar de volta para claro
      act(() => {
        result.current.toggleTheme()
      })

      expect(result.current.isDarkMode).toBe(false)
      expect(result.current.theme.palette.mode).toBe('light')
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('darkMode', 'false')
    })

    it('deve persistir preferência no localStorage', () => {
      mockLocalStorage.getItem.mockReturnValue(null)

      const wrapper = ({ children }) => (
        <ThemeProvider>{children}</ThemeProvider>
      )

      const { result } = renderHook(() => useTheme(), { wrapper })

      act(() => {
        result.current.toggleTheme()
      })

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('darkMode', 'true')
    })

    it('deve lidar com valores inválidos no localStorage', () => {
      mockLocalStorage.getItem.mockReturnValue('invalid')

      const wrapper = ({ children }) => (
        <ThemeProvider>{children}</ThemeProvider>
      )

      const { result } = renderHook(() => useTheme(), { wrapper })

      // Deve usar padrão (claro) para valores inválidos
      expect(result.current.isDarkMode).toBe(false)
    })
  })

  describe('useTheme hook', () => {
    it('deve lançar erro quando usado fora do provider', () => {
      // Capturar erro do console
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      expect(() => {
        renderHook(() => useTheme())
      }).toThrow('useTheme deve ser usado dentro de um ThemeProvider')

      consoleSpy.mockRestore()
    })

    it('deve retornar contexto válido quando usado dentro do provider', () => {
      const wrapper = ({ children }) => (
        <ThemeProvider>{children}</ThemeProvider>
      )

      const { result } = renderHook(() => useTheme(), { wrapper })

      expect(result.current).toHaveProperty('isDarkMode')
      expect(result.current).toHaveProperty('toggleTheme')
      expect(result.current).toHaveProperty('theme')
      expect(typeof result.current.toggleTheme).toBe('function')
    })
  })

  describe('Tema MUI', () => {
    it('deve configurar cores corretas para tema claro', () => {
      mockLocalStorage.getItem.mockReturnValue(null)

      const wrapper = ({ children }) => (
        <ThemeProvider>{children}</ThemeProvider>
      )

      const { result } = renderHook(() => useTheme(), { wrapper })

      const theme = result.current.theme
      expect(theme.palette.mode).toBe('light')
      expect(theme.palette.primary.main).toBe('#1976d2')
      expect(theme.palette.background.default).toBe('#fafafa')
    })

    it('deve configurar cores corretas para tema escuro', () => {
      mockLocalStorage.getItem.mockReturnValue('true')

      const wrapper = ({ children }) => (
        <ThemeProvider>{children}</ThemeProvider>
      )

      const { result } = renderHook(() => useTheme(), { wrapper })

      const theme = result.current.theme
      expect(theme.palette.mode).toBe('dark')
      expect(theme.palette.primary.main).toBe('#90caf9')
      expect(theme.palette.background.default).toBe('#121212')
    })

    it('deve manter configurações customizadas do tema', () => {
      const wrapper = ({ children }) => (
        <ThemeProvider>{children}</ThemeProvider>
      )

      const { result } = renderHook(() => useTheme(), { wrapper })

      const theme = result.current.theme
      
      // Verificar se mantém configurações customizadas
      expect(theme.typography.fontFamily).toContain('Roboto')
      expect(theme.shape.borderRadius).toBe(8)
      expect(theme.spacing(1)).toBe(8) // MUI default spacing
    })
  })

  describe('Integração com sistema', () => {
    it('deve detectar preferência do sistema quando não há valor salvo', () => {
      mockLocalStorage.getItem.mockReturnValue(null)
      
      // Mock da preferência do sistema
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: vi.fn().mockImplementation(query => ({
          matches: query === '(prefers-color-scheme: dark)',
          media: query,
          onchange: null,
          addListener: vi.fn(),
          removeListener: vi.fn(),
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
          dispatchEvent: vi.fn(),
        })),
      })

      const wrapper = ({ children }) => (
        <ThemeProvider>{children}</ThemeProvider>
      )

      const { result } = renderHook(() => useTheme(), { wrapper })

      // Como mockamos que o sistema prefere dark mode
      expect(result.current.isDarkMode).toBe(true)
    })
  })
})
