import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Alert,
  Tabs,
  Tab,
  FormControlLabel,
  Checkbox,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
} from '@mui/material'
import {
  Send as SendIcon,
  Cancel as CancelIcon,
  Upload as UploadIcon,
  Delete as DeleteIcon,
  Preview as PreviewIcon,
  AutoAwesome as AutoAwesomeIcon,
  ExpandMore as ExpandMoreIcon,
  ContactPhone as ContactPhoneIcon,
  Message as MessageIcon,
  WhatsApp as WhatsAppIcon,
} from '@mui/icons-material'
import { useForm, Controller } from 'react-hook-form'
import { useMutation, useQuery } from 'react-query'
import { useSnackbar } from 'notistack'
import { messagingApi } from '../services/api'
import JobProgress from '../components/JobProgress/JobProgress'

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`messaging-tabpanel-${index}`}
      aria-labelledby={`messaging-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  )
}

function Messaging() {
  const [currentJob, setCurrentJob] = useState(null)
  const [tabValue, setTabValue] = useState(0)
  const [contacts, setContacts] = useState([])
  const [uploadedFile, setUploadedFile] = useState(null)
  const [previewDialog, setPreviewDialog] = useState(false)
  const [messageVariations, setMessageVariations] = useState([])
  const { enqueueSnackbar } = useSnackbar()

  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
    watch,
    setValue,
    getValues,
  } = useForm({
    defaultValues: {
      message_template: '',
      interval: 30,
      use_variations: false,
    },
  })

  const messageTemplate = watch('message_template')

  // Mutation para upload de contatos
  const uploadContactsMutation = useMutation(
    (file) => messagingApi.uploadContacts(file),
    {
      onSuccess: (response) => {
        setUploadedFile(response)
        enqueueSnackbar(`Arquivo carregado: ${response.contacts_count} contatos`, { variant: 'success' })
      },
      onError: (error) => {
        enqueueSnackbar(`Erro no upload: ${error.message}`, { variant: 'error' })
      },
    }
  )

  // Mutation para gerar variação
  const generateVariationMutation = useMutation(
    (message) => messagingApi.generateVariation(message),
    {
      onSuccess: (response) => {
        setMessageVariations(prev => [...prev, response.variation])
        enqueueSnackbar('Variação gerada!', { variant: 'success' })
      },
      onError: (error) => {
        enqueueSnackbar(`Erro ao gerar variação: ${error.message}`, { variant: 'error' })
      },
    }
  )

  // Mutation para enviar mensagens
  const sendMessagesMutation = useMutation(
    (data) => messagingApi.send(data),
    {
      onSuccess: (response) => {
        setCurrentJob({
          job_id: response.job_id,
          status: 'running',
          progress: 0,
        })
        enqueueSnackbar('Envio de mensagens iniciado!', { variant: 'success' })
      },
      onError: (error) => {
        enqueueSnackbar(`Erro ao iniciar envio: ${error.message}`, { variant: 'error' })
      },
    }
  )

  // Mutation para cancelar envio
  const cancelSendingMutation = useMutation(
    (jobId) => messagingApi.cancel(jobId),
    {
      onSuccess: () => {
        setCurrentJob(null)
        enqueueSnackbar('Envio cancelado!', { variant: 'info' })
      },
      onError: (error) => {
        enqueueSnackbar(`Erro ao cancelar: ${error.message}`, { variant: 'error' })
      },
    }
  )

  const handleFileUpload = (event) => {
    const file = event.target.files[0]
    if (file) {
      uploadContactsMutation.mutate(file)
    }
  }

  const handleAddManualContact = () => {
    const name = prompt('Nome do contato:')
    const phone = prompt('Telefone (com DDD):')

    if (name && phone) {
      setContacts(prev => [...prev, { name, phone }])
      enqueueSnackbar('Contato adicionado!', { variant: 'success' })
    }
  }

  const handleRemoveContact = (index) => {
    setContacts(prev => prev.filter((_, i) => i !== index))
  }

  const handleGenerateVariation = () => {
    if (messageTemplate.trim()) {
      generateVariationMutation.mutate(messageTemplate)
    }
  }

  const handlePreviewMessage = () => {
    setPreviewDialog(true)
  }

  const onSubmit = (data) => {
    const allContacts = [...contacts]

    if (uploadedFile) {
      // Aqui você carregaria os contatos do arquivo
      // Por simplicidade, vamos simular
      enqueueSnackbar('Funcionalidade de arquivo será implementada', { variant: 'info' })
      return
    }

    if (allContacts.length === 0) {
      enqueueSnackbar('Adicione pelo menos um contato', { variant: 'error' })
      return
    }

    if (!data.message_template.includes('{nome}')) {
      enqueueSnackbar('A mensagem deve conter {nome} para personalização', { variant: 'error' })
      return
    }

    sendMessagesMutation.mutate({
      contacts: allContacts,
      message_template: data.message_template,
      interval: data.interval,
      use_variations: data.use_variations,
    })
  }

  const handleCancel = () => {
    if (currentJob) {
      cancelSendingMutation.mutate(currentJob.job_id)
    }
  }

  const handleJobComplete = (job) => {
    setCurrentJob(null)
    if (job.status === 'completed') {
      enqueueSnackbar('Envio concluído!', { variant: 'success' })
    } else if (job.status === 'failed') {
      enqueueSnackbar('Envio falhou. Verifique os logs.', { variant: 'error' })
    }
  }

  const getPreviewMessage = () => {
    return messageTemplate.replace('{nome}', 'João Silva')
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Sistema de Mensagens
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Envie mensagens personalizadas via WhatsApp Web
        </Typography>
      </Box>

      {currentJob ? (
        <JobProgress
          jobId={currentJob.job_id}
          onComplete={handleJobComplete}
        />
      ) : (
        <Grid container spacing={3}>
          {/* Configuração */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Tabs value={tabValue} onChange={(e, v) => setTabValue(v)}>
                  <Tab icon={<ContactPhoneIcon />} label="Contatos" />
                  <Tab icon={<MessageIcon />} label="Mensagem" />
                  <Tab icon={<SendIcon />} label="Envio" />
                </Tabs>

                <Box component="form" onSubmit={handleSubmit(onSubmit)}>
                  {/* Tab 1: Contatos */}
                  <TabPanel value={tabValue} index={0}>
                    <Typography variant="h6" gutterBottom>
                      Gerenciar Contatos
                    </Typography>

                    {/* Upload de arquivo */}
                    <Card variant="outlined" sx={{ mb: 3 }}>
                      <CardContent>
                        <Typography variant="subtitle1" gutterBottom>
                          Upload de Arquivo
                        </Typography>
                        <input
                          accept=".xlsx,.xls,.csv"
                          style={{ display: 'none' }}
                          id="file-upload"
                          type="file"
                          onChange={handleFileUpload}
                          disabled={!!currentJob}
                        />
                        <label htmlFor="file-upload">
                          <Button
                            variant="outlined"
                            component="span"
                            startIcon={<UploadIcon />}
                            disabled={!!currentJob || uploadContactsMutation.isLoading}
                            sx={{ mr: 2 }}
                          >
                            {uploadContactsMutation.isLoading ? 'Carregando...' : 'Carregar Excel/CSV'}
                          </Button>
                        </label>

                        {uploadedFile && (
                          <Chip
                            label={`${uploadedFile.filename} (${uploadedFile.contacts_count} contatos)`}
                            color="success"
                            onDelete={() => setUploadedFile(null)}
                          />
                        )}

                        <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                          Arquivo deve conter colunas: nome, telefone
                        </Typography>
                      </CardContent>
                    </Card>

                    {/* Contatos manuais */}
                    <Card variant="outlined">
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                          <Typography variant="subtitle1">
                            Contatos Manuais ({contacts.length})
                          </Typography>
                          <Button
                            variant="outlined"
                            size="small"
                            onClick={handleAddManualContact}
                            disabled={!!currentJob}
                          >
                            Adicionar Contato
                          </Button>
                        </Box>

                        {contacts.length > 0 ? (
                          <List>
                            {contacts.map((contact, index) => (
                              <ListItem key={index}>
                                <ListItemText
                                  primary={contact.name}
                                  secondary={contact.phone}
                                />
                                <ListItemSecondaryAction>
                                  <IconButton
                                    edge="end"
                                    onClick={() => handleRemoveContact(index)}
                                    disabled={!!currentJob}
                                  >
                                    <DeleteIcon />
                                  </IconButton>
                                </ListItemSecondaryAction>
                              </ListItem>
                            ))}
                          </List>
                        ) : (
                          <Typography color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
                            Nenhum contato adicionado
                          </Typography>
                        )}
                      </CardContent>
                    </Card>
                  </TabPanel>

                  {/* Tab 2: Mensagem */}
                  <TabPanel value={tabValue} index={1}>
                    <Typography variant="h6" gutterBottom>
                      Configurar Mensagem
                    </Typography>

                    <TextField
                      fullWidth
                      label="Template da Mensagem"
                      multiline
                      rows={6}
                      placeholder="Olá {nome}! Como vai? ..."
                      margin="normal"
                      {...register('message_template', {
                        required: 'Mensagem é obrigatória',
                        validate: (value) =>
                          value.includes('{nome}') || 'A mensagem deve conter {nome} para personalização'
                      })}
                      error={!!errors.message_template}
                      helperText={errors.message_template?.message || 'Use {nome} para personalizar com o nome do contato'}
                      disabled={!!currentJob}
                    />

                    <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                      <Button
                        variant="outlined"
                        startIcon={<PreviewIcon />}
                        onClick={handlePreviewMessage}
                        disabled={!messageTemplate.trim()}
                      >
                        Visualizar
                      </Button>

                      <Button
                        variant="outlined"
                        startIcon={<AutoAwesomeIcon />}
                        onClick={handleGenerateVariation}
                        disabled={!messageTemplate.trim() || generateVariationMutation.isLoading}
                      >
                        {generateVariationMutation.isLoading ? 'Gerando...' : 'Gerar Variação'}
                      </Button>
                    </Box>

                    {/* Variações geradas */}
                    {messageVariations.length > 0 && (
                      <Card variant="outlined" sx={{ mt: 3 }}>
                        <CardContent>
                          <Typography variant="subtitle1" gutterBottom>
                            Variações Geradas
                          </Typography>
                          {messageVariations.map((variation, index) => (
                            <Accordion key={index}>
                              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                <Typography variant="body2">
                                  Variação {index + 1}
                                </Typography>
                              </AccordionSummary>
                              <AccordionDetails>
                                <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                                  {variation}
                                </Typography>
                                <Button
                                  size="small"
                                  onClick={() => setValue('message_template', variation)}
                                  sx={{ mt: 1 }}
                                >
                                  Usar esta variação
                                </Button>
                              </AccordionDetails>
                            </Accordion>
                          ))}
                        </CardContent>
                      </Card>
                    )}
                  </TabPanel>

                  {/* Tab 3: Envio */}
                  <TabPanel value={tabValue} index={2}>
                    <Typography variant="h6" gutterBottom>
                      Configurações de Envio
                    </Typography>

                    <TextField
                      fullWidth
                      label="Intervalo entre mensagens (segundos)"
                      type="number"
                      margin="normal"
                      {...register('interval', {
                        required: 'Intervalo é obrigatório',
                        min: { value: 10, message: 'Mínimo 10 segundos' },
                        max: { value: 300, message: 'Máximo 300 segundos' },
                      })}
                      error={!!errors.interval}
                      helperText={errors.interval?.message || 'Recomendado: 30-60 segundos para evitar bloqueios'}
                      disabled={!!currentJob}
                    />

                    <FormControlLabel
                      control={
                        <Checkbox
                          {...register('use_variations')}
                          disabled={!!currentJob}
                        />
                      }
                      label="Usar variações automáticas da mensagem"
                      sx={{ mt: 2, mb: 2 }}
                    />

                    <Alert severity="warning" sx={{ mt: 2 }}>
                      <Typography variant="body2" fontWeight="bold" gutterBottom>
                        Importante:
                      </Typography>
                      <ul style={{ margin: 0, paddingLeft: 20 }}>
                        <li>Mantenha o WhatsApp Web aberto durante o envio</li>
                        <li>Não feche o navegador até o envio terminar</li>
                        <li>Respeite os limites do WhatsApp para evitar bloqueios</li>
                        <li>Use intervalos adequados entre mensagens</li>
                      </ul>
                    </Alert>

                    <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                      <Button
                        type="submit"
                        variant="contained"
                        startIcon={<WhatsAppIcon />}
                        disabled={!!currentJob || sendMessagesMutation.isLoading}
                        fullWidth
                      >
                        {sendMessagesMutation.isLoading ? 'Iniciando...' : 'Iniciar Envio'}
                      </Button>

                      {currentJob && (
                        <Button
                          variant="outlined"
                          color="error"
                          startIcon={<CancelIcon />}
                          onClick={handleCancel}
                          disabled={cancelSendingMutation.isLoading}
                        >
                          Cancelar
                        </Button>
                      )}
                    </Box>
                  </TabPanel>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Informações e ajuda */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Como usar
                </Typography>

                <Typography variant="body2" paragraph>
                  <strong>1. Adicione contatos:</strong> Faça upload de um arquivo Excel/CSV ou adicione manualmente.
                </Typography>

                <Typography variant="body2" paragraph>
                  <strong>2. Configure a mensagem:</strong> Use {'{nome}'} para personalizar com o nome do contato.
                </Typography>

                <Typography variant="body2" paragraph>
                  <strong>3. Defina o intervalo:</strong> Tempo entre cada mensagem (recomendado: 30-60s).
                </Typography>

                <Typography variant="body2" paragraph>
                  <strong>4. Inicie o envio:</strong> O WhatsApp Web será aberto automaticamente.
                </Typography>

                <Divider sx={{ my: 2 }} />

                <Typography variant="h6" gutterBottom>
                  Templates de Exemplo
                </Typography>

                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="body2">Apresentação de Serviços</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
{`Olá {nome}! 😊

Meu nome é [SEU NOME] e trabalho com [SEU SERVIÇO].

Gostaria de apresentar nossos serviços e verificar se há interesse em uma conversa.

Aguardo seu retorno!

Abraços!`}
                    </Typography>
                    <Button
                      size="small"
                      onClick={() => setValue('message_template', `Olá {nome}! 😊\n\nMeu nome é [SEU NOME] e trabalho com [SEU SERVIÇO].\n\nGostaria de apresentar nossos serviços e verificar se há interesse em uma conversa.\n\nAguardo seu retorno!\n\nAbraços!`)}
                      sx={{ mt: 1 }}
                    >
                      Usar este template
                    </Button>
                  </AccordionDetails>
                </Accordion>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Dialog de preview */}
      <Dialog open={previewDialog} onClose={() => setPreviewDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Preview da Mensagem</DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap', p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
            {getPreviewMessage()}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialog(false)}>Fechar</Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default Messaging
