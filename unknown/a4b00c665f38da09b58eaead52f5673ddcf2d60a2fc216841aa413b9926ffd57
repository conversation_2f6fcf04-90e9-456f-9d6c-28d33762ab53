import { useState, useEffect, useRef, useCallback } from 'react'
import { useSnackbar } from 'notistack'

/**
 * Hook customizado para gerenciar conexões WebSocket
 * @param {string} url - URL do WebSocket
 * @param {Object} options - Opções de configuração
 * @returns {Object} - Estado e funções do WebSocket
 */
export function useWebSocket(url, options = {}) {
  const {
    onMessage,
    onOpen,
    onClose,
    onError,
    reconnectAttempts = 5,
    reconnectInterval = 3000,
    autoConnect = true,
    showNotifications = false,
  } = options

  const [isConnected, setIsConnected] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [error, setError] = useState(null)
  const [lastMessage, setLastMessage] = useState(null)
  
  const wsRef = useRef(null)
  const reconnectTimeoutRef = useRef(null)
  const reconnectCountRef = useRef(0)
  const { enqueueSnackbar } = useSnackbar()

  const connect = useCallback(() => {
    if (!url || wsRef.current?.readyState === WebSocket.OPEN) {
      return
    }

    setIsConnecting(true)
    setError(null)

    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const wsUrl = url.startsWith('ws') ? url : `${protocol}//${window.location.host}${url}`
      
      wsRef.current = new WebSocket(wsUrl)

      wsRef.current.onopen = (event) => {
        setIsConnected(true)
        setIsConnecting(false)
        setError(null)
        reconnectCountRef.current = 0
        
        if (showNotifications) {
          enqueueSnackbar('Conectado ao servidor', { variant: 'success' })
        }
        
        onOpen?.(event)
      }

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          setLastMessage(data)
          onMessage?.(data, event)
        } catch (error) {
          console.error('Erro ao processar mensagem WebSocket:', error)
          setLastMessage(event.data)
          onMessage?.(event.data, event)
        }
      }

      wsRef.current.onclose = (event) => {
        setIsConnected(false)
        setIsConnecting(false)
        
        if (showNotifications && !event.wasClean) {
          enqueueSnackbar('Conexão perdida', { variant: 'warning' })
        }
        
        onClose?.(event)

        // Tentar reconectar se não foi um fechamento intencional
        if (!event.wasClean && reconnectCountRef.current < reconnectAttempts) {
          reconnectCountRef.current++
          
          if (showNotifications) {
            enqueueSnackbar(
              `Tentando reconectar... (${reconnectCountRef.current}/${reconnectAttempts})`,
              { variant: 'info' }
            )
          }
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect()
          }, reconnectInterval)
        }
      }

      wsRef.current.onerror = (event) => {
        const errorMsg = 'Erro na conexão WebSocket'
        setError(errorMsg)
        setIsConnecting(false)
        
        if (showNotifications) {
          enqueueSnackbar(errorMsg, { variant: 'error' })
        }
        
        onError?.(event)
      }

    } catch (error) {
      setError(error.message)
      setIsConnecting(false)
      
      if (showNotifications) {
        enqueueSnackbar(`Erro ao conectar: ${error.message}`, { variant: 'error' })
      }
    }
  }, [url, onMessage, onOpen, onClose, onError, reconnectAttempts, reconnectInterval, showNotifications, enqueueSnackbar])

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }
    
    if (wsRef.current) {
      wsRef.current.close(1000, 'Disconnect requested')
      wsRef.current = null
    }
    
    setIsConnected(false)
    setIsConnecting(false)
    setError(null)
  }, [])

  const sendMessage = useCallback((message) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      const data = typeof message === 'string' ? message : JSON.stringify(message)
      wsRef.current.send(data)
      return true
    }
    return false
  }, [])

  const sendPing = useCallback(() => {
    return sendMessage({ type: 'ping', timestamp: Date.now() })
  }, [sendMessage])

  // Auto-conectar quando o hook é montado
  useEffect(() => {
    if (autoConnect && url) {
      connect()
    }

    return () => {
      disconnect()
    }
  }, [url, autoConnect, connect, disconnect])

  // Ping periódico para manter conexão viva
  useEffect(() => {
    if (!isConnected) return

    const pingInterval = setInterval(() => {
      sendPing()
    }, 30000) // Ping a cada 30 segundos

    return () => clearInterval(pingInterval)
  }, [isConnected, sendPing])

  return {
    isConnected,
    isConnecting,
    error,
    lastMessage,
    connect,
    disconnect,
    sendMessage,
    sendPing,
  }
}

/**
 * Hook específico para WebSocket de jobs
 * @param {string} jobId - ID do job
 * @returns {Object} - Estado e funções específicas para jobs
 */
export function useJobWebSocket(jobId) {
  const [logs, setLogs] = useState([])
  const [progress, setProgress] = useState(0)
  const [status, setStatus] = useState(null)

  const handleMessage = useCallback((data) => {
    switch (data.type) {
      case 'log':
        setLogs(prev => [...prev.slice(-49), data.data]) // Manter últimos 50 logs
        break
      case 'progress':
        setProgress(data.progress)
        break
      case 'status_change':
        setStatus(data.status)
        break
      default:
        console.log('Mensagem WebSocket não reconhecida:', data)
    }
  }, [])

  const websocket = useWebSocket(
    jobId ? `/api/jobs/ws/${jobId}` : null,
    {
      onMessage: handleMessage,
      showNotifications: false,
      autoConnect: !!jobId,
    }
  )

  const clearLogs = useCallback(() => {
    setLogs([])
  }, [])

  return {
    ...websocket,
    logs,
    progress,
    status,
    clearLogs,
  }
}

/**
 * Hook para WebSocket global de todos os jobs
 * @returns {Object} - Estado e funções para jobs globais
 */
export function useGlobalJobsWebSocket() {
  const [jobs, setJobs] = useState([])
  const [notifications, setNotifications] = useState([])

  const handleMessage = useCallback((data) => {
    switch (data.type) {
      case 'jobs_list':
        setJobs(data.data)
        break
      case 'job_update':
        setJobs(prev => {
          const jobIndex = prev.findIndex(job => job.job_id === data.data.job_id)
          if (jobIndex >= 0) {
            const newJobs = [...prev]
            newJobs[jobIndex] = { ...newJobs[jobIndex], ...data.data }
            return newJobs
          }
          return prev
        })
        break
      case 'job_completed':
        setNotifications(prev => [...prev, {
          id: Date.now(),
          type: 'success',
          message: `Job ${data.job_id} concluído com sucesso!`,
          timestamp: new Date(),
        }])
        break
      case 'job_failed':
        setNotifications(prev => [...prev, {
          id: Date.now(),
          type: 'error',
          message: `Job ${data.job_id} falhou: ${data.error}`,
          timestamp: new Date(),
        }])
        break
      default:
        console.log('Mensagem WebSocket global não reconhecida:', data)
    }
  }, [])

  const websocket = useWebSocket('/api/jobs/ws/all', {
    onMessage: handleMessage,
    showNotifications: true,
    autoConnect: true,
  })

  const clearNotifications = useCallback(() => {
    setNotifications([])
  }, [])

  const removeNotification = useCallback((id) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id))
  }, [])

  return {
    ...websocket,
    jobs,
    notifications,
    clearNotifications,
    removeNotification,
  }
}
