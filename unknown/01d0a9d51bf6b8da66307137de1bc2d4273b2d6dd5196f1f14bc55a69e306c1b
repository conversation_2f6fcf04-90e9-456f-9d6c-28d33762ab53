import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useWebSocket } from '../useWebSocket'
import io from 'socket.io-client'

// Mock do socket.io-client
vi.mock('socket.io-client')

describe('useWebSocket', () => {
  let mockSocket

  beforeEach(() => {
    mockSocket = {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
      connect: vi.fn(),
      disconnect: vi.fn(),
      connected: false,
      id: 'test-socket-id',
    }

    io.mockReturnValue(mockSocket)
    vi.clearAllMocks()
  })

  describe('Inicialização', () => {
    it('deve conectar ao WebSocket na inicialização', () => {
      renderHook(() => useWebSocket())

      expect(io).toHaveBeenCalledWith('http://localhost:8000', {
        transports: ['websocket'],
        autoConnect: true,
      })
    })

    it('deve configurar listeners de eventos básicos', () => {
      renderHook(() => useWebSocket())

      expect(mockSocket.on).toHaveBeenCalledWith('connect', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('disconnect', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('error', expect.any(Function))
    })

    it('deve retornar estado inicial correto', () => {
      const { result } = renderHook(() => useWebSocket())

      expect(result.current.isConnected).toBe(false)
      expect(result.current.lastMessage).toBe(null)
      expect(result.current.error).toBe(null)
      expect(typeof result.current.sendMessage).toBe('function')
      expect(typeof result.current.subscribe).toBe('function')
      expect(typeof result.current.unsubscribe).toBe('function')
    })
  })

  describe('Estados de conexão', () => {
    it('deve atualizar isConnected quando conectar', () => {
      const { result } = renderHook(() => useWebSocket())

      // Simular evento de conexão
      const connectHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'connect'
      )[1]

      act(() => {
        mockSocket.connected = true
        connectHandler()
      })

      expect(result.current.isConnected).toBe(true)
      expect(result.current.error).toBe(null)
    })

    it('deve atualizar isConnected quando desconectar', () => {
      const { result } = renderHook(() => useWebSocket())

      // Primeiro conectar
      act(() => {
        mockSocket.connected = true
        const connectHandler = mockSocket.on.mock.calls.find(
          call => call[0] === 'connect'
        )[1]
        connectHandler()
      })

      // Depois desconectar
      act(() => {
        mockSocket.connected = false
        const disconnectHandler = mockSocket.on.mock.calls.find(
          call => call[0] === 'disconnect'
        )[1]
        disconnectHandler('transport close')
      })

      expect(result.current.isConnected).toBe(false)
    })

    it('deve capturar erros de conexão', () => {
      const { result } = renderHook(() => useWebSocket())

      const errorHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'error'
      )[1]

      const testError = new Error('Connection failed')

      act(() => {
        errorHandler(testError)
      })

      expect(result.current.error).toBe(testError)
    })
  })

  describe('Envio de mensagens', () => {
    it('deve enviar mensagem através do socket', () => {
      const { result } = renderHook(() => useWebSocket())

      const testMessage = { type: 'test', data: 'hello' }

      act(() => {
        result.current.sendMessage('test_event', testMessage)
      })

      expect(mockSocket.emit).toHaveBeenCalledWith('test_event', testMessage)
    })

    it('deve lidar com erro ao enviar mensagem quando desconectado', () => {
      const { result } = renderHook(() => useWebSocket())

      mockSocket.connected = false

      act(() => {
        result.current.sendMessage('test_event', { data: 'test' })
      })

      // Deve tentar enviar mesmo assim (socket.io lida com buffer)
      expect(mockSocket.emit).toHaveBeenCalled()
    })
  })

  describe('Subscrição de eventos', () => {
    it('deve permitir subscrever a eventos customizados', () => {
      const { result } = renderHook(() => useWebSocket())

      const callback = vi.fn()

      act(() => {
        result.current.subscribe('job_update', callback)
      })

      expect(mockSocket.on).toHaveBeenCalledWith('job_update', callback)
    })

    it('deve permitir cancelar subscrição de eventos', () => {
      const { result } = renderHook(() => useWebSocket())

      const callback = vi.fn()

      act(() => {
        result.current.subscribe('job_update', callback)
        result.current.unsubscribe('job_update', callback)
      })

      expect(mockSocket.off).toHaveBeenCalledWith('job_update', callback)
    })

    it('deve atualizar lastMessage quando receber mensagem', () => {
      const { result } = renderHook(() => useWebSocket())

      const callback = vi.fn()
      
      act(() => {
        result.current.subscribe('job_update', callback)
      })

      const testMessage = { type: 'job_update', data: { progress: 50 } }

      // Simular recebimento de mensagem
      const jobUpdateHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'job_update'
      )[1]

      act(() => {
        jobUpdateHandler(testMessage)
      })

      expect(callback).toHaveBeenCalledWith(testMessage)
      expect(result.current.lastMessage).toEqual({
        event: 'job_update',
        data: testMessage,
        timestamp: expect.any(Number),
      })
    })
  })

  describe('Cleanup', () => {
    it('deve desconectar socket ao desmontar', () => {
      const { unmount } = renderHook(() => useWebSocket())

      unmount()

      expect(mockSocket.disconnect).toHaveBeenCalled()
    })

    it('deve remover todos os listeners ao desmontar', () => {
      const { result, unmount } = renderHook(() => useWebSocket())

      const callback1 = vi.fn()
      const callback2 = vi.fn()

      act(() => {
        result.current.subscribe('event1', callback1)
        result.current.subscribe('event2', callback2)
      })

      unmount()

      expect(mockSocket.off).toHaveBeenCalledWith('event1', callback1)
      expect(mockSocket.off).toHaveBeenCalledWith('event2', callback2)
    })
  })

  describe('Reconexão', () => {
    it('deve tentar reconectar automaticamente', () => {
      renderHook(() => useWebSocket())

      // Simular desconexão
      const disconnectHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'disconnect'
      )[1]

      act(() => {
        disconnectHandler('transport close')
      })

      // Socket.io deve tentar reconectar automaticamente
      expect(mockSocket.connect).not.toHaveBeenCalled() // Reconexão automática é interna
    })
  })

  describe('Eventos específicos do TeemoFlow', () => {
    it('deve lidar com eventos de job_progress', () => {
      const { result } = renderHook(() => useWebSocket())

      const callback = vi.fn()

      act(() => {
        result.current.subscribe('job_progress', callback)
      })

      const progressData = {
        job_id: 'test-job',
        progress: 75,
        total: 100,
        status: 'running',
      }

      const progressHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'job_progress'
      )[1]

      act(() => {
        progressHandler(progressData)
      })

      expect(callback).toHaveBeenCalledWith(progressData)
    })

    it('deve lidar com eventos de job_complete', () => {
      const { result } = renderHook(() => useWebSocket())

      const callback = vi.fn()

      act(() => {
        result.current.subscribe('job_complete', callback)
      })

      const completeData = {
        job_id: 'test-job',
        status: 'completed',
        results_count: 50,
        export_file: 'results.xlsx',
      }

      const completeHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'job_complete'
      )[1]

      act(() => {
        completeHandler(completeData)
      })

      expect(callback).toHaveBeenCalledWith(completeData)
    })

    it('deve lidar com eventos de notification', () => {
      const { result } = renderHook(() => useWebSocket())

      const callback = vi.fn()

      act(() => {
        result.current.subscribe('notification', callback)
      })

      const notificationData = {
        type: 'success',
        message: 'Operação concluída com sucesso',
        timestamp: Date.now(),
      }

      const notificationHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'notification'
      )[1]

      act(() => {
        notificationHandler(notificationData)
      })

      expect(callback).toHaveBeenCalledWith(notificationData)
    })
  })
})
