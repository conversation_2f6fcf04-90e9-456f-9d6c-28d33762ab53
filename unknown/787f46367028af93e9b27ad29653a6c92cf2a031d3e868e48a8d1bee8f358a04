"""
Serviços para gerenciamento de arquivos
"""
import os
import aiofiles
import pandas as pd
from typing import List, Dict, Any
from fastapi import UploadFile, HTTPException
from app.core.config import settings


async def save_upload_file(file: UploadFile) -> str:
    """Salva um arquivo de upload"""
    try:
        # Criar diretório se não existir
        os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
        
        # Gerar nome único para o arquivo
        import uuid
        file_extension = os.path.splitext(file.filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(settings.UPLOAD_DIR, unique_filename)
        
        # Salvar arquivo
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        return file_path
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao salvar arquivo: {str(e)}"
        )


async def process_contacts_file(file: UploadFile) -> Dict[str, Any]:
    """Processa um arquivo de contatos e retorna informações"""
    try:
        # Salvar arquivo temporariamente
        file_path = await save_upload_file(file)
        
        # Ler arquivo baseado na extensão
        if file.filename.lower().endswith('.csv'):
            df = pd.read_csv(file_path)
        else:  # Excel
            df = pd.read_excel(file_path)
        
        # Validar colunas necessárias
        required_columns = ['nome', 'telefone']
        missing_columns = [col for col in required_columns if col not in df.columns.str.lower()]
        
        if missing_columns:
            # Tentar mapear colunas comuns
            column_mapping = {
                'name': 'nome',
                'phone': 'telefone',
                'telefone': 'telefone',
                'nome': 'nome',
                'contato': 'nome',
                'empresa': 'nome'
            }
            
            # Renomear colunas se possível
            df.columns = df.columns.str.lower()
            for old_col, new_col in column_mapping.items():
                if old_col in df.columns:
                    df.rename(columns={old_col: new_col}, inplace=True)
            
            # Verificar novamente
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise HTTPException(
                    status_code=400,
                    detail=f"Arquivo deve conter as colunas: {', '.join(required_columns)}"
                )
        
        # Limpar dados
        df = df.dropna(subset=['nome', 'telefone'])
        df['telefone'] = df['telefone'].astype(str).str.replace(r'[^\d]', '', regex=True)
        
        # Filtrar telefones válidos (pelo menos 10 dígitos)
        df = df[df['telefone'].str.len() >= 10]
        
        contacts_count = len(df)
        
        return {
            "filename": file.filename,
            "file_path": file_path,
            "size": file.size,
            "contacts_count": contacts_count
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao processar arquivo de contatos: {str(e)}"
        )


def list_export_files(directory: str = None) -> List[Dict[str, Any]]:
    """Lista arquivos em um diretório"""
    if directory is None:
        directory = settings.EXPORT_DIR
    
    try:
        if not os.path.exists(directory):
            return []
        
        files = []
        for filename in os.listdir(directory):
            file_path = os.path.join(directory, filename)
            if os.path.isfile(file_path):
                stat = os.stat(file_path)
                files.append({
                    "filename": filename,
                    "size": stat.st_size,
                    "created_at": stat.st_ctime,
                    "modified_at": stat.st_mtime
                })
        
        # Ordenar por data de modificação (mais recentes primeiro)
        files.sort(key=lambda x: x["modified_at"], reverse=True)
        
        return files
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao listar arquivos: {str(e)}"
        )


def delete_file(filename: str, directory: str) -> bool:
    """Remove um arquivo"""
    try:
        file_path = os.path.join(directory, filename)
        
        # Verificar se o arquivo existe
        if not os.path.exists(file_path):
            return False
        
        # Verificar se está dentro do diretório permitido
        if not os.path.abspath(file_path).startswith(os.path.abspath(directory)):
            return False
        
        os.remove(file_path)
        return True
        
    except Exception:
        return False


def export_leads_to_file(leads: List[Dict[str, Any]], filename: str, file_format: str = "excel") -> str:
    """Exporta leads para arquivo Excel ou CSV"""
    try:
        # Criar diretório se não existir
        os.makedirs(settings.EXPORT_DIR, exist_ok=True)
        
        # Criar DataFrame
        df = pd.DataFrame(leads)
        
        # Definir caminho do arquivo
        if file_format.lower() == "csv":
            if not filename.endswith('.csv'):
                filename += '.csv'
            file_path = os.path.join(settings.EXPORT_DIR, filename)
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
        else:  # Excel
            if not filename.endswith('.xlsx'):
                filename += '.xlsx'
            file_path = os.path.join(settings.EXPORT_DIR, filename)
            df.to_excel(file_path, index=False, engine='openpyxl')
        
        return file_path
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao exportar leads: {str(e)}"
        )


def read_contacts_from_file(file_path: str) -> List[Dict[str, str]]:
    """Lê contatos de um arquivo"""
    try:
        # Ler arquivo baseado na extensão
        if file_path.lower().endswith('.csv'):
            df = pd.read_csv(file_path)
        else:  # Excel
            df = pd.read_excel(file_path)
        
        # Normalizar nomes das colunas
        df.columns = df.columns.str.lower()
        
        # Mapear colunas
        column_mapping = {
            'name': 'nome',
            'phone': 'telefone',
            'telefone': 'telefone',
            'nome': 'nome',
            'contato': 'nome',
            'empresa': 'nome'
        }
        
        for old_col, new_col in column_mapping.items():
            if old_col in df.columns:
                df.rename(columns={old_col: new_col}, inplace=True)
        
        # Verificar colunas necessárias
        if 'nome' not in df.columns or 'telefone' not in df.columns:
            raise ValueError("Arquivo deve conter colunas 'nome' e 'telefone'")
        
        # Limpar dados
        df = df.dropna(subset=['nome', 'telefone'])
        df['telefone'] = df['telefone'].astype(str).str.replace(r'[^\d]', '', regex=True)
        df = df[df['telefone'].str.len() >= 10]
        
        # Converter para lista de dicionários
        contacts = []
        for _, row in df.iterrows():
            contacts.append({
                "name": str(row['nome']),
                "phone": str(row['telefone'])
            })
        
        return contacts
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao ler contatos do arquivo: {str(e)}"
        )
