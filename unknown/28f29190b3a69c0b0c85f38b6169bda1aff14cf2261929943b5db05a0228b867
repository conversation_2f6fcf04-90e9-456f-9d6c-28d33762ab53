import { describe, it, expect, beforeEach, vi } from 'vitest'
import axios from 'axios'
import { 
  api, 
  cepSearchApi, 
  flexibleSearchApi, 
  gmapsSearchApi, 
  automatedSearchApi,
  messagingApi,
  jobsApi,
  filesApi 
} from '../api'

// Mock do axios
vi.mock('axios')

describe('API Service', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock das respostas padrão do axios
    axios.create.mockReturnValue({
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      delete: vi.fn(),
      interceptors: {
        request: { use: vi.fn() },
        response: { use: vi.fn() },
      },
    })
  })

  describe('Configuração da API', () => {
    it('deve criar instância do axios com configuração correta', () => {
      expect(axios.create).toHaveBeenCalledWith({
        baseURL: 'http://localhost:8000/api',
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
        },
      })
    })

    it('deve configurar interceptors', () => {
      const mockAxiosInstance = axios.create.mockReturnValue({
        interceptors: {
          request: { use: vi.fn() },
          response: { use: vi.fn() },
        },
      })

      // Verificar se interceptors foram configurados
      expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalled()
      expect(mockAxiosInstance.interceptors.response.use).toHaveBeenCalled()
    })
  })

  describe('CEP Search API', () => {
    let mockApi

    beforeEach(() => {
      mockApi = {
        post: vi.fn(),
        get: vi.fn(),
      }
      axios.create.mockReturnValue(mockApi)
    })

    it('deve iniciar busca por CEP', async () => {
      const searchData = {
        cep: '01310-100',
        keyword: 'restaurante',
        max_results: 50,
        headless: true,
      }

      const mockResponse = { data: { job_id: 'test-job-1' } }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await cepSearchApi.search(searchData)

      expect(mockApi.post).toHaveBeenCalledWith('/cep-search', searchData)
      expect(result).toEqual(mockResponse.data)
    })

    it('deve obter resultados da busca por CEP', async () => {
      const jobId = 'test-job-1'
      const mockResponse = { data: { results: [] } }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await cepSearchApi.getResults(jobId)

      expect(mockApi.get).toHaveBeenCalledWith(`/cep-search/${jobId}/results`)
      expect(result).toEqual(mockResponse.data)
    })
  })

  describe('Flexible Search API', () => {
    let mockApi

    beforeEach(() => {
      mockApi = {
        post: vi.fn(),
        get: vi.fn(),
      }
      axios.create.mockReturnValue(mockApi)
    })

    it('deve iniciar busca flexível', async () => {
      const searchData = {
        location_type: 'city',
        location: 'São Paulo, SP',
        business_type: 'restaurante',
        radius: 5000,
        max_results: 100,
      }

      const mockResponse = { data: { job_id: 'test-job-2' } }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await flexibleSearchApi.search(searchData)

      expect(mockApi.post).toHaveBeenCalledWith('/flexible-search', searchData)
      expect(result).toEqual(mockResponse.data)
    })

    it('deve obter sugestões de negócios', async () => {
      const query = 'rest'
      const mockResponse = { data: { suggestions: ['restaurante', 'restô'] } }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await flexibleSearchApi.getSuggestions(query)

      expect(mockApi.get).toHaveBeenCalledWith('/flexible-search/suggestions', {
        params: { q: query }
      })
      expect(result).toEqual(mockResponse.data)
    })
  })

  describe('Google Maps Search API', () => {
    let mockApi

    beforeEach(() => {
      mockApi = {
        post: vi.fn(),
        get: vi.fn(),
      }
      axios.create.mockReturnValue(mockApi)
    })

    it('deve iniciar busca no Google Maps', async () => {
      const searchData = {
        search_term: 'restaurante em São Paulo',
        location: 'São Paulo, SP',
        max_results: 50,
      }

      const mockResponse = { data: { job_id: 'test-job-3' } }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await gmapsSearchApi.search(searchData)

      expect(mockApi.post).toHaveBeenCalledWith('/gmaps-search', searchData)
      expect(result).toEqual(mockResponse.data)
    })
  })

  describe('Automated Search API', () => {
    let mockApi

    beforeEach(() => {
      mockApi = {
        post: vi.fn(),
        get: vi.fn(),
      }
      axios.create.mockReturnValue(mockApi)
    })

    it('deve iniciar busca automatizada', async () => {
      const searchData = {
        searches: [
          { type: 'flexible', location: 'São Paulo', business_type: 'restaurante' },
          { type: 'cep', cep: '01310-100', keyword: 'loja' },
        ],
        export_format: 'xlsx',
      }

      const mockResponse = { data: { job_id: 'test-job-4' } }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await automatedSearchApi.start(searchData)

      expect(mockApi.post).toHaveBeenCalledWith('/automated-search', searchData)
      expect(result).toEqual(mockResponse.data)
    })
  })

  describe('Messaging API', () => {
    let mockApi

    beforeEach(() => {
      mockApi = {
        post: vi.fn(),
        get: vi.fn(),
      }
      axios.create.mockReturnValue(mockApi)
    })

    it('deve iniciar campanha de mensagens', async () => {
      const campaignData = {
        contacts: [
          { name: 'João', phone: '***********' },
          { name: 'Maria', phone: '***********' },
        ],
        message_template: 'Olá {name}, tudo bem?',
        interval: 5,
      }

      const mockResponse = { data: { job_id: 'test-job-5' } }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await messagingApi.startCampaign(campaignData)

      expect(mockApi.post).toHaveBeenCalledWith('/messaging/campaign', campaignData)
      expect(result).toEqual(mockResponse.data)
    })

    it('deve gerar variações de mensagem', async () => {
      const messageData = {
        base_message: 'Olá, como vai?',
        count: 3,
      }

      const mockResponse = { 
        data: { 
          variations: [
            'Olá, como vai?',
            'Oi, tudo bem?',
            'E aí, beleza?'
          ] 
        } 
      }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await messagingApi.generateVariations(messageData)

      expect(mockApi.post).toHaveBeenCalledWith('/messaging/generate-variations', messageData)
      expect(result).toEqual(mockResponse.data)
    })

    it('deve obter templates de mensagem', async () => {
      const mockResponse = { 
        data: { 
          templates: [
            { id: 1, name: 'Saudação', content: 'Olá {name}!' },
            { id: 2, name: 'Promoção', content: 'Oferta especial para você!' },
          ] 
        } 
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await messagingApi.getTemplates()

      expect(mockApi.get).toHaveBeenCalledWith('/messaging/templates')
      expect(result).toEqual(mockResponse.data)
    })
  })

  describe('Jobs API', () => {
    let mockApi

    beforeEach(() => {
      mockApi = {
        get: vi.fn(),
        post: vi.fn(),
        delete: vi.fn(),
      }
      axios.create.mockReturnValue(mockApi)
    })

    it('deve listar jobs', async () => {
      const mockResponse = { 
        data: { 
          jobs: [
            { id: 'job-1', type: 'flexible_search', status: 'completed' },
            { id: 'job-2', type: 'cep_search', status: 'running' },
          ] 
        } 
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await jobsApi.list()

      expect(mockApi.get).toHaveBeenCalledWith('/jobs')
      expect(result).toEqual(mockResponse.data)
    })

    it('deve obter detalhes de um job', async () => {
      const jobId = 'test-job-1'
      const mockResponse = { 
        data: { 
          id: jobId, 
          type: 'flexible_search', 
          status: 'completed',
          results: []
        } 
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await jobsApi.get(jobId)

      expect(mockApi.get).toHaveBeenCalledWith(`/jobs/${jobId}`)
      expect(result).toEqual(mockResponse.data)
    })

    it('deve cancelar job', async () => {
      const jobId = 'test-job-1'
      const mockResponse = { data: { success: true } }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await jobsApi.cancel(jobId)

      expect(mockApi.post).toHaveBeenCalledWith(`/jobs/${jobId}/cancel`)
      expect(result).toEqual(mockResponse.data)
    })

    it('deve pausar job', async () => {
      const jobId = 'test-job-1'
      const mockResponse = { data: { success: true } }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await jobsApi.pause(jobId)

      expect(mockApi.post).toHaveBeenCalledWith(`/jobs/${jobId}/pause`)
      expect(result).toEqual(mockResponse.data)
    })

    it('deve retomar job', async () => {
      const jobId = 'test-job-1'
      const mockResponse = { data: { success: true } }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await jobsApi.resume(jobId)

      expect(mockApi.post).toHaveBeenCalledWith(`/jobs/${jobId}/resume`)
      expect(result).toEqual(mockResponse.data)
    })

    it('deve deletar job', async () => {
      const jobId = 'test-job-1'
      const mockResponse = { data: { success: true } }
      mockApi.delete.mockResolvedValue(mockResponse)

      const result = await jobsApi.delete(jobId)

      expect(mockApi.delete).toHaveBeenCalledWith(`/jobs/${jobId}`)
      expect(result).toEqual(mockResponse.data)
    })
  })

  describe('Files API', () => {
    let mockApi

    beforeEach(() => {
      mockApi = {
        get: vi.fn(),
        post: vi.fn(),
        delete: vi.fn(),
      }
      axios.create.mockReturnValue(mockApi)
    })

    it('deve listar arquivos', async () => {
      const fileType = 'exports'
      const mockResponse = { 
        data: { 
          files: [
            { id: 'file-1', name: 'export1.xlsx', type: 'export' },
            { id: 'file-2', name: 'export2.csv', type: 'export' },
          ] 
        } 
      }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await filesApi.list(fileType)

      expect(mockApi.get).toHaveBeenCalledWith('/files', {
        params: { type: fileType }
      })
      expect(result).toEqual(mockResponse.data)
    })

    it('deve fazer upload de arquivo', async () => {
      const file = new File(['test'], 'test.xlsx')
      const mockResponse = { data: { file_id: 'uploaded-file-1' } }
      mockApi.post.mockResolvedValue(mockResponse)

      const result = await filesApi.upload(file)

      expect(mockApi.post).toHaveBeenCalledWith('/files/upload', expect.any(FormData), {
        headers: { 'Content-Type': 'multipart/form-data' }
      })
      expect(result).toEqual(mockResponse.data)
    })

    it('deve fazer download de arquivo', async () => {
      const fileId = 'test-file-1'
      const mockResponse = { data: new Blob(['file content']) }
      mockApi.get.mockResolvedValue(mockResponse)

      const result = await filesApi.download(fileId)

      expect(mockApi.get).toHaveBeenCalledWith(`/files/${fileId}/download`, {
        responseType: 'blob'
      })
      expect(result).toEqual(mockResponse.data)
    })

    it('deve deletar arquivo', async () => {
      const fileId = 'test-file-1'
      const mockResponse = { data: { success: true } }
      mockApi.delete.mockResolvedValue(mockResponse)

      const result = await filesApi.delete(fileId)

      expect(mockApi.delete).toHaveBeenCalledWith(`/files/${fileId}`)
      expect(result).toEqual(mockResponse.data)
    })
  })

  describe('Tratamento de erros', () => {
    let mockApi

    beforeEach(() => {
      mockApi = {
        get: vi.fn(),
        post: vi.fn(),
      }
      axios.create.mockReturnValue(mockApi)
    })

    it('deve propagar erros de rede', async () => {
      const networkError = new Error('Network Error')
      mockApi.get.mockRejectedValue(networkError)

      await expect(jobsApi.list()).rejects.toThrow('Network Error')
    })

    it('deve propagar erros de API', async () => {
      const apiError = {
        response: {
          status: 400,
          data: { detail: 'Bad Request' }
        }
      }
      mockApi.post.mockRejectedValue(apiError)

      await expect(cepSearchApi.search({})).rejects.toEqual(apiError)
    })
  })
})
