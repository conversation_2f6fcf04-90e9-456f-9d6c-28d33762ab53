import React from 'react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { renderWithProviders, mockThemeContext } from '../../../test/utils'
import ThemeToggle from '../ThemeToggle'
import { useTheme } from '../../../contexts/ThemeContext'

// Mock do contexto de tema
vi.mock('../../../contexts/ThemeContext', () => ({
  useTheme: vi.fn(),
}))

describe('ThemeToggle', () => {
  const user = userEvent.setup()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Renderização', () => {
    it('deve renderizar botão de toggle', () => {
      useTheme.mockReturnValue({
        ...mockThemeContext,
        isDarkMode: false,
      })

      renderWithProviders(<ThemeToggle />)

      const button = screen.getByRole('button')
      expect(button).toBeInTheDocument()
    })

    it('deve mostrar ícone de lua quando em modo claro', () => {
      useTheme.mockReturnValue({
        ...mockThemeContext,
        isDarkMode: false,
      })

      renderWithProviders(<ThemeToggle />)

      // Ícone Brightness4 (lua) deve estar presente
      const moonIcon = screen.getByTestId('Brightness4Icon')
      expect(moonIcon).toBeInTheDocument()
    })

    it('deve mostrar ícone de sol quando em modo escuro', () => {
      useTheme.mockReturnValue({
        ...mockThemeContext,
        isDarkMode: true,
      })

      renderWithProviders(<ThemeToggle />)

      // Ícone Brightness7 (sol) deve estar presente
      const sunIcon = screen.getByTestId('Brightness7Icon')
      expect(sunIcon).toBeInTheDocument()
    })

    it('deve mostrar tooltip correto para modo claro', () => {
      useTheme.mockReturnValue({
        ...mockThemeContext,
        isDarkMode: false,
      })

      renderWithProviders(<ThemeToggle />)

      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('aria-describedby')
      
      // Verificar se o tooltip tem o texto correto
      expect(button.getAttribute('title') || button.getAttribute('aria-label')).toMatch(/modo escuro/i)
    })

    it('deve mostrar tooltip correto para modo escuro', () => {
      useTheme.mockReturnValue({
        ...mockThemeContext,
        isDarkMode: true,
      })

      renderWithProviders(<ThemeToggle />)

      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('aria-describedby')
      
      // Verificar se o tooltip tem o texto correto
      expect(button.getAttribute('title') || button.getAttribute('aria-label')).toMatch(/modo claro/i)
    })
  })

  describe('Interação', () => {
    it('deve chamar toggleTheme ao clicar', async () => {
      const mockToggleTheme = vi.fn()
      
      useTheme.mockReturnValue({
        ...mockThemeContext,
        isDarkMode: false,
        toggleTheme: mockToggleTheme,
      })

      renderWithProviders(<ThemeToggle />)

      const button = screen.getByRole('button')
      await user.click(button)

      expect(mockToggleTheme).toHaveBeenCalledTimes(1)
    })

    it('deve ser acessível via teclado', async () => {
      const mockToggleTheme = vi.fn()
      
      useTheme.mockReturnValue({
        ...mockThemeContext,
        isDarkMode: false,
        toggleTheme: mockToggleTheme,
      })

      renderWithProviders(<ThemeToggle />)

      const button = screen.getByRole('button')
      
      // Focar no botão
      button.focus()
      expect(button).toHaveFocus()

      // Pressionar Enter
      await user.keyboard('{Enter}')
      expect(mockToggleTheme).toHaveBeenCalledTimes(1)

      // Pressionar Space
      await user.keyboard(' ')
      expect(mockToggleTheme).toHaveBeenCalledTimes(2)
    })

    it('deve ter atributos de acessibilidade corretos', () => {
      useTheme.mockReturnValue({
        ...mockThemeContext,
        isDarkMode: false,
      })

      renderWithProviders(<ThemeToggle />)

      const button = screen.getByRole('button')
      
      expect(button).toHaveAttribute('type', 'button')
      expect(button).toHaveAttribute('aria-describedby')
    })
  })

  describe('Estados visuais', () => {
    it('deve aplicar estilos corretos do Material-UI', () => {
      useTheme.mockReturnValue({
        ...mockThemeContext,
        isDarkMode: false,
      })

      renderWithProviders(<ThemeToggle />)

      const button = screen.getByRole('button')
      
      // Verificar se tem classes do Material-UI
      expect(button).toHaveClass('MuiIconButton-root')
    })

    it('deve ter margem esquerda aplicada', () => {
      useTheme.mockReturnValue({
        ...mockThemeContext,
        isDarkMode: false,
      })

      renderWithProviders(<ThemeToggle />)

      const button = screen.getByRole('button')
      
      // Verificar se o estilo sx={{ ml: 1 }} foi aplicado
      const computedStyle = window.getComputedStyle(button)
      expect(computedStyle.marginLeft).toBeTruthy()
    })

    it('deve herdar cor do tema', () => {
      useTheme.mockReturnValue({
        ...mockThemeContext,
        isDarkMode: false,
      })

      renderWithProviders(<ThemeToggle />)

      const button = screen.getByRole('button')
      
      // Verificar se tem a propriedade color="inherit"
      expect(button).toHaveClass('MuiIconButton-colorInherit')
    })
  })

  describe('Tooltip', () => {
    it('deve mostrar tooltip ao fazer hover', async () => {
      useTheme.mockReturnValue({
        ...mockThemeContext,
        isDarkMode: false,
      })

      renderWithProviders(<ThemeToggle />)

      const button = screen.getByRole('button')
      
      // Fazer hover
      await user.hover(button)
      
      // Aguardar tooltip aparecer
      const tooltip = await screen.findByRole('tooltip')
      expect(tooltip).toBeInTheDocument()
      expect(tooltip).toHaveTextContent('Modo escuro')
    })

    it('deve esconder tooltip ao sair do hover', async () => {
      useTheme.mockReturnValue({
        ...mockThemeContext,
        isDarkMode: false,
      })

      renderWithProviders(<ThemeToggle />)

      const button = screen.getByRole('button')
      
      // Fazer hover e depois unhover
      await user.hover(button)
      await user.unhover(button)
      
      // Tooltip deve desaparecer
      expect(screen.queryByRole('tooltip')).not.toBeInTheDocument()
    })

    it('deve mostrar tooltip correto para cada modo', async () => {
      // Teste modo claro
      useTheme.mockReturnValue({
        ...mockThemeContext,
        isDarkMode: false,
      })

      const { rerender } = renderWithProviders(<ThemeToggle />)

      let button = screen.getByRole('button')
      await user.hover(button)
      
      let tooltip = await screen.findByRole('tooltip')
      expect(tooltip).toHaveTextContent('Modo escuro')
      
      await user.unhover(button)

      // Teste modo escuro
      useTheme.mockReturnValue({
        ...mockThemeContext,
        isDarkMode: true,
      })

      rerender(<ThemeToggle />)

      button = screen.getByRole('button')
      await user.hover(button)
      
      tooltip = await screen.findByRole('tooltip')
      expect(tooltip).toHaveTextContent('Modo claro')
    })
  })

  describe('Integração com contexto', () => {
    it('deve reagir a mudanças no contexto de tema', () => {
      const { rerender } = renderWithProviders(<ThemeToggle />)

      // Iniciar com modo claro
      useTheme.mockReturnValue({
        ...mockThemeContext,
        isDarkMode: false,
      })

      rerender(<ThemeToggle />)
      expect(screen.getByTestId('Brightness4Icon')).toBeInTheDocument()

      // Mudar para modo escuro
      useTheme.mockReturnValue({
        ...mockThemeContext,
        isDarkMode: true,
      })

      rerender(<ThemeToggle />)
      expect(screen.getByTestId('Brightness7Icon')).toBeInTheDocument()
    })

    it('deve lidar com contexto não disponível', () => {
      useTheme.mockImplementation(() => {
        throw new Error('useTheme deve ser usado dentro de um ThemeProvider')
      })

      // Capturar erro do console
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      expect(() => {
        renderWithProviders(<ThemeToggle />)
      }).toThrow()

      consoleSpy.mockRestore()
    })
  })
})
