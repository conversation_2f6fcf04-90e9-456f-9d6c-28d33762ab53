#!/usr/bin/env node
/**
 * Script para executar verificações de qualidade de código no frontend
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// Cores para output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
}

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`
}

function runCommand(command, description, fixCommand = null) {
  console.log('\n' + '='.repeat(60))
  console.log(colorize(`🔍 ${description}`, 'cyan'))
  console.log('='.repeat(60))

  try {
    const output = execSync(command, { 
      encoding: 'utf8',
      stdio: 'pipe'
    })
    
    if (output.trim()) {
      console.log(output)
    }
    
    console.log(colorize(`✅ ${description} - PASSOU`, 'green'))
    return true
  } catch (error) {
    console.log(colorize(`❌ ${description} - FALHOU`, 'red'))
    
    if (error.stdout) {
      console.log('STDOUT:', error.stdout)
    }
    if (error.stderr) {
      console.log('STDERR:', error.stderr)
    }
    
    if (fixCommand) {
      console.log(colorize(`💡 Para corrigir, execute: ${fixCommand}`, 'yellow'))
    }
    
    return false
  }
}

function checkNodeModules() {
  if (!fs.existsSync('node_modules')) {
    console.log(colorize('❌ node_modules não encontrado!', 'red'))
    console.log(colorize('Execute: npm install', 'yellow'))
    process.exit(1)
  }
}

function main() {
  console.log(colorize('🔍 TeemoFlow Frontend - Verificações de Qualidade', 'magenta'))
  console.log('='.repeat(60))

  // Verificar se estamos no diretório correto
  if (!fs.existsSync('package.json')) {
    console.log(colorize('❌ package.json não encontrado!', 'red'))
    console.log('Execute este script a partir do diretório frontend/')
    process.exit(1)
  }

  // Verificar node_modules
  checkNodeModules()

  // Lista de verificações
  const qualityChecks = [
    {
      command: 'npm list eslint --depth=0',
      description: 'Verificando instalação do ESLint'
    },
    {
      command: 'npm list prettier --depth=0',
      description: 'Verificando instalação do Prettier'
    },
    {
      command: 'npm run lint',
      description: 'Verificando código com ESLint',
      fixCommand: 'npm run lint:fix'
    },
    {
      command: 'npm run format:check',
      description: 'Verificando formatação com Prettier',
      fixCommand: 'npm run format'
    },
    {
      command: 'npm run build',
      description: 'Verificando build de produção'
    }
  ]

  // Executar verificações
  let successCount = 0
  const totalCount = qualityChecks.length

  for (const check of qualityChecks) {
    if (runCommand(check.command, check.description, check.fixCommand)) {
      successCount++
    }
  }

  // Resumo final
  console.log('\n' + '='.repeat(60))
  console.log(colorize('📊 RESUMO DAS VERIFICAÇÕES', 'magenta'))
  console.log('='.repeat(60))
  console.log(colorize(`✅ Sucessos: ${successCount}/${totalCount}`, 'green'))
  console.log(colorize(`❌ Falhas: ${totalCount - successCount}/${totalCount}`, 'red'))

  if (successCount === totalCount) {
    console.log(colorize('\n🎉 TODAS AS VERIFICAÇÕES PASSARAM!', 'green'))
    console.log(colorize('✨ Código está seguindo os padrões de qualidade!', 'green'))
    process.exit(0)
  } else {
    console.log(colorize('\n⚠️  ALGUMAS VERIFICAÇÕES FALHARAM!', 'yellow'))
    console.log(colorize('🔧 Execute os comandos de correção sugeridos acima.', 'yellow'))
    process.exit(1)
  }
}

function formatCode() {
  console.log(colorize('🔧 Formatando código automaticamente...', 'cyan'))

  const commands = [
    { command: 'npm run lint:fix', description: 'Corrigindo problemas de ESLint' },
    { command: 'npm run format', description: 'Formatando com Prettier' }
  ]

  for (const { command, description } of commands) {
    console.log(colorize(`\n${description}...`, 'blue'))
    try {
      execSync(command, { stdio: 'inherit' })
      console.log(colorize(`✅ ${description} - Concluído`, 'green'))
    } catch (error) {
      console.log(colorize(`❌ ${description} - Falhou`, 'red'))
    }
  }

  console.log(colorize('\n✨ Formatação concluída!', 'green'))
}

// Verificar argumentos da linha de comando
const args = process.argv.slice(2)

if (args.includes('format')) {
  formatCode()
} else {
  main()
}
