"""
Worker para sistema de mensagens via WhatsApp Web
"""
import time
import urllib.parse
from datetime import datetime
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

from app.workers.base_worker import BaseSeleniumWorker, safe_find_element
from app.models.schemas import JobStatus


class MessageSendingWorker(BaseSeleniumWorker):
    """Worker para envio de mensagens via WhatsApp Web"""
    
    def __init__(self, job_id: str, contacts: list, message_template: str, 
                 interval: int = 30, use_variations: bool = False, headless: bool = False):
        # WhatsApp Web funciona melhor em modo visível
        super().__init__(job_id, headless=False)
        self.contacts = contacts
        self.message_template = message_template
        self.interval = interval
        self.use_variations = use_variations
        self.messages_sent = 0
        self.messages_failed = 0
        self.current_variation_index = 0
        self.message_variations = []
    
    def run(self):
        """Executa o envio de mensagens"""
        try:
            self.set_status(JobStatus.RUNNING)
            self.log_info(f"Iniciando envio de mensagens para {len(self.contacts)} contatos")
            
            # Gerar variações se solicitado
            if self.use_variations:
                self.generate_message_variations()
            
            # Configurar driver
            if not self.setup_driver():
                return
            
            # Abrir WhatsApp Web
            self.log_info("Abrindo WhatsApp Web...")
            self.driver.get('https://web.whatsapp.com/')
            
            # Aguardar login do usuário
            if not self.wait_for_whatsapp_login():
                return
            
            if self.check_cancellation():
                return
            
            # Enviar mensagens
            self.send_messages()
            
            # Finalizar
            self.set_status(JobStatus.COMPLETED)
            self.log_info(f"Envio concluído! {self.messages_sent} enviadas, {self.messages_failed} falharam.")
            
        except Exception as e:
            self.handle_error(e)
        finally:
            self.cleanup_driver()
    
    def wait_for_whatsapp_login(self) -> bool:
        """Aguarda o usuário fazer login no WhatsApp Web"""
        try:
            self.log_info("Aguardando login no WhatsApp Web...")
            self.log_info("Por favor, escaneie o QR Code com seu celular para fazer login")
            
            # Aguardar até 5 minutos pelo login
            timeout = 300  # 5 minutos
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                if self.check_cancellation():
                    return False
                
                try:
                    # Verificar se o QR Code ainda está presente
                    qr_code = self.driver.find_element(By.XPATH, '//canvas[@aria-label="Scan me!"]')
                    if qr_code:
                        remaining_time = int(timeout - (time.time() - start_time))
                        if remaining_time % 30 == 0:  # Log a cada 30 segundos
                            self.log_info(f"Aguardando login... {remaining_time}s restantes")
                        time.sleep(1)
                        continue
                except:
                    pass
                
                try:
                    # Verificar se já logou (presença da barra de pesquisa)
                    search_box = self.driver.find_element(By.XPATH, '//div[@contenteditable="true"][@data-tab="3"]')
                    if search_box:
                        self.log_info("Login realizado com sucesso!")
                        time.sleep(3)  # Aguardar carregar completamente
                        return True
                except:
                    pass
                
                time.sleep(1)
            
            self.log_error("Timeout: Login não foi realizado dentro do tempo limite")
            return False
            
        except Exception as e:
            self.log_error(f"Erro ao aguardar login: {str(e)}")
            return False
    
    def send_messages(self):
        """Envia mensagens para todos os contatos"""
        try:
            total_contacts = len(self.contacts)
            
            for i, contact in enumerate(self.contacts):
                if self.check_cancellation():
                    break
                
                self.wait_if_paused()
                
                # Atualizar progresso
                progress = int((i / total_contacts) * 100)
                self.update_progress(progress)
                
                name = contact.get('name', 'Contato')
                phone = contact.get('phone', '')
                
                self.log_info(f"Enviando mensagem {i+1}/{total_contacts} para {name} ({phone})")
                
                # Validar e formatar telefone
                formatted_phone = self.format_phone(phone)
                if not formatted_phone:
                    self.log_warning(f"Número inválido para {name}: {phone}")
                    self.messages_failed += 1
                    continue
                
                # Personalizar mensagem
                message = self.get_personalized_message(name)
                
                # Enviar mensagem
                if self.send_single_message(formatted_phone, message, name):
                    self.messages_sent += 1
                    self.log_info(f"✅ Mensagem enviada para {name}")
                else:
                    self.messages_failed += 1
                    self.log_warning(f"❌ Falha ao enviar para {name}")
                
                # Aguardar intervalo entre mensagens (exceto na última)
                if i < total_contacts - 1:
                    self.wait_interval()
            
            # Progresso final
            self.update_progress(100)
            
        except Exception as e:
            self.log_error(f"Erro durante envio de mensagens: {str(e)}")
    
    def send_single_message(self, phone: str, message: str, name: str) -> bool:
        """Envia uma mensagem para um contato específico"""
        try:
            # Codificar mensagem para URL
            encoded_message = urllib.parse.quote(message)
            
            # Criar URL do WhatsApp
            url = f"https://web.whatsapp.com/send?phone={phone}&text={encoded_message}"
            
            # Navegar para o URL
            self.driver.get(url)
            time.sleep(5)
            
            # Verificar se o número é válido
            try:
                # Procurar por indicadores de número inválido
                invalid_indicators = [
                    "Phone number shared via url is invalid",
                    "Número de telefone inválido",
                    "Invalid phone number"
                ]
                
                page_text = self.driver.page_source.lower()
                for indicator in invalid_indicators:
                    if indicator.lower() in page_text:
                        self.log_warning(f"Número inválido detectado: {phone}")
                        return False
                        
            except:
                pass
            
            # Aguardar campo de mensagem aparecer
            try:
                message_box = WebDriverWait(self.driver, 15).until(
                    EC.presence_of_element_located((By.XPATH, '//div[@contenteditable="true"][@data-tab="10"]'))
                )
                
                # Verificar se a mensagem já está no campo
                if message_box.text.strip():
                    # Enviar mensagem
                    send_button = self.driver.find_element(By.XPATH, '//span[@data-icon="send"]')
                    send_button.click()
                    
                    # Aguardar confirmação de envio
                    time.sleep(3)
                    
                    return True
                else:
                    self.log_warning(f"Campo de mensagem vazio para {name}")
                    return False
                    
            except TimeoutException:
                self.log_warning(f"Timeout ao aguardar campo de mensagem para {name}")
                return False
                
        except Exception as e:
            self.log_warning(f"Erro ao enviar mensagem para {name}: {str(e)}")
            return False
    
    def format_phone(self, phone: str) -> str:
        """Formata número de telefone para WhatsApp"""
        try:
            # Remover caracteres não numéricos
            clean_phone = ''.join(filter(str.isdigit, phone))
            
            # Verificar se tem pelo menos 10 dígitos
            if len(clean_phone) < 10:
                return None
            
            # Adicionar código do país se não estiver presente
            if not clean_phone.startswith('55'):
                clean_phone = '55' + clean_phone
            
            # Verificar formato brasileiro
            if len(clean_phone) == 13:  # 55 + 11 dígitos (celular com 9)
                return clean_phone
            elif len(clean_phone) == 12:  # 55 + 10 dígitos (fixo ou celular antigo)
                return clean_phone
            
            return None
            
        except:
            return None
    
    def get_personalized_message(self, name: str) -> str:
        """Obtém mensagem personalizada"""
        if self.use_variations and self.message_variations:
            # Usar variação atual
            template = self.message_variations[self.current_variation_index]
            self.current_variation_index = (self.current_variation_index + 1) % len(self.message_variations)
        else:
            template = self.message_template
        
        # Personalizar com nome
        return template.format(nome=name)
    
    def generate_message_variations(self):
        """Gera variações da mensagem"""
        try:
            from app.services.message_generator import generate_message_variations
            
            self.log_info("Gerando variações da mensagem...")
            variations = generate_message_variations(self.message_template, count=5)
            
            if variations:
                self.message_variations = variations
                self.log_info(f"Geradas {len(variations)} variações da mensagem")
            else:
                self.log_warning("Não foi possível gerar variações, usando mensagem original")
                self.message_variations = [self.message_template]
                
        except Exception as e:
            self.log_warning(f"Erro ao gerar variações: {str(e)}")
            self.message_variations = [self.message_template]
    
    def wait_interval(self):
        """Aguarda intervalo entre mensagens"""
        for remaining in range(self.interval, 0, -1):
            if self.check_cancellation() or self.paused:
                break
            
            if remaining % 10 == 0 or remaining <= 5:
                self.log_info(f"Aguardando {remaining}s para próxima mensagem...")
            
            time.sleep(1)
        
        self.wait_if_paused()


async def start_message_sending(job_id: str, contacts: list, message_template: str, 
                               interval: int = 30, use_variations: bool = False):
    """Função para iniciar envio de mensagens em background"""
    worker = MessageSendingWorker(job_id, contacts, message_template, interval, use_variations)
    worker.run()
