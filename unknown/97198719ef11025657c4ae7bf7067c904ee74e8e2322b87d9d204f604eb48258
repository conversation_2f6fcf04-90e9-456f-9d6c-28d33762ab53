"""
Testes para o JobManager
"""
import pytest
from datetime import datetime
from unittest.mock import patch, Mock

from app.services.job_manager import JobManager
from app.models.schemas import JobStatus, LogLevel


@pytest.mark.unit
class TestJobManager:
    """Testes unitários para JobManager"""

    def test_create_job(self):
        """Testa criação de job"""
        manager = JobManager()

        job = manager.create_job("test-job-123", "test_type", {"param": "value"})

        assert job is not None
        assert job.job_id == "test-job-123"
        assert "test-job-123" in manager._jobs

        stored_job = manager._jobs["test-job-123"]
        assert stored_job.status == JobStatus.PENDING
        assert stored_job.progress == 0
        assert stored_job.created_at is not None
        assert len(stored_job.logs) > 0  # Deve ter log inicial

    def test_get_job_existing(self):
        """Testa busca de job existente"""
        manager = JobManager()
        manager.create_job("test-job-456", "test_type", {})

        job = manager.get_job("test-job-456")

        assert job is not None
        assert job.job_id == "test-job-456"
        assert job.status == JobStatus.PENDING

    def test_get_job_nonexistent(self):
        """Testa busca de job inexistente"""
        manager = JobManager()

        job = manager.get_job("nonexistent-job")

        assert job is None

    def test_list_jobs_empty(self):
        """Testa listagem de jobs vazia"""
        manager = JobManager()

        jobs = manager.list_jobs()

        assert jobs == []

    def test_list_jobs_with_data(self):
        """Testa listagem de jobs com dados"""
        manager = JobManager()
        manager.create_job("job-1", "type1", {})
        manager.create_job("job-2", "type2", {})

        jobs = manager.list_jobs()

        assert len(jobs) == 2
        job_ids = [job.job_id for job in jobs]
        assert "job-1" in job_ids
        assert "job-2" in job_ids

    def test_update_job_status_existing(self):
        """Testa atualização de status de job existente"""
        manager = JobManager()
        manager.create_job("test-job-789", "test_type", {})

        result = manager.update_job_status("test-job-789", JobStatus.RUNNING)

        assert result is True
        job = manager.get_job("test-job-789")
        assert job.status == JobStatus.RUNNING
        assert job.started_at is not None

    def test_update_job_status_to_completed(self):
        """Testa atualização de status para completed"""
        manager = JobManager()
        manager.create_job("test-job-completed", "test_type", {})

        result = manager.update_job_status("test-job-completed", JobStatus.COMPLETED)

        assert result is True
        job = manager.get_job("test-job-completed")
        assert job.status == JobStatus.COMPLETED
        assert job.completed_at is not None

    def test_update_job_status_nonexistent(self):
        """Testa atualização de status de job inexistente"""
        manager = JobManager()

        result = manager.update_job_status("nonexistent-job", JobStatus.RUNNING)

        assert result is False

    def test_update_job_progress_existing(self):
        """Testa atualização de progresso de job existente"""
        manager = JobManager()
        manager.create_job("test-job-progress", "test_type", {})

        result = manager.update_job_progress("test-job-progress", 50)

        assert result is True
        job = manager.get_job("test-job-progress")
        assert job.progress == 50

    def test_update_job_progress_nonexistent(self):
        """Testa atualização de progresso de job inexistente"""
        manager = JobManager()

        result = manager.update_job_progress("nonexistent-job", 50)

        assert result is False

    def test_set_job_result_file_existing(self):
        """Testa definição de arquivo de resultado"""
        manager = JobManager()
        manager.create_job("test-job-result", "test_type", {})

        result = manager.set_job_result_file("test-job-result", "/path/to/result.xlsx")

        assert result is True
        job = manager.get_job("test-job-result")
        assert job.result_file == "/path/to/result.xlsx"

    def test_set_job_result_file_nonexistent(self):
        """Testa definição de arquivo de resultado para job inexistente"""
        manager = JobManager()

        result = manager.set_job_result_file("nonexistent-job", "/path/to/result.xlsx")

        assert result is False

    def test_set_job_error_existing(self):
        """Testa definição de erro de job"""
        manager = JobManager()
        manager.create_job("test-job-error", "test_type", {})

        result = manager.set_job_error("test-job-error", "Test error message")

        assert result is True
        job = manager.get_job("test-job-error")
        assert job.error_message == "Test error message"

    def test_set_job_error_nonexistent(self):
        """Testa definição de erro para job inexistente"""
        manager = JobManager()

        result = manager.set_job_error("nonexistent-job", "Test error")

        assert result is False

    def test_add_log_existing_job(self):
        """Testa adição de log para job existente"""
        manager = JobManager()
        manager.create_job("test-job-log", "test_type", {})

        result = manager.add_log("test-job-log", LogLevel.INFO, "Test log message")

        assert result is True
        job = manager.get_job("test-job-log")
        assert len(job.logs) >= 1  # Pelo menos o log inicial + o novo
        # Verificar se o log foi adicionado (pode não ser o primeiro devido ao log inicial)
        log_messages = [log["message"] for log in job.logs]
        assert "Test log message" in log_messages

    def test_add_log_nonexistent_job(self):
        """Testa adição de log para job inexistente"""
        manager = JobManager()

        result = manager.add_log("nonexistent-job", LogLevel.INFO, "Test log")

        assert result is False

    def test_add_log_limit(self):
        """Testa limite de logs (máximo 1000)"""
        manager = JobManager()
        manager.create_job("test-job-limit", "test_type", {})

        # Adicionar mais de 1000 logs
        for i in range(1005):
            manager.add_log("test-job-limit", LogLevel.INFO, f"Log message {i}")

        job = manager.get_job("test-job-limit")
        assert len(job.logs) == 1000  # Deve manter apenas os últimos 1000
        # Verificar se os logs mais recentes foram mantidos
        log_messages = [log["message"] for log in job.logs]
        assert "Log message 1004" in log_messages  # Último log deve estar presente

    def test_cancel_job_existing(self):
        """Testa cancelamento de job existente"""
        manager = JobManager()
        manager.create_job("test-job-cancel", "test_type", {})

        result = manager.cancel_job("test-job-cancel")

        assert result is True
        job = manager.get_job("test-job-cancel")
        assert job.status == JobStatus.CANCELLED
        assert job.completed_at is not None
        assert len(job.logs) > 0
        log_messages = [log["message"].lower() for log in job.logs]
        assert any("cancelado" in msg for msg in log_messages)

    def test_cancel_job_nonexistent(self):
        """Testa cancelamento de job inexistente"""
        manager = JobManager()

        result = manager.cancel_job("nonexistent-job")

        assert result is False

    def test_cancel_job_already_completed(self):
        """Testa cancelamento de job já concluído"""
        manager = JobManager()
        manager.create_job("test-job-completed-cancel", "test_type", {})
        manager.update_job_status("test-job-completed-cancel", JobStatus.COMPLETED)

        result = manager.cancel_job("test-job-completed-cancel")

        assert result is False

    def test_pause_job_existing(self):
        """Testa pausa de job existente"""
        manager = JobManager()
        manager.create_job("test-job-pause", "test_type", {})

        result = manager.pause_job("test-job-pause")

        assert result is True
        job = manager.get_job("test-job-pause")
        assert any("PAUSE_REQUESTED" in log["message"] for log in job.logs)

    def test_resume_job_existing(self):
        """Testa retomada de job existente"""
        manager = JobManager()
        manager.create_job("test-job-resume", "test_type", {})

        result = manager.resume_job("test-job-resume")

        assert result is True
        job = manager.get_job("test-job-resume")
        assert any("RESUME_REQUESTED" in log["message"] for log in job.logs)

    def test_delete_job_completed(self):
        """Testa exclusão de job concluído"""
        manager = JobManager()
        manager.create_job("test-job-delete-ok", "test_type", {})
        manager.update_job_status("test-job-delete-ok", JobStatus.COMPLETED)

        result = manager.delete_job("test-job-delete-ok")

        assert result is True
        assert manager.get_job("test-job-delete-ok") is None

    def test_delete_job_running(self):
        """Testa exclusão de job em execução (não deve permitir)"""
        manager = JobManager()
        manager.create_job("test-job-delete-fail", "test_type", {})
        manager.update_job_status("test-job-delete-fail", JobStatus.RUNNING)

        result = manager.delete_job("test-job-delete-fail")

        assert result is False
        assert manager.get_job("test-job-delete-fail") is not None

    @patch('app.services.job_manager.websocket_manager')
    def test_websocket_notification_on_log(self, mock_websocket_manager):
        """Testa notificação WebSocket ao adicionar log"""
        manager = JobManager()
        manager.create_job("test-job-websocket", "test_type", {})

        manager.add_log("test-job-websocket", LogLevel.INFO, "Test message")

        # Verifica se o WebSocket foi notificado
        # Note: Como é assíncrono, apenas verificamos se não houve erro
        assert True  # Se chegou aqui, não houve erro na notificação
