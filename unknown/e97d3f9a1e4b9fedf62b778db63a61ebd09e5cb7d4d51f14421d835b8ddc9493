"""
Gerenciador de Jobs do TeemoFlow
"""
import json
import threading
from datetime import datetime
from typing import Dict, List, Optional, Any
from app.models.schemas import JobInfo, JobStatus, LogLevel


class JobManager:
    """Gerenciador centralizado de jobs"""

    def __init__(self):
        self._jobs: Dict[str, JobInfo] = {}
        self._lock = threading.Lock()

    def create_job(self, job_id: str, job_type: str, parameters: Dict[str, Any]) -> JobInfo:
        """Cria um novo job"""
        with self._lock:
            job = JobInfo(
                job_id=job_id,
                status=JobStatus.PENDING,
                progress=0,
                created_at=datetime.now().isoformat(),
                started_at=None,
                completed_at=None,
                result_file=None,
                error_message=None,
                logs=[]
            )

            # Adicionar log inicial
            self.add_log(job_id, LogLevel.INFO, f"Job {job_type} criado com parâmetros: {parameters}")

            self._jobs[job_id] = job
            return job

    def get_job(self, job_id: str) -> Optional[JobInfo]:
        """Obtém um job pelo ID"""
        with self._lock:
            return self._jobs.get(job_id)

    def list_jobs(self, limit: int = 50, offset: int = 0) -> List[JobInfo]:
        """Lista jobs com paginação"""
        with self._lock:
            jobs = list(self._jobs.values())
            # Ordenar por data de criação (mais recentes primeiro)
            jobs.sort(key=lambda x: x.created_at, reverse=True)
            return jobs[offset:offset + limit]

    def count_jobs(self) -> int:
        """Conta o total de jobs"""
        with self._lock:
            return len(self._jobs)

    def update_job_status(self, job_id: str, status: JobStatus) -> bool:
        """Atualiza o status de um job"""
        with self._lock:
            if job_id not in self._jobs:
                return False

            job = self._jobs[job_id]
            old_status = job.status
            job.status = status

            if status == JobStatus.RUNNING and not job.started_at:
                job.started_at = datetime.now().isoformat()
            elif status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
                job.completed_at = datetime.now().isoformat()

            # Notificar WebSocket sobre mudança de status
            if old_status != status:
                self._notify_status_change(job_id, status)

                # Enviar notificação especial para jobs concluídos ou com falha
                if status == JobStatus.COMPLETED:
                    self._notify_job_completion(job_id, True)
                elif status == JobStatus.FAILED:
                    self._notify_job_completion(job_id, False, job.error_message)

            return True

    def update_job_progress(self, job_id: str, progress: int) -> bool:
        """Atualiza o progresso de um job"""
        with self._lock:
            if job_id not in self._jobs:
                return False

            self._jobs[job_id].progress = max(0, min(100, progress))
            return True

    def set_job_result_file(self, job_id: str, file_path: str) -> bool:
        """Define o arquivo de resultado de um job"""
        with self._lock:
            if job_id not in self._jobs:
                return False

            self._jobs[job_id].result_file = file_path
            return True

    def set_job_error(self, job_id: str, error_message: str) -> bool:
        """Define uma mensagem de erro para um job"""
        with self._lock:
            if job_id not in self._jobs:
                return False

            self._jobs[job_id].error_message = error_message
            self._jobs[job_id].status = JobStatus.FAILED
            self._jobs[job_id].completed_at = datetime.now().isoformat()

            # Adicionar log de erro
            self.add_log(job_id, LogLevel.ERROR, error_message)

            return True

    def add_log(self, job_id: str, level: LogLevel, message: str) -> bool:
        """Adiciona um log a um job"""
        with self._lock:
            if job_id not in self._jobs:
                return False

            log_entry = {
                "level": level.value,
                "message": message,
                "timestamp": datetime.now().isoformat()
            }

            self._jobs[job_id].logs.append(log_entry)

            # Limitar número de logs (manter apenas os últimos 1000)
            if len(self._jobs[job_id].logs) > 1000:
                self._jobs[job_id].logs = self._jobs[job_id].logs[-1000:]

            # Notificar WebSocket se disponível
            self._notify_websocket(job_id, log_entry)

            return True

    def cancel_job(self, job_id: str) -> bool:
        """Cancela um job"""
        with self._lock:
            if job_id not in self._jobs:
                return False

            job = self._jobs[job_id]
            if job.status not in [JobStatus.PENDING, JobStatus.RUNNING]:
                return False

            job.status = JobStatus.CANCELLED
            job.completed_at = datetime.now().isoformat()

            self.add_log(job_id, LogLevel.WARNING, "Job cancelado pelo usuário")

            return True

    def pause_job(self, job_id: str) -> bool:
        """Pausa um job (implementação específica nos workers)"""
        with self._lock:
            if job_id not in self._jobs:
                return False

            # Adicionar flag de pausa nos logs para os workers verificarem
            self.add_log(job_id, LogLevel.INFO, "PAUSE_REQUESTED")

            return True

    def resume_job(self, job_id: str) -> bool:
        """Retoma um job pausado"""
        with self._lock:
            if job_id not in self._jobs:
                return False

            # Adicionar flag de retomada nos logs
            self.add_log(job_id, LogLevel.INFO, "RESUME_REQUESTED")

            return True

    def delete_job(self, job_id: str) -> bool:
        """Remove um job do sistema"""
        with self._lock:
            if job_id not in self._jobs:
                return False

            # Só permitir remoção de jobs finalizados
            job = self._jobs[job_id]
            if job.status in [JobStatus.PENDING, JobStatus.RUNNING]:
                return False

            del self._jobs[job_id]
            return True

    def _notify_websocket(self, job_id: str, log_entry: Dict[str, Any]):
        """Notifica WebSocket sobre novo log (implementado no websocket_manager)"""
        try:
            from app.services.websocket_manager import websocket_manager
            import asyncio
            try:
                loop = asyncio.get_event_loop()
                loop.create_task(websocket_manager.broadcast_log(job_id, log_entry))
            except RuntimeError:
                # Se não há loop rodando, ignorar
                pass
        except ImportError:
            # WebSocket manager não disponível
            pass

    def _notify_status_change(self, job_id: str, status: JobStatus):
        """Notifica WebSocket sobre mudança de status"""
        try:
            from app.services.websocket_manager import websocket_manager
            import asyncio
            try:
                loop = asyncio.get_event_loop()
                loop.create_task(websocket_manager.broadcast_status_change(job_id, status.value))
            except RuntimeError:
                pass
        except ImportError:
            pass

    def _notify_job_completion(self, job_id: str, success: bool, error_message: str = None):
        """Notifica WebSocket sobre conclusão de job"""
        try:
            from app.services.websocket_manager import websocket_manager
            import asyncio
            try:
                loop = asyncio.get_event_loop()
                if success:
                    loop.create_task(websocket_manager.broadcast_job_completion(job_id, True))
                else:
                    loop.create_task(websocket_manager.broadcast_job_completion(job_id, False, error_message))
            except RuntimeError:
                pass
        except ImportError:
            pass


# Instância global do gerenciador de jobs
job_manager = JobManager()
