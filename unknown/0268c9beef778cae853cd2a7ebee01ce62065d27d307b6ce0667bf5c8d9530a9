import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  <PERSON>pography,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  List,
  ListItem,
  ListItemText,
  Pagination,
} from '@mui/material'
import {
  Refresh as RefreshIcon,
  Delete as DeleteIcon,
  Cancel as CancelIcon,
  Download as DownloadIcon,
  Visibility as VisibilityIcon,
  FilterList as FilterListIcon,
} from '@mui/icons-material'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useSnackbar } from 'notistack'
import { jobsApi, filesApi } from '../services/api'

function Jobs() {
  const [page, setPage] = useState(1)
  const [statusFilter, setStatusFilter] = useState('all')
  const [selectedJob, setSelectedJob] = useState(null)
  const [logsDialog, setLogsDialog] = useState(false)
  const queryClient = useQueryClient()
  const { enqueueSnackbar } = useSnackbar()

  const limit = 10

  // Buscar jobs
  const { data: jobsData, isLoading, refetch } = useQuery(
    ['jobs', page, statusFilter],
    () => jobsApi.list({
      limit,
      offset: (page - 1) * limit,
      status: statusFilter !== 'all' ? statusFilter : undefined
    }),
    {
      refetchInterval: 5000, // Atualizar a cada 5 segundos
    }
  )

  // Mutation para cancelar job
  const cancelJobMutation = useMutation(
    (jobId) => jobsApi.cancel(jobId),
    {
      onSuccess: () => {
        enqueueSnackbar('Job cancelado com sucesso!', { variant: 'success' })
        refetch()
      },
      onError: (error) => {
        enqueueSnackbar(`Erro ao cancelar job: ${error.message}`, { variant: 'error' })
      },
    }
  )

  // Mutation para deletar job
  const deleteJobMutation = useMutation(
    (jobId) => jobsApi.delete(jobId),
    {
      onSuccess: () => {
        enqueueSnackbar('Job removido com sucesso!', { variant: 'success' })
        refetch()
      },
      onError: (error) => {
        enqueueSnackbar(`Erro ao remover job: ${error.message}`, { variant: 'error' })
      },
    }
  )

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success'
      case 'running':
        return 'info'
      case 'failed':
        return 'error'
      case 'cancelled':
        return 'default'
      default:
        return 'warning'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'pending':
        return 'Pendente'
      case 'running':
        return 'Executando'
      case 'completed':
        return 'Concluído'
      case 'failed':
        return 'Falhou'
      case 'cancelled':
        return 'Cancelado'
      default:
        return status
    }
  }

  const getJobTypeText = (jobId) => {
    // Inferir tipo do job baseado nos parâmetros (simplificado)
    return 'Busca de Leads'
  }

  const handleViewLogs = (job) => {
    setSelectedJob(job)
    setLogsDialog(true)
  }

  const handleDownload = async (job) => {
    if (!job.result_file) {
      enqueueSnackbar('Nenhum arquivo de resultado disponível', { variant: 'warning' })
      return
    }

    try {
      const filename = job.result_file.split('/').pop()
      const response = await filesApi.download(filename)

      // Criar URL para download
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', filename)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)

      enqueueSnackbar('Download iniciado!', { variant: 'success' })
    } catch (error) {
      enqueueSnackbar(`Erro no download: ${error.message}`, { variant: 'error' })
    }
  }

  const handleCancelJob = (jobId) => {
    if (window.confirm('Tem certeza que deseja cancelar este job?')) {
      cancelJobMutation.mutate(jobId)
    }
  }

  const handleDeleteJob = (jobId) => {
    if (window.confirm('Tem certeza que deseja remover este job? Esta ação não pode ser desfeita.')) {
      deleteJobMutation.mutate(jobId)
    }
  }

  const totalPages = Math.ceil((jobsData?.total || 0) / limit)

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Gerenciamento de Jobs
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Visualize e gerencie todos os jobs em execução
        </Typography>
      </Box>

      {/* Filtros e controles */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  label="Status"
                  onChange={(e) => {
                    setStatusFilter(e.target.value)
                    setPage(1)
                  }}
                >
                  <MenuItem value="all">Todos</MenuItem>
                  <MenuItem value="pending">Pendente</MenuItem>
                  <MenuItem value="running">Executando</MenuItem>
                  <MenuItem value="completed">Concluído</MenuItem>
                  <MenuItem value="failed">Falhou</MenuItem>
                  <MenuItem value="cancelled">Cancelado</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => refetch()}
                disabled={isLoading}
                fullWidth
              >
                Atualizar
              </Button>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="body2" color="text.secondary">
                Total: {jobsData?.total || 0} jobs
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Tabela de jobs */}
      <Card>
        <CardContent>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>ID</TableCell>
                  <TableCell>Tipo</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Progresso</TableCell>
                  <TableCell>Criado em</TableCell>
                  <TableCell>Ações</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={6} sx={{ textAlign: 'center', py: 4 }}>
                      Carregando jobs...
                    </TableCell>
                  </TableRow>
                ) : jobsData?.jobs?.length > 0 ? (
                  jobsData.jobs.map((job) => (
                    <TableRow key={job.job_id}>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {job.job_id.slice(0, 8)}...
                        </Typography>
                      </TableCell>

                      <TableCell>
                        {getJobTypeText(job.job_id)}
                      </TableCell>

                      <TableCell>
                        <Chip
                          label={getStatusText(job.status)}
                          color={getStatusColor(job.status)}
                          size="small"
                        />
                      </TableCell>

                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <LinearProgress
                            variant="determinate"
                            value={job.progress}
                            sx={{ flexGrow: 1, height: 6, borderRadius: 3 }}
                          />
                          <Typography variant="body2" color="text.secondary">
                            {job.progress}%
                          </Typography>
                        </Box>
                      </TableCell>

                      <TableCell>
                        <Typography variant="body2">
                          {new Date(job.created_at).toLocaleString()}
                        </Typography>
                      </TableCell>

                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                          <IconButton
                            size="small"
                            onClick={() => handleViewLogs(job)}
                            title="Ver logs"
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>

                          {job.result_file && (
                            <IconButton
                              size="small"
                              onClick={() => handleDownload(job)}
                              title="Download"
                            >
                              <DownloadIcon fontSize="small" />
                            </IconButton>
                          )}

                          {job.status === 'running' && (
                            <IconButton
                              size="small"
                              onClick={() => handleCancelJob(job.job_id)}
                              title="Cancelar"
                              color="warning"
                            >
                              <CancelIcon fontSize="small" />
                            </IconButton>
                          )}

                          {['completed', 'failed', 'cancelled'].includes(job.status) && (
                            <IconButton
                              size="small"
                              onClick={() => handleDeleteJob(job.job_id)}
                              title="Remover"
                              color="error"
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          )}
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} sx={{ textAlign: 'center', py: 4 }}>
                      Nenhum job encontrado
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Paginação */}
          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={(e, value) => setPage(value)}
                color="primary"
              />
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Dialog de logs */}
      <Dialog
        open={logsDialog}
        onClose={() => setLogsDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Logs do Job {selectedJob?.job_id.slice(0, 8)}...
        </DialogTitle>
        <DialogContent>
          {selectedJob && (
            <Box>
              {/* Informações do job */}
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Status: <Chip label={getStatusText(selectedJob.status)} color={getStatusColor(selectedJob.status)} size="small" />
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Progresso: {selectedJob.progress}%
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Criado: {new Date(selectedJob.created_at).toLocaleString()}
                  </Typography>
                </Grid>
                {selectedJob.completed_at && (
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Concluído: {new Date(selectedJob.completed_at).toLocaleString()}
                    </Typography>
                  </Grid>
                )}
              </Grid>

              {/* Logs */}
              <Typography variant="h6" gutterBottom>
                Logs
              </Typography>

              <Paper variant="outlined" sx={{ maxHeight: 400, overflow: 'auto' }}>
                {selectedJob.logs?.length > 0 ? (
                  <List dense>
                    {selectedJob.logs.map((log, index) => (
                      <ListItem key={index}>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Chip
                                label={log.level}
                                size="small"
                                color={
                                  log.level === 'error' ? 'error' :
                                  log.level === 'warning' ? 'warning' :
                                  log.level === 'success' ? 'success' : 'info'
                                }
                                sx={{ minWidth: 60 }}
                              />
                              <Typography variant="body2">
                                {log.message}
                              </Typography>
                            </Box>
                          }
                          secondary={new Date(log.timestamp).toLocaleString()}
                        />
                      </ListItem>
                    ))}
                  </List>
                ) : (
                  <Box sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      Nenhum log disponível
                    </Typography>
                  </Box>
                )}
              </Paper>

              {/* Erro */}
              {selectedJob.error_message && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="h6" color="error" gutterBottom>
                    Erro
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 2, bgcolor: 'error.light', color: 'error.contrastText' }}>
                    <Typography variant="body2">
                      {selectedJob.error_message}
                    </Typography>
                  </Paper>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setLogsDialog(false)}>Fechar</Button>
          {selectedJob?.result_file && (
            <Button
              variant="contained"
              startIcon={<DownloadIcon />}
              onClick={() => {
                handleDownload(selectedJob)
                setLogsDialog(false)
              }}
            >
              Download
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default Jobs
