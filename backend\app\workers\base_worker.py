"""
Worker base para operações com Selenium
"""
import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from app.services.job_manager import job_manager
from app.models.schemas import JobStatus, LogLevel

logger = logging.getLogger(__name__)


class BaseSeleniumWorker:
    """Classe base para workers que usam Selenium"""
    
    def __init__(self, job_id: str, headless: bool = True):
        self.job_id = job_id
        self.headless = headless
        self.driver = None
        self.cancelled = False
        self.paused = False
    
    def setup_driver(self):
        """Configura o driver do Chrome"""
        try:
            self.log_info("Configurando Chrome WebDriver...")
            
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--log-level=3")
            
            if self.headless:
                chrome_options.add_argument("--headless")
                self.log_info("Modo headless ativado")
            else:
                self.log_info("Modo visível ativado")
            
            # Instalar/atualizar ChromeDriver
            self.log_info("Instalando/atualizando ChromeDriver...")
            service = Service(ChromeDriverManager().install())
            
            # Criar driver
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.log_info("Chrome WebDriver configurado com sucesso")
            
            return True
            
        except Exception as e:
            error_msg = f"Erro ao configurar Chrome WebDriver: {str(e)}"
            self.log_error(error_msg)
            job_manager.set_job_error(self.job_id, error_msg)
            return False
    
    def cleanup_driver(self):
        """Limpa recursos do driver"""
        if self.driver:
            try:
                self.log_info("Fechando navegador...")
                self.driver.quit()
                self.driver = None
            except Exception as e:
                self.log_warning(f"Erro ao fechar navegador: {str(e)}")
    
    def check_cancellation(self) -> bool:
        """Verifica se o job foi cancelado"""
        job = job_manager.get_job(self.job_id)
        if job and job.status == JobStatus.CANCELLED:
            self.cancelled = True
            return True
        
        # Verificar logs para comandos de controle
        if job:
            for log in job.logs[-5:]:  # Verificar últimos 5 logs
                if log.get("message") == "PAUSE_REQUESTED":
                    self.paused = True
                    self.log_info("Operação pausada pelo usuário")
                elif log.get("message") == "RESUME_REQUESTED":
                    self.paused = False
                    self.log_info("Operação retomada pelo usuário")
        
        return self.cancelled
    
    def wait_if_paused(self):
        """Aguarda enquanto estiver pausado"""
        while self.paused and not self.cancelled:
            time.sleep(1)
            self.check_cancellation()
    
    def log_info(self, message: str):
        """Adiciona log de informação"""
        logger.info(f"[{self.job_id}] {message}")
        job_manager.add_log(self.job_id, LogLevel.INFO, message)
    
    def log_warning(self, message: str):
        """Adiciona log de aviso"""
        logger.warning(f"[{self.job_id}] {message}")
        job_manager.add_log(self.job_id, LogLevel.WARNING, message)
    
    def log_error(self, message: str):
        """Adiciona log de erro"""
        logger.error(f"[{self.job_id}] {message}")
        job_manager.add_log(self.job_id, LogLevel.ERROR, message)
    
    def update_progress(self, progress: int):
        """Atualiza progresso do job"""
        job_manager.update_job_progress(self.job_id, progress)
    
    def set_status(self, status: JobStatus):
        """Atualiza status do job"""
        job_manager.update_job_status(self.job_id, status)
    
    def set_result_file(self, file_path: str):
        """Define arquivo de resultado"""
        job_manager.set_job_result_file(self.job_id, file_path)
    
    def handle_error(self, error: Exception):
        """Trata erros do worker"""
        error_msg = f"Erro durante execução: {str(error)}"
        self.log_error(error_msg)
        job_manager.set_job_error(self.job_id, error_msg)
        self.cleanup_driver()


def safe_find_element(driver, by, value, timeout=10):
    """Encontra elemento de forma segura com timeout"""
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    
    try:
        element = WebDriverWait(driver, timeout).until(
            EC.presence_of_element_located((by, value))
        )
        return element
    except Exception:
        return None


def safe_click(driver, element):
    """Clica em elemento de forma segura"""
    try:
        # Tentar scroll até o elemento
        driver.execute_script("arguments[0].scrollIntoView(true);", element)
        time.sleep(0.5)
        
        # Tentar clicar
        element.click()
        return True
    except Exception:
        try:
            # Tentar clicar via JavaScript
            driver.execute_script("arguments[0].click();", element)
            return True
        except Exception:
            return False
