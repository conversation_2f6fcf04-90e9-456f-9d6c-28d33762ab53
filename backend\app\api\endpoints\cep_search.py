"""
Endpoint para busca por CEP
"""
import uuid
from fastapi import APIRouter, HTTPException, BackgroundTasks
from app.models.schemas import CEPSearchRequest, CEPSearchResponse
from app.services.job_manager import job_manager
from app.workers.cep_worker import start_cep_search

router = APIRouter()


@router.post("/start", response_model=CEPSearchResponse)
async def start_cep_search_endpoint(
    request: CEPSearchRequest,
    background_tasks: BackgroundTasks
):
    """
    Inicia uma busca por CEP
    """
    try:
        # Validar CEP
        cep_clean = request.cep.replace("-", "").replace(".", "")
        if len(cep_clean) != 8 or not cep_clean.isdigit():
            raise HTTPException(
                status_code=400,
                detail="CEP deve ter 8 dígitos numéricos"
            )
        
        # Gerar ID único para o job
        job_id = str(uuid.uuid4())
        
        # Criar job no gerenciador
        job_manager.create_job(
            job_id=job_id,
            job_type="cep_search",
            parameters={
                "cep": request.cep,
                "palavra_chave": request.palavra_chave,
                "quantidade": request.quantidade,
                "headless": request.headless
            }
        )
        
        # Iniciar worker em background
        background_tasks.add_task(
            start_cep_search,
            job_id=job_id,
            cep=request.cep,
            palavra_chave=request.palavra_chave,
            quantidade=request.quantidade,
            headless=request.headless
        )
        
        return CEPSearchResponse(
            job_id=job_id,
            message=f"Busca por CEP iniciada. Buscando '{request.palavra_chave}' na região do CEP {request.cep}"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao iniciar busca por CEP: {str(e)}"
        )


@router.post("/cancel/{job_id}")
async def cancel_cep_search(job_id: str):
    """
    Cancela uma busca por CEP em andamento
    """
    try:
        success = job_manager.cancel_job(job_id)
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Job não encontrado ou não pode ser cancelado"
            )
        
        return {"message": "Busca cancelada com sucesso"}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao cancelar busca: {str(e)}"
        )
