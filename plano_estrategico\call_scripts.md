# 📞 Scripts de Ligação - TeemoFlow

## Script 1: Ligação de Discovery (Prospecção Inicial)

### 🎯 **Abertura (30 segundos)**

**Vendedor**: "Ol<PERSON>, {{nome}}! Aqui é o {{seu_nome}} da {{empresa}}. Tudo bem? Tenho apenas 2 minutos, posso falar?"

**[Aguardar resposta]**

**Vendedor**: "Perfeito! {{nome}}, descobri que a {{empresa_prospect}} atua com {{setor}} em {{cidade}}, correto? Estou ligando porque desenvolvemos uma ferramenta que está ajudando empresas como a sua a multiplicar a captação de leads. Posso fazer uma pergunta rápida?"

### 🔍 **Qualificação (2-3 minutos)**

**Pergunta 1**: "Como vocês fazem para encontrar novos clientes hoje?"
- **Se responder "indicação/networking"**: "Entendo, indicação é ótimo! E quando as indicações não são suficientes, como vocês fazem?"
- **Se responder "Google Ads/marketing digital"**: "Perfeito! E além do digital, vocês fazem prospecção ativa?"
- **Se responder "não fazemos"**: "Interessante! Então vocês dependem só de clientes que chegam até vocês?"

**Pergunta 2**: "Em média, quantos leads novos vocês precisariam por mês para bater as metas?"
- **Anotar número específico**
- **Se não souber**: "Entendo. E se eu te dissesse que posso entregar 100 leads qualificados por mês, isso seria interessante?"

**Pergunta 3**: "Vocês já tentaram extrair dados de empresas do Google Maps?"
- **Se sim**: "Como foi a experiência? Conseguiram bons resultados?"
- **Se não**: "Faz sentido, é trabalhoso fazer manualmente. E se fosse automático?"

### 💡 **Apresentação da Solução (2 minutos)**

**Vendedor**: "{{nome}}, baseado no que você me falou, acredito que o TeemoFlow pode ajudar muito vocês. Deixe-me explicar rapidamente:

Imagina poder digitar o CEP da região que vocês atendem + a palavra-chave do seu negócio, e em 30 minutos ter uma planilha com:
- Nome de todas as empresas do seu nicho na região
- Telefone de contato
- Endereço completo  
- Website quando disponível
- Avaliações no Google

E mais: o sistema ainda conecta com WhatsApp e envia mensagens personalizadas automaticamente.

Faz sentido para vocês?"

### 🎯 **Fechamento para Demo (1 minuto)**

**Vendedor**: "{{nome}}, em vez de eu ficar explicando por telefone, que tal eu mostrar funcionando? Leva 15 minutos e posso fazer uma busca real do seu segmento em {{cidade}}.

Tenho um horário livre amanhã às {{horario_1}} ou prefere {{horario_2}}?"

**[Se resistência]**: "Olha, sem compromisso nenhum. É só para você ver se faz sentido para a realidade de vocês. Se não fizer, pelo menos você conheceu uma ferramenta nova. Que tal?"

### 📝 **Encerramento**

**Vendedor**: "Perfeito! Então confirmado para {{dia}} às {{horario}}. Vou enviar um link do Google Meet no seu WhatsApp {{telefone}}. Alguma dúvida até lá?"

**[Finalizar]**: "Ótimo, {{nome}}! Até {{dia}} então. Tenho certeza que você vai gostar do que vou mostrar. Abraço!"

---

## Script 2: Ligação de Fechamento (Pós-Demo)

### 🎯 **Abertura Calorosa (30 segundos)**

**Vendedor**: "Oi {{nome}}! Aqui é o {{seu_nome}} novamente. Como você está? Lembra da nossa conversa sobre o TeemoFlow na {{data_demo}}?"

**[Aguardar resposta positiva]**

**Vendedor**: "Que bom! Então, você teve tempo de pensar sobre o que conversamos? Alguma dúvida surgiu?"

### 🔍 **Sondagem Pós-Demo (2 minutos)**

**Pergunta 1**: "Do que você viu na demonstração, o que mais chamou sua atenção?"
- **Anotar pontos de interesse**
- **Reforçar benefícios mencionados**

**Pergunta 2**: "Você consegue visualizar como isso se encaixaria no processo de vocês?"
- **Se sim**: "Perfeito! Como você imagina implementando?"
- **Se não**: "Entendo. Que parte ficou menos clara?"

**Pergunta 3**: "Se vocês implementassem hoje, qual seria o primeiro uso que fariam?"
- **Identificar caso de uso prioritário**
- **Calcular ROI específico**

### 💰 **Apresentação de Valor (3 minutos)**

**Vendedor**: "{{nome}}, vou ser bem direto com você. Baseado no que conversamos, vocês precisam de {{numero}} leads por mês, correto?

Vamos fazer uma conta rápida:
- TeemoFlow: R$ {{preco}} por mês
- Funcionário para prospecção: R$ 3.000 + encargos = R$ 4.500/mês
- Economia: R$ {{economia}}/mês
- Mais: resultados em 24h vs. semanas para contratar

Sem contar que vocês teriam acesso a leads que nem sabiam que existiam. Faz sentido?"

### 🎯 **Fechamento Direto (2 minutos)**

**Vendedor**: "{{nome}}, tenho uma proposta para você. Como você demonstrou interesse real e sua empresa tem o perfil ideal, posso oferecer:

✅ 30% de desconto no primeiro ano
✅ Implementação e treinamento gratuitos  
✅ Suporte prioritário por 90 dias
✅ Garantia: 100 leads no primeiro mês ou dinheiro de volta

Essa condição especial é válida só até sexta-feira. Podemos fechar hoje?"

### 🛡️ **Tratamento de Objeções**

**Se "preciso pensar"**:
"Entendo, {{nome}}. Posso perguntar o que especificamente você precisa avaliar? Talvez eu possa esclarecer agora mesmo."

**Se "preciso consultar"**:
"Claro! Para facilitar sua apresentação, que tal eu preparar um resumo executivo com ROI projetado? Ou prefere que eu apresente diretamente para vocês dois?"

**Se "está caro"**:
"{{nome}}, vamos pensar assim: se o TeemoFlow gerar apenas 10 clientes novos por mês, com seu ticket médio de R$ {{ticket}}, são R$ {{receita}} em receita adicional. O investimento se paga na primeira semana."

### ✅ **Fechamento Final**

**Vendedor**: "Perfeito, {{nome}}! Então vamos começar. Preciso apenas confirmar alguns dados para preparar o contrato:

- Razão social da empresa
- CNPJ  
- E-mail para envio do contrato
- Forma de pagamento preferida

O acesso fica pronto em 24h e já agendamos o treinamento. Alguma dúvida?"

---

## Script 3: Ligação de Follow-up (Pós-Proposta)

### 🎯 **Abertura Consultiva (30 segundos)**

**Vendedor**: "Oi {{nome}}! {{seu_nome}} aqui. Como você está? Estou ligando para saber se você teve tempo de avaliar nossa proposta do TeemoFlow."

### 🔍 **Sondagem de Status (1-2 minutos)**

**Pergunta 1**: "Você conseguiu analisar os números que enviamos?"
- **Se sim**: "E aí, o que achou? Fez sentido?"
- **Se não**: "Sem problema! Quer que eu resuma os pontos principais agora?"

**Pergunta 2**: "Surgiu alguma dúvida ou ponto que precisa esclarecer?"
- **Anotar objeções específicas**
- **Tratar uma por vez**

**Pergunta 3**: "Como está o processo de decisão aí? Tem algum prazo definido?"

### 🚀 **Criação de Urgência (2 minutos)**

**Vendedor**: "{{nome}}, vou ser transparente com você. Temos apenas 2 vagas restantes para implementação assistida este mês. Depois disso, a próxima turma só em {{data_futura}}.

Considerando que vocês querem começar a gerar resultados rapidamente, faz sentido garantir uma dessas vagas?"

### 🎯 **Fechamento Alternativo (1 minuto)**

**Vendedor**: "{{nome}}, que tal fazermos assim: em vez de implementação completa, que tal começarmos com um projeto piloto de 30 dias?

Você testa com uma região específica, vê os resultados, e aí decide se expande. Investimento menor, risco zero. O que acha?"

### 📅 **Agendamento de Retorno**

**Se não fechar**: "Entendo, {{nome}}. Quando você acha que terá uma definição? Posso ligar na {{data_sugerida}} para saber como ficou?"

**Se agendar**: "Perfeito! Então na {{data}} às {{horario}} eu ligo para saber a decisão. Até lá, qualquer dúvida, me chama no WhatsApp {{telefone}}."

---

## Script 4: Ligação de Reativação (Leads Frios)

### 🎯 **Abertura Casual (30 segundos)**

**Vendedor**: "Oi {{nome}}! Aqui é o {{seu_nome}}. Tudo bem? Lembra que conversamos sobre automação de leads há uns meses? Como estão as coisas por aí?"

### 🔄 **Reconexão (1-2 minutos)**

**Vendedor**: "{{nome}}, estou ligando porque lançamos algumas funcionalidades novas que podem ser interessantes para vocês:

- Busca por múltiplos CEPs simultâneos
- Integração direta com CRM
- IA para personalização de mensagens
- Relatórios em tempo real

Como estão fazendo a prospecção hoje? Melhorou desde nossa última conversa?"

### 💡 **Nova Proposta de Valor (2 minutos)**

**Vendedor**: "Olha, {{nome}}, sei que na época não era o momento ideal. Mas agora no final do ano, muitas empresas estão acelerando para fechar bem 2024 e planejar 2025.

Que tal fazermos um teste rápido? Posso fazer uma busca gratuita do seu segmento em {{cidade}} e enviar os primeiros 50 leads. Sem compromisso, só para você ver a qualidade.

Se for útil, conversamos. Se não for, pelo menos você tem uma lista nova. Que tal?"

### 🎯 **Fechamento Suave**

**Vendedor**: "{{nome}}, leva 10 minutos para eu preparar essa lista. Posso enviar no seu e-mail {{email}} ainda hoje?"

**[Se aceitar]**: "Perfeito! Vou enviar até às 18h. Aí você dá uma olhada e me fala o que achou. Combinado?"

---

## Dicas Gerais para Todas as Ligações

### 🎯 **Tom e Postura**
- **Confiante, mas não arrogante**
- **Consultivo, não vendedor**
- **Entusiasmado com a solução**
- **Respeitoso com o tempo do prospect**

### 📝 **Preparação Pré-Ligação**
- Pesquisar empresa no Google/LinkedIn
- Verificar site e redes sociais
- Identificar possíveis dores do segmento
- Preparar perguntas específicas

### ⏰ **Gestão de Tempo**
- Máximo 10 minutos por ligação
- Ir direto ao ponto
- Fazer pausas para o prospect falar
- Sempre perguntar se tem tempo

### 🎯 **Objetivos por Tipo de Ligação**
- **Discovery**: Agendar demonstração
- **Fechamento**: Fechar negócio ou próximo passo
- **Follow-up**: Destravar processo
- **Reativação**: Reengajar e qualificar novamente

### 📊 **Métricas de Acompanhamento**
- Taxa de atendimento: >30%
- Taxa de agendamento: >15%
- Taxa de fechamento: >20%
- Tempo médio de ligação: 8-12 minutos

### 🚫 **O que NÃO Fazer**
- Ler script roboticamente
- Falar sem parar
- Pressionar demais
- Desistir na primeira objeção
- Esquecer de fazer follow-up
