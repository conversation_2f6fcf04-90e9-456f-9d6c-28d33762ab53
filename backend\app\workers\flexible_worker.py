"""
Worker para busca flexível (múltiplos tipos de localização)
"""
import time
import re
from datetime import datetime
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from app.workers.base_worker import BaseSeleniumWorker, safe_find_element
from app.services.file_service import export_leads_to_file
from app.models.schemas import JobStatus


class FlexibleSearchWorker(BaseSeleniumWorker):
    """Worker para busca flexível por diferentes tipos de localização"""
    
    def __init__(self, job_id: str, search_type: str, location_query: str, 
                 business_type: str, quantidade: int, radius_km: int = None, headless: bool = True):
        super().__init__(job_id, headless)
        self.search_type = search_type
        self.location_query = location_query
        self.business_type = business_type
        self.quantidade = quantidade
        self.radius_km = radius_km
        self.leads_capturados = []
    
    def run(self):
        """Executa a busca flexível"""
        try:
            self.set_status(JobStatus.RUNNING)
            self.log_info(f"Iniciando busca flexível: {self.business_type} em {self.location_query} (tipo: {self.search_type})")
            
            # Configurar driver
            if not self.setup_driver():
                return
            
            # Abrir Google Maps
            self.log_info("Abrindo Google Maps...")
            self.driver.get('https://www.google.com/maps/')
            time.sleep(3)
            
            if self.check_cancellation():
                return
            
            # Buscar localização baseado no tipo
            if not self.buscar_localizacao():
                return
            
            if self.check_cancellation():
                return
            
            # Buscar negócios
            if not self.buscar_negocios():
                return
            
            if self.check_cancellation():
                return
            
            # Extrair leads
            self.extrair_leads()
            
            # Salvar resultados
            if self.leads_capturados:
                self.salvar_resultados()
                self.set_status(JobStatus.COMPLETED)
                self.log_info(f"Busca concluída com sucesso! {len(self.leads_capturados)} leads capturados.")
            else:
                self.log_warning("Nenhum lead foi capturado.")
                self.set_status(JobStatus.COMPLETED)
            
        except Exception as e:
            self.handle_error(e)
        finally:
            self.cleanup_driver()
    
    def buscar_localizacao(self) -> bool:
        """Busca pela localização baseado no tipo"""
        try:
            search_query = self._build_location_query()
            self.log_info(f"Buscando localização: {search_query}")
            
            # Encontrar campo de busca
            search_box = safe_find_element(self.driver, By.XPATH, '//*[@id="searchboxinput"]', 20)
            if not search_box:
                self.log_error("Campo de busca não encontrado")
                return False
            
            search_box.clear()
            search_box.send_keys(search_query)
            search_box.send_keys(Keys.ENTER)
            
            self.log_info("Aguardando resultados da localização...")
            time.sleep(5)
            
            # Verificar se a localização foi encontrada
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.any_of(
                        EC.presence_of_element_located((By.XPATH, '//h1[contains(@class, "fontHeadlineLarge")]')),
                        EC.presence_of_element_located((By.XPATH, '//div[contains(@class, "place-name")]')),
                        EC.presence_of_element_located((By.XPATH, '//button[@data-value="Directions"]'))
                    )
                )
                self.log_info(f"Localização encontrada: {search_query}")
                return True
            except:
                self.log_warning(f"Não foi possível confirmar se a localização foi encontrada: {search_query}")
                return True  # Continuar mesmo assim
                
        except Exception as e:
            self.log_error(f"Erro ao buscar localização: {str(e)}")
            return False
    
    def buscar_negocios(self) -> bool:
        """Busca pelos negócios na localização"""
        try:
            # Construir query de busca completa
            if self.radius_km:
                search_query = f"{self.business_type} near {self.location_query} within {self.radius_km}km"
            else:
                search_query = f"{self.business_type} near {self.location_query}"
            
            self.log_info(f"Buscando negócios: {search_query}")
            
            # Encontrar campo de busca
            search_box = safe_find_element(self.driver, By.XPATH, '//*[@id="searchboxinput"]')
            if not search_box:
                self.log_error("Campo de busca não encontrado")
                return False
            
            # Limpar e inserir nova busca
            search_box.send_keys(Keys.CONTROL + "a")
            search_box.send_keys(Keys.DELETE)
            search_box.send_keys(search_query)
            search_box.send_keys(Keys.ENTER)
            
            self.log_info("Aguardando resultados da busca de negócios...")
            time.sleep(8)
            
            # Verificar se há resultados
            try:
                resultados = WebDriverWait(self.driver, 15).until(
                    EC.presence_of_all_elements_located((By.XPATH, '//a[@class="hfpxzc"]'))
                )
                self.log_info(f"Encontrados {len(resultados)} resultados iniciais para '{self.business_type}'")
                return True
            except:
                self.log_error(f"Não foram encontrados resultados para '{self.business_type}' na localização especificada")
                return False
                
        except Exception as e:
            self.log_error(f"Erro ao buscar negócios: {str(e)}")
            return False
    
    def extrair_leads(self):
        """Extrai os leads dos resultados"""
        try:
            self.log_info(f"Iniciando extração de {self.quantidade} leads...")
            
            contador = 0
            tentativas_sem_novos = 0
            MAX_TENTATIVAS_SEM_NOVOS = 15
            scroll_attempts = 0
            MAX_SCROLL_ATTEMPTS = 10
            
            while contador < self.quantidade and tentativas_sem_novos < MAX_TENTATIVAS_SEM_NOVOS:
                if self.check_cancellation():
                    break
                
                self.wait_if_paused()
                
                # Coletar elementos de resultados
                try:
                    elementos = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_all_elements_located((By.XPATH, '//a[@class="hfpxzc"]'))
                    )
                except:
                    self.log_warning("Não foi possível encontrar elementos de resultado")
                    break
                
                leads_antes = len(self.leads_capturados)
                
                # Processar cada elemento
                for i, elemento in enumerate(elementos):
                    if contador >= self.quantidade:
                        break
                    
                    if self.check_cancellation():
                        break
                    
                    try:
                        # Scroll até o elemento e clicar
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", elemento)
                        time.sleep(0.5)
                        
                        # Tentar clicar
                        try:
                            elemento.click()
                        except:
                            # Se não conseguir clicar, tentar via JavaScript
                            self.driver.execute_script("arguments[0].click();", elemento)
                        
                        time.sleep(2)
                        
                        # Extrair dados
                        lead = self.extrair_dados_lead()
                        
                        if lead and not self.lead_ja_capturado(lead):
                            self.leads_capturados.append(lead)
                            contador += 1
                            
                            # Atualizar progresso
                            progress = int((contador / self.quantidade) * 100)
                            self.update_progress(progress)
                            
                            self.log_info(f"Lead {contador}/{self.quantidade}: {lead['nome']}")
                        
                    except Exception as e:
                        self.log_warning(f"Erro ao processar elemento {i}: {str(e)}")
                        continue
                
                # Verificar se capturou novos leads
                if len(self.leads_capturados) == leads_antes:
                    tentativas_sem_novos += 1
                    self.log_info(f"Nenhum novo lead capturado. Tentativa {tentativas_sem_novos}/{MAX_TENTATIVAS_SEM_NOVOS}")
                    
                    # Tentar rolar a lista de resultados para carregar mais
                    if scroll_attempts < MAX_SCROLL_ATTEMPTS:
                        try:
                            # Encontrar a lista de resultados e rolar
                            results_panel = self.driver.find_element(By.XPATH, '//div[@role="main"]')
                            self.driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", results_panel)
                            time.sleep(3)
                            scroll_attempts += 1
                            self.log_info(f"Rolando lista de resultados ({scroll_attempts}/{MAX_SCROLL_ATTEMPTS})")
                        except:
                            # Fallback: rolar a página inteira
                            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                            time.sleep(3)
                else:
                    tentativas_sem_novos = 0
                    scroll_attempts = 0
            
            self.log_info(f"Extração finalizada. Total de leads capturados: {len(self.leads_capturados)}")
            
        except Exception as e:
            self.log_error(f"Erro durante extração de leads: {str(e)}")
    
    def extrair_dados_lead(self) -> dict:
        """Extrai dados de um lead específico"""
        try:
            lead = {
                'nome': '',
                'telefone': '',
                'endereco': '',
                'site': '',
                'rating': '',
                'reviews_count': '',
                'categoria': '',
                'horario_funcionamento': '',
                'tipo_busca': self.search_type,
                'localizacao_busca': self.location_query
            }
            
            # Nome do estabelecimento
            try:
                nome_element = self.driver.find_element(By.XPATH, '//h1[@class="DUwDvf lfPIob"]')
                lead['nome'] = nome_element.text.strip()
            except:
                try:
                    # Tentar seletor alternativo
                    nome_element = self.driver.find_element(By.XPATH, '//h1[contains(@class, "fontHeadlineLarge")]')
                    lead['nome'] = nome_element.text.strip()
                except:
                    pass
            
            # Telefone
            try:
                telefone_element = self.driver.find_element(By.XPATH, '//button[contains(@data-item-id, "phone")]//div[contains(@class, "fontBodyMedium")]')
                lead['telefone'] = telefone_element.text.strip()
            except:
                pass
            
            # Endereço
            try:
                endereco_element = self.driver.find_element(By.XPATH, '//button[contains(@data-item-id, "address")]//div[contains(@class, "fontBodyMedium")]')
                lead['endereco'] = endereco_element.text.strip()
            except:
                pass
            
            # Website
            try:
                site_element = self.driver.find_element(By.XPATH, '//a[contains(@data-item-id, "authority")]//div[contains(@class, "fontBodyMedium")]')
                lead['site'] = site_element.text.strip()
            except:
                pass
            
            # Rating e avaliações
            try:
                rating_element = self.driver.find_element(By.XPATH, '//div[@class="F7nice "]//span[@class="ceNzKf"]')
                rating_text = rating_element.get_attribute('aria-label')
                if rating_text:
                    lead['rating'] = rating_text
            except:
                pass
            
            # Categoria
            try:
                categoria_element = self.driver.find_element(By.XPATH, '//button[@class="DkEaL "]')
                lead['categoria'] = categoria_element.text.strip()
            except:
                pass
            
            return lead if lead['nome'] else None
            
        except Exception as e:
            self.log_warning(f"Erro ao extrair dados do lead: {str(e)}")
            return None
    
    def lead_ja_capturado(self, lead: dict) -> bool:
        """Verifica se o lead já foi capturado"""
        for lead_existente in self.leads_capturados:
            if lead_existente['nome'] == lead['nome'] and lead_existente['endereco'] == lead['endereco']:
                return True
        return False
    
    def salvar_resultados(self):
        """Salva os resultados em arquivo Excel"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"leads_{self.search_type}_{timestamp}.xlsx"
            
            file_path = export_leads_to_file(self.leads_capturados, filename, "excel")
            self.set_result_file(file_path)
            
            self.log_info(f"Resultados salvos em: {filename}")
            
        except Exception as e:
            self.log_error(f"Erro ao salvar resultados: {str(e)}")
    
    def _build_location_query(self) -> str:
        """Constrói a query de localização baseada no tipo"""
        if self.search_type == "cep":
            # Formatar CEP
            cep_clean = re.sub(r'[^0-9]', '', self.location_query)
            if len(cep_clean) == 8:
                return f"{cep_clean[:5]}-{cep_clean[5:]}"
            return self.location_query
        
        elif self.search_type == "coordinates":
            # Coordenadas já estão no formato correto
            return self.location_query
        
        else:
            # Para city, neighborhood, address - usar como está
            return self.location_query


async def start_flexible_search(job_id: str, search_type: str, location_query: str, 
                               business_type: str, quantidade: int, radius_km: int = None, 
                               headless: bool = True):
    """Função para iniciar busca flexível em background"""
    worker = FlexibleSearchWorker(job_id, search_type, location_query, business_type, 
                                 quantidade, radius_km, headless)
    worker.run()
