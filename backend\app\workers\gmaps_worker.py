"""
Worker para busca no Google Maps
"""
import time
from datetime import datetime
from selenium.webdriver.common.by import By

from app.workers.base_worker import BaseSeleniumWorker, safe_find_element
from app.services.file_service import export_leads_to_file
from app.models.schemas import JobStatus


class GoogleMapsSearchWorker(BaseSeleniumWorker):
    """Worker para busca no Google Maps"""

    def __init__(self, job_id: str, search_for: str, location: str, total: int,
                 file_format: str = "excel", headless: bool = True):
        super().__init__(job_id, headless)
        self.search_for = search_for
        self.location = location
        self.total = total
        self.file_format = file_format
        self.leads_capturados = []

    def run(self):
        """Executa a busca no Google Maps"""
        try:
            self.set_status(JobStatus.RUNNING)
            self.log_info(f"Iniciando busca: '{self.search_for}' em '{self.location}'")

            # Configurar driver
            if not self.setup_driver():
                return

            # Abrir Google Maps
            self.log_info("Abrindo Google Maps...")
            self.driver.get('https://www.google.com.br/maps')
            time.sleep(3)

            if self.check_cancellation():
                return

            # Buscar localização
            if not self.buscar_localizacao():
                return

            if self.check_cancellation():
                return

            # Buscar termo
            if not self.buscar_termo():
                return

            if self.check_cancellation():
                return

            # Extrair leads
            self.extrair_leads()

            # Salvar resultados
            if self.leads_capturados:
                self.salvar_resultados()
                self.set_status(JobStatus.COMPLETED)
                self.log_info(f"Busca concluída! {len(self.leads_capturados)} leads capturados.")
            else:
                self.log_warning("Nenhum lead foi capturado.")
                self.set_status(JobStatus.COMPLETED)

        except Exception as e:
            self.handle_error(e)
        finally:
            self.cleanup_driver()

    def buscar_localizacao(self) -> bool:
        """Busca pela localização"""
        try:
            self.log_info(f"Buscando localização: {self.location}")

            search_box = self.driver.find_element(By.XPATH, '//*[@id="searchboxinput"]')
            search_box.send_keys(self.location)
            time.sleep(2)

            search_button = self.driver.find_element(By.XPATH, '//*[@id="searchbox-searchbutton"]')
            search_button.click()
            time.sleep(15)

            # Limpar busca
            clear_button = self.driver.find_element(By.XPATH, '//*[@id="searchbox"]/div[2]/button')
            clear_button.click()
            time.sleep(5)

            return True

        except Exception as e:
            self.log_error(f"Erro ao buscar localização: {str(e)}")
            return False

    def buscar_termo(self) -> bool:
        """Busca pelo termo"""
        try:
            self.log_info(f"Buscando termo: {self.search_for}")

            search_box = self.driver.find_element(By.XPATH, '//*[@id="searchboxinput"]')
            search_box.send_keys(self.search_for)

            search_button = self.driver.find_element(By.XPATH, '//*[@id="searchbox-searchbutton"]')
            search_button.click()
            time.sleep(10)

            return True

        except Exception as e:
            self.log_error(f"Erro ao buscar termo: {str(e)}")
            return False

    def extrair_leads(self):
        """Extrai os leads dos resultados"""
        try:
            self.log_info(f"Iniciando extração de {self.total} leads...")

            contador = 0
            move_count = 0
            max_moves = 4

            while contador < self.total and move_count < max_moves:
                if self.check_cancellation():
                    break

                self.wait_if_paused()

                # Coletar elementos de resultados - múltiplas estratégias
                elementos = []
                try:
                    # Estratégia 1: Seletor principal
                    elementos = self.driver.find_elements(By.CSS_SELECTOR, '[data-result-index]')
                    if not elementos:
                        # Estratégia 2: Seletor alternativo
                        elementos = self.driver.find_elements(By.CSS_SELECTOR, 'a.hfpxzc')
                    if not elementos:
                        # Estratégia 3: XPath
                        elementos = self.driver.find_elements(By.XPATH, '//a[@class="hfpxzc"]')

                    if not elementos:
                        self.log_warning("Não foi possível encontrar elementos de resultado")
                        break

                except Exception as e:
                    self.log_warning(f"Erro ao buscar elementos: {str(e)}")
                    break

                leads_antes = len(self.leads_capturados)

                # Processar cada elemento
                for elemento in elementos:
                    if contador >= self.total:
                        break

                    if self.check_cancellation():
                        break

                    try:
                        # Clicar no elemento
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", elemento)
                        time.sleep(0.5)
                        elemento.click()
                        time.sleep(2)

                        # Extrair dados
                        lead = self.extrair_dados_lead()

                        if lead and not self.lead_ja_capturado(lead):
                            self.leads_capturados.append(lead)
                            contador += 1

                            # Atualizar progresso
                            progress = int((contador / self.total) * 100)
                            self.update_progress(progress)

                            self.log_info(f"Lead {contador}/{self.total}: {lead['nome']}")

                    except Exception as e:
                        self.log_warning(f"Erro ao processar elemento: {str(e)}")
                        continue

                # Verificar se capturou novos leads
                if len(self.leads_capturados) == leads_antes:
                    move_count += 1
                    self.log_info(f"Tentando carregar mais resultados ({move_count}/{max_moves})")

                    # Estratégias para carregar mais resultados
                    try:
                        # Estratégia 1: Scroll na lista de resultados
                        results_panel = safe_find_element(self.driver, By.CSS_SELECTOR, '[role="main"]', timeout=2)
                        if results_panel:
                            self.driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", results_panel)
                            time.sleep(3)
                        else:
                            # Estratégia 2: Scroll geral da página
                            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                            time.sleep(3)

                        # Estratégia 3: Tentar clicar em "Mais resultados" se existir
                        more_button = safe_find_element(self.driver, By.XPATH, '//span[contains(text(), "Mais resultados")]', timeout=1)
                        if more_button:
                            more_button.click()
                            time.sleep(3)

                    except Exception as e:
                        self.log_warning(f"Erro ao tentar carregar mais resultados: {str(e)}")
                else:
                    move_count = 0

            self.log_info(f"Extração finalizada. Total de leads capturados: {len(self.leads_capturados)}")

        except Exception as e:
            self.log_error(f"Erro durante extração de leads: {str(e)}")

    def extrair_dados_lead(self) -> dict:
        """Extrai dados de um lead específico com múltiplas estratégias"""
        try:
            lead = {
                'nome': 'N/A',
                'telefone': 'N/A',
                'endereco': 'N/A',
                'website': 'N/A',
                'avaliacao': 'N/A',
                'total_avaliacoes': 'N/A',
                'categoria': 'N/A',
                'horario_funcionamento': 'N/A',
                'capturado_em': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # Nome do estabelecimento - múltiplas estratégias
            nome_element = (
                safe_find_element(self.driver, By.CSS_SELECTOR, 'h1[data-attrid="title"]', timeout=3) or
                safe_find_element(self.driver, By.CSS_SELECTOR, 'h1.DUwDvf', timeout=3) or
                safe_find_element(self.driver, By.CSS_SELECTOR, 'h1.lfPIob', timeout=3) or
                safe_find_element(self.driver, By.XPATH, '//h1[@class="DUwDvf lfPIob"]', timeout=3)
            )
            if nome_element:
                lead['nome'] = nome_element.text.strip()

            # Telefone - múltiplas estratégias
            telefone_element = (
                safe_find_element(self.driver, By.CSS_SELECTOR, '[data-item-id*="phone"] .fontBodyMedium', timeout=2) or
                safe_find_element(self.driver, By.CSS_SELECTOR, '.rogA2c .fontBodyMedium', timeout=2) or
                safe_find_element(self.driver, By.XPATH, '//button[contains(@data-item-id, "phone")]//div[contains(@class, "fontBodyMedium")]', timeout=2)
            )
            if telefone_element:
                lead['telefone'] = telefone_element.text.strip()

            # Endereço - múltiplas estratégias
            endereco_element = (
                safe_find_element(self.driver, By.CSS_SELECTOR, '[data-item-id="address"] .fontBodyMedium', timeout=2) or
                safe_find_element(self.driver, By.CSS_SELECTOR, '.Io6YTe', timeout=2) or
                safe_find_element(self.driver, By.XPATH, '//button[contains(@data-item-id, "address")]//div[contains(@class, "fontBodyMedium")]', timeout=2)
            )
            if endereco_element:
                lead['endereco'] = endereco_element.text.strip()

            # Website - múltiplas estratégias
            website_element = (
                safe_find_element(self.driver, By.CSS_SELECTOR, '[data-item-id="authority"] a', timeout=2) or
                safe_find_element(self.driver, By.CSS_SELECTOR, 'a[href*="http"]', timeout=2) or
                safe_find_element(self.driver, By.XPATH, '//a[contains(@data-item-id, "authority")]', timeout=2)
            )
            if website_element:
                href = website_element.get_attribute('href')
                if href:
                    lead['website'] = href
                else:
                    lead['website'] = website_element.text.strip()

            # Avaliação
            rating_element = (
                safe_find_element(self.driver, By.CSS_SELECTOR, '.F7nice', timeout=2) or
                safe_find_element(self.driver, By.CSS_SELECTOR, '[data-value] span', timeout=2)
            )
            if rating_element:
                lead['avaliacao'] = rating_element.text.strip()

            # Total de avaliações
            reviews_element = (
                safe_find_element(self.driver, By.CSS_SELECTOR, '.F7nice + span', timeout=2) or
                safe_find_element(self.driver, By.CSS_SELECTOR, '.UY7F9', timeout=2)
            )
            if reviews_element:
                reviews_text = reviews_element.text.strip()
                # Extrair apenas números das avaliações
                import re
                numbers = re.findall(r'\d+', reviews_text)
                if numbers:
                    lead['total_avaliacoes'] = numbers[0]

            # Categoria
            categoria_element = (
                safe_find_element(self.driver, By.CSS_SELECTOR, '.DkEaL', timeout=2) or
                safe_find_element(self.driver, By.CSS_SELECTOR, '.YhemCb', timeout=2)
            )
            if categoria_element:
                lead['categoria'] = categoria_element.text.strip()

            # Horário de funcionamento
            horario_element = (
                safe_find_element(self.driver, By.CSS_SELECTOR, '[data-item-id="oh"] .fontBodyMedium', timeout=2) or
                safe_find_element(self.driver, By.CSS_SELECTOR, '.t39EBf', timeout=2)
            )
            if horario_element:
                lead['horario_funcionamento'] = horario_element.text.strip()

            # Só retorna se pelo menos o nome foi encontrado
            return lead if lead['nome'] != 'N/A' else None

        except Exception as e:
            self.log_warning(f"Erro ao extrair dados do lead: {str(e)}")
            return None

    def lead_ja_capturado(self, lead: dict) -> bool:
        """Verifica se o lead já foi capturado"""
        for lead_existente in self.leads_capturados:
            if lead_existente['nome'] == lead['nome']:
                return True
        return False

    def salvar_resultados(self):
        """Salva os resultados em arquivo"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"leads_gmaps_{timestamp}"

            file_path = export_leads_to_file(self.leads_capturados, filename, self.file_format)
            self.set_result_file(file_path)

            self.log_info(f"Resultados salvos em: {filename}.{self.file_format}")

        except Exception as e:
            self.log_error(f"Erro ao salvar resultados: {str(e)}")


async def start_gmaps_search(job_id: str, search_for: str, location: str, total: int,
                           file_format: str = "excel", headless: bool = True):
    """Função para iniciar busca no Google Maps em background"""
    worker = GoogleMapsSearchWorker(job_id, search_for, location, total, file_format, headless)
    worker.run()
