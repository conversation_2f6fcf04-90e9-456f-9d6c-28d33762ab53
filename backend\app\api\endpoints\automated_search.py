"""
Endpoint para busca automatizada
"""
import uuid
from fastapi import APIRouter, HTTPException, BackgroundTasks
from app.models.schemas import AutomatedSearchRequest, AutomatedSearchResponse
from app.services.job_manager import job_manager
from app.workers.automated_worker import start_automated_search

router = APIRouter()


@router.post("/start", response_model=AutomatedSearchResponse)
async def start_automated_search_endpoint(
    request: AutomatedSearchRequest,
    background_tasks: BackgroundTasks
):
    """
    Inicia uma busca automatizada
    """
    try:
        # Validar se há consultas
        if not request.queries:
            raise HTTPException(
                status_code=400,
                detail="É necessário fornecer pelo menos uma consulta"
            )
        
        # Gerar ID único para o job
        job_id = str(uuid.uuid4())
        
        # Converter queries para dict
        queries_dict = [
            {
                "search_term": q.search_term,
                "location": q.location,
                "max_results": q.max_results
            }
            for q in request.queries
        ]
        
        # Criar job no gerenciador
        job_manager.create_job(
            job_id=job_id,
            job_type="automated_search",
            parameters={
                "queries": queries_dict,
                "file_format": request.file_format.value,
                "headless": request.headless,
                "interval_between_queries": request.interval_between_queries
            }
        )
        
        # Iniciar worker em background
        background_tasks.add_task(
            start_automated_search,
            job_id=job_id,
            queries=queries_dict,
            file_format=request.file_format.value,
            headless=request.headless,
            interval_between_queries=request.interval_between_queries
        )
        
        return AutomatedSearchResponse(
            job_id=job_id,
            message=f"Busca automatizada iniciada com {len(request.queries)} consultas",
            total_queries=len(request.queries)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao iniciar busca automatizada: {str(e)}"
        )


@router.post("/cancel/{job_id}")
async def cancel_automated_search(job_id: str):
    """
    Cancela uma busca automatizada em andamento
    """
    try:
        success = job_manager.cancel_job(job_id)
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Job não encontrado ou não pode ser cancelado"
            )
        
        return {"message": "Busca automatizada cancelada com sucesso"}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao cancelar busca: {str(e)}"
        )


@router.post("/pause/{job_id}")
async def pause_automated_search(job_id: str):
    """
    Pausa uma busca automatizada em andamento
    """
    try:
        success = job_manager.pause_job(job_id)
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Job não encontrado ou não pode ser pausado"
            )
        
        return {"message": "Busca automatizada pausada com sucesso"}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao pausar busca: {str(e)}"
        )


@router.post("/resume/{job_id}")
async def resume_automated_search(job_id: str):
    """
    Retoma uma busca automatizada pausada
    """
    try:
        success = job_manager.resume_job(job_id)
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Job não encontrado ou não pode ser retomado"
            )
        
        return {"message": "Busca automatizada retomada com sucesso"}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao retomar busca: {str(e)}"
        )
