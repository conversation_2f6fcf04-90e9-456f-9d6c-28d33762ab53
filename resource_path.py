import os
import sys

def resource_path(relative_path):
    """
    Obtém o caminho absoluto para um recurso, funciona para desenvolvimento e para o executável.
    
    Args:
        relative_path (str): Caminho relativo para o recurso
        
    Returns:
        str: Caminho absoluto para o recurso
    """
    try:
        # PyInstaller cria um diretório temporário e armazena o caminho em _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        # Se não estiver em um executável, usar o diretório atual
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)
