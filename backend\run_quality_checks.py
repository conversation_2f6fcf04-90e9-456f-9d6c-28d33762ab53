#!/usr/bin/env python3
"""
Script para executar verificações de qualidade de código
"""
import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description, fix_command=None):
    """Executa um comando e exibe o resultado"""
    print(f"\n{'='*60}")
    print(f"🔍 {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True,
            cwd=Path(__file__).parent
        )
        
        if result.stdout:
            print(result.stdout)
        
        print(f"✅ {description} - PASSOU")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - FALHOU")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        
        if fix_command:
            print(f"💡 Para corrigir, execute: {fix_command}")
        
        return False


def main():
    """Função principal"""
    print("🔍 TeemoFlow - Verificações de Qualidade de Código")
    print("=" * 60)
    
    # Verificar se estamos no diretório correto
    if not os.path.exists("app"):
        print("❌ Diretório 'app' não encontrado!")
        print("Execute este script a partir do diretório backend/")
        sys.exit(1)
    
    # Lista de verificações
    quality_checks = [
        {
            "command": "python -m black --version",
            "description": "Verificando instalação do Black"
        },
        {
            "command": "python -m black --check --diff app/ tests/",
            "description": "Verificando formatação com Black",
            "fix_command": "python -m black app/ tests/"
        },
        {
            "command": "python -m isort --check-only --diff app/ tests/",
            "description": "Verificando ordenação de imports com isort",
            "fix_command": "python -m isort app/ tests/"
        },
        {
            "command": "python -m flake8 app/ tests/",
            "description": "Verificando estilo de código com flake8"
        },
        {
            "command": "python -m mypy app/",
            "description": "Verificando tipos com mypy"
        }
    ]
    
    # Executar verificações
    success_count = 0
    total_count = len(quality_checks)
    
    for check in quality_checks:
        if run_command(
            check["command"], 
            check["description"], 
            check.get("fix_command")
        ):
            success_count += 1
    
    # Resumo final
    print(f"\n{'='*60}")
    print(f"📊 RESUMO DAS VERIFICAÇÕES")
    print(f"{'='*60}")
    print(f"✅ Sucessos: {success_count}/{total_count}")
    print(f"❌ Falhas: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print(f"\n🎉 TODAS AS VERIFICAÇÕES PASSARAM!")
        print("✨ Código está seguindo os padrões de qualidade!")
        return 0
    else:
        print(f"\n⚠️  ALGUMAS VERIFICAÇÕES FALHARAM!")
        print("🔧 Execute os comandos de correção sugeridos acima.")
        return 1


def format_code():
    """Formata o código automaticamente"""
    print("🔧 Formatando código automaticamente...")
    
    commands = [
        ("python -m black app/ tests/", "Formatando com Black"),
        ("python -m isort app/ tests/", "Organizando imports com isort")
    ]
    
    for command, description in commands:
        print(f"\n{description}...")
        try:
            subprocess.run(command, shell=True, check=True)
            print(f"✅ {description} - Concluído")
        except subprocess.CalledProcessError as e:
            print(f"❌ {description} - Falhou: {e}")
    
    print("\n✨ Formatação concluída!")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "format":
        format_code()
    else:
        sys.exit(main())
