import React from 'react'
import { I<PERSON><PERSON>utton, Tooltip } from '@mui/material'
import { Brightness4, Brightness7 } from '@mui/icons-material'
import { useTheme } from '../../contexts/ThemeContext'

function ThemeToggle() {
  const { isDarkMode, toggleTheme } = useTheme()

  return (
    <Tooltip title={isDarkMode ? 'Modo claro' : 'Modo escuro'}>
      <IconButton
        color="inherit"
        onClick={toggleTheme}
        sx={{ ml: 1 }}
      >
        {isDarkMode ? <Brightness7 /> : <Brightness4 />}
      </IconButton>
    </Tooltip>
  )
}

export default ThemeToggle
