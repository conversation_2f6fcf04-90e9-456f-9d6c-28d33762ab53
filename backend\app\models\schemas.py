"""
Schemas Pydantic para validação de dados
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class JobStatus(str, Enum):
    """Status de um job"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class LogLevel(str, Enum):
    """Níveis de log"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    SUCCESS = "success"


class FileFormat(str, Enum):
    """Formatos de arquivo suportados"""
    EXCEL = "excel"
    CSV = "csv"


# Enums para tipos de busca
class SearchType(str, Enum):
    """Tipos de busca disponíveis"""
    CEP = "cep"
    CITY = "city"
    NEIGHBORHOOD = "neighborhood"
    ADDRESS = "address"
    COORDINATES = "coordinates"


# Schemas para Busca Flexível
class FlexibleSearchRequest(BaseModel):
    """Request para busca flexível (substitui CEP search)"""
    search_type: SearchType = Field(..., description="Tipo de busca")
    location_query: str = Field(..., description="Consulta de localização (CEP, cidade, bairro, etc.)")
    business_type: str = Field(..., description="Tipo de negócio para buscar")
    quantidade: int = Field(..., gt=0, le=1000, description="Quantidade de leads")
    radius_km: Optional[int] = Field(None, ge=1, le=50, description="Raio de busca em km (opcional)")
    headless: bool = Field(True, description="Modo headless do navegador")

    class Config:
        schema_extra = {
            "example": {
                "search_type": "cep",
                "location_query": "01310-100",
                "business_type": "restaurantes",
                "quantidade": 50,
                "radius_km": 5,
                "headless": True
            }
        }


# Manter compatibilidade com CEP search
class CEPSearchRequest(BaseModel):
    """Request para busca por CEP (compatibilidade)"""
    cep: str = Field(..., description="CEP para busca")
    palavra_chave: str = Field(..., description="Palavra-chave para buscar")
    quantidade: int = Field(..., gt=0, le=1000, description="Quantidade de leads")
    headless: bool = Field(True, description="Modo headless do navegador")


class CEPSearchResponse(BaseModel):
    """Response da busca por CEP"""
    job_id: str = Field(..., description="ID do job criado")
    message: str = Field(..., description="Mensagem de confirmação")


# Schemas para Google Maps
class GoogleMapsSearchRequest(BaseModel):
    """Request para busca no Google Maps"""
    search_for: str = Field(..., description="Termo de busca")
    location: str = Field(..., description="Localização")
    total: int = Field(..., gt=0, le=1000, description="Total de resultados")
    file_format: FileFormat = Field(FileFormat.EXCEL, description="Formato do arquivo")
    headless: bool = Field(True, description="Modo headless do navegador")


class GoogleMapsSearchResponse(BaseModel):
    """Response da busca no Google Maps"""
    job_id: str = Field(..., description="ID do job criado")
    message: str = Field(..., description="Mensagem de confirmação")


# Schemas para Busca Automatizada
class AutomatedSearchQuery(BaseModel):
    """Uma consulta da busca automatizada"""
    search_term: str = Field(..., description="Termo de busca")
    location: str = Field(..., description="Localização")
    max_results: int = Field(50, gt=0, le=500, description="Máximo de resultados")


class AutomatedSearchRequest(BaseModel):
    """Request para busca automatizada"""
    queries: List[AutomatedSearchQuery] = Field(..., description="Lista de consultas")
    file_format: FileFormat = Field(FileFormat.EXCEL, description="Formato do arquivo")
    headless: bool = Field(True, description="Modo headless do navegador")
    interval_between_queries: int = Field(5, ge=1, le=60, description="Intervalo entre consultas (segundos)")


class AutomatedSearchResponse(BaseModel):
    """Response da busca automatizada"""
    job_id: str = Field(..., description="ID do job criado")
    message: str = Field(..., description="Mensagem de confirmação")
    total_queries: int = Field(..., description="Total de consultas")


# Schemas para Sistema de Mensagens
class MessageContact(BaseModel):
    """Contato para envio de mensagem"""
    name: str = Field(..., description="Nome do contato")
    phone: str = Field(..., description="Telefone do contato")


class MessageSendRequest(BaseModel):
    """Request para envio de mensagens"""
    contacts: List[MessageContact] = Field(..., description="Lista de contatos")
    message_template: str = Field(..., description="Template da mensagem")
    interval: int = Field(30, ge=10, le=300, description="Intervalo entre mensagens (segundos)")
    use_variations: bool = Field(False, description="Usar variações da mensagem")


class MessageSendResponse(BaseModel):
    """Response do envio de mensagens"""
    job_id: str = Field(..., description="ID do job criado")
    message: str = Field(..., description="Mensagem de confirmação")
    total_contacts: int = Field(..., description="Total de contatos")


# Schemas para Jobs
class JobInfo(BaseModel):
    """Informações de um job"""
    job_id: str = Field(..., description="ID do job")
    status: JobStatus = Field(..., description="Status do job")
    progress: int = Field(0, ge=0, le=100, description="Progresso em porcentagem")
    created_at: str = Field(..., description="Data/hora de criação")
    started_at: Optional[str] = Field(None, description="Data/hora de início")
    completed_at: Optional[str] = Field(None, description="Data/hora de conclusão")
    result_file: Optional[str] = Field(None, description="Arquivo de resultado")
    error_message: Optional[str] = Field(None, description="Mensagem de erro")
    logs: List[Dict[str, Any]] = Field(default_factory=list, description="Logs do job")


class JobListResponse(BaseModel):
    """Response da lista de jobs"""
    jobs: List[JobInfo] = Field(..., description="Lista de jobs")
    total: int = Field(..., description="Total de jobs")


# Schemas para Logs em tempo real
class LogMessage(BaseModel):
    """Mensagem de log"""
    job_id: str = Field(..., description="ID do job")
    level: LogLevel = Field(..., description="Nível do log")
    message: str = Field(..., description="Mensagem")
    timestamp: str = Field(..., description="Timestamp")


# Schemas para Upload de arquivos
class FileUploadResponse(BaseModel):
    """Response do upload de arquivo"""
    filename: str = Field(..., description="Nome do arquivo")
    file_path: str = Field(..., description="Caminho do arquivo")
    size: int = Field(..., description="Tamanho do arquivo em bytes")
    contacts_count: Optional[int] = Field(None, description="Número de contatos (se aplicável)")


# Schemas para Lead/Cliente
class Lead(BaseModel):
    """Dados de um lead capturado"""
    nome: str = Field(..., description="Nome do estabelecimento")
    telefone: Optional[str] = Field(None, description="Telefone")
    endereco: Optional[str] = Field(None, description="Endereço")
    site: Optional[str] = Field(None, description="Website")
    rating: Optional[float] = Field(None, description="Avaliação")
    reviews_count: Optional[int] = Field(None, description="Número de avaliações")
    categoria: Optional[str] = Field(None, description="Categoria do negócio")
    horario_funcionamento: Optional[str] = Field(None, description="Horário de funcionamento")


class LeadsExportResponse(BaseModel):
    """Response da exportação de leads"""
    file_path: str = Field(..., description="Caminho do arquivo exportado")
    total_leads: int = Field(..., description="Total de leads exportados")
    format: FileFormat = Field(..., description="Formato do arquivo")
