"""
Endpoint para gerenciamento de jobs
"""
from fastapi import APIRouter, HTTPException, WebSocket, WebSocketDisconnect
from typing import List
import json
from app.models.schemas import JobInfo, JobListResponse, LogMessage
from app.services.job_manager import job_manager
from app.services.websocket_manager import websocket_manager

router = APIRouter()


@router.get("/", response_model=JobListResponse)
async def list_jobs(limit: int = 50, offset: int = 0):
    """
    Lista todos os jobs
    """
    try:
        jobs = job_manager.list_jobs(limit=limit, offset=offset)
        total = job_manager.count_jobs()
        
        return JobListResponse(
            jobs=jobs,
            total=total
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao listar jobs: {str(e)}"
        )


@router.get("/{job_id}", response_model=JobInfo)
async def get_job(job_id: str):
    """
    Obtém informações de um job específico
    """
    try:
        job = job_manager.get_job(job_id)
        if not job:
            raise HTTPException(
                status_code=404,
                detail="Job não encontrado"
            )
        
        return job
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao obter job: {str(e)}"
        )


@router.delete("/{job_id}")
async def delete_job(job_id: str):
    """
    Remove um job do sistema
    """
    try:
        success = job_manager.delete_job(job_id)
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Job não encontrado"
            )
        
        return {"message": "Job removido com sucesso"}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao remover job: {str(e)}"
        )


@router.post("/{job_id}/cancel")
async def cancel_job(job_id: str):
    """
    Cancela um job em andamento
    """
    try:
        success = job_manager.cancel_job(job_id)
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Job não encontrado ou não pode ser cancelado"
            )
        
        return {"message": "Job cancelado com sucesso"}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao cancelar job: {str(e)}"
        )


@router.websocket("/ws/{job_id}")
async def websocket_job_logs(websocket: WebSocket, job_id: str):
    """
    WebSocket para receber logs em tempo real de um job
    """
    await websocket.accept()
    
    # Adicionar conexão ao gerenciador
    websocket_manager.add_connection(job_id, websocket)
    
    try:
        # Enviar logs existentes
        job = job_manager.get_job(job_id)
        if job:
            for log in job.logs:
                await websocket.send_text(json.dumps(log))
        
        # Manter conexão ativa
        while True:
            # Aguardar mensagens do cliente (ping/pong)
            try:
                data = await websocket.receive_text()
                # Echo para manter conexão viva
                await websocket.send_text(json.dumps({"type": "pong"}))
            except WebSocketDisconnect:
                break
                
    except WebSocketDisconnect:
        pass
    finally:
        # Remover conexão do gerenciador
        websocket_manager.remove_connection(job_id, websocket)


@router.websocket("/ws/all")
async def websocket_all_jobs(websocket: WebSocket):
    """
    WebSocket para receber atualizações de todos os jobs
    """
    await websocket.accept()
    
    # Adicionar conexão global
    websocket_manager.add_global_connection(websocket)
    
    try:
        # Enviar lista inicial de jobs
        jobs = job_manager.list_jobs(limit=100)
        await websocket.send_text(json.dumps({
            "type": "jobs_list",
            "data": [job.dict() for job in jobs]
        }))
        
        # Manter conexão ativa
        while True:
            try:
                data = await websocket.receive_text()
                await websocket.send_text(json.dumps({"type": "pong"}))
            except WebSocketDisconnect:
                break
                
    except WebSocketDisconnect:
        pass
    finally:
        # Remover conexão global
        websocket_manager.remove_global_connection(websocket)
