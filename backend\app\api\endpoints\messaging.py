"""
Endpoint para sistema de mensagens
"""
import uuid
from fastapi import APIRouter, HTTPException, BackgroundTasks, UploadFile, File
from typing import List
from app.models.schemas import MessageSendRequest, MessageSendResponse, FileUploadResponse
from app.services.job_manager import job_manager
from app.services.file_service import process_contacts_file
from app.workers.messaging_worker import start_message_sending

router = APIRouter()


@router.post("/upload-contacts", response_model=FileUploadResponse)
async def upload_contacts_file(file: UploadFile = File(...)):
    """
    Faz upload de arquivo de contatos (Excel/CSV)
    """
    try:
        # Validar tipo de arquivo
        if not file.filename.lower().endswith(('.xlsx', '.xls', '.csv')):
            raise HTTPException(
                status_code=400,
                detail="Arquivo deve ser Excel (.xlsx, .xls) ou CSV (.csv)"
            )
        
        # Processar arquivo
        result = await process_contacts_file(file)
        
        return FileUploadResponse(
            filename=result["filename"],
            file_path=result["file_path"],
            size=result["size"],
            contacts_count=result["contacts_count"]
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao processar arquivo de contatos: {str(e)}"
        )


@router.post("/send", response_model=MessageSendResponse)
async def send_messages_endpoint(
    request: MessageSendRequest,
    background_tasks: BackgroundTasks
):
    """
    Inicia o envio de mensagens
    """
    try:
        # Validar se há contatos
        if not request.contacts:
            raise HTTPException(
                status_code=400,
                detail="É necessário fornecer pelo menos um contato"
            )
        
        # Validar template da mensagem
        if "{nome}" not in request.message_template:
            raise HTTPException(
                status_code=400,
                detail="Template da mensagem deve conter {nome} para personalização"
            )
        
        # Gerar ID único para o job
        job_id = str(uuid.uuid4())
        
        # Converter contatos para dict
        contacts_dict = [
            {
                "name": c.name,
                "phone": c.phone
            }
            for c in request.contacts
        ]
        
        # Criar job no gerenciador
        job_manager.create_job(
            job_id=job_id,
            job_type="message_sending",
            parameters={
                "contacts": contacts_dict,
                "message_template": request.message_template,
                "interval": request.interval,
                "use_variations": request.use_variations
            }
        )
        
        # Iniciar worker em background
        background_tasks.add_task(
            start_message_sending,
            job_id=job_id,
            contacts=contacts_dict,
            message_template=request.message_template,
            interval=request.interval,
            use_variations=request.use_variations
        )
        
        return MessageSendResponse(
            job_id=job_id,
            message=f"Envio de mensagens iniciado para {len(request.contacts)} contatos",
            total_contacts=len(request.contacts)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao iniciar envio de mensagens: {str(e)}"
        )


@router.post("/cancel/{job_id}")
async def cancel_message_sending(job_id: str):
    """
    Cancela o envio de mensagens em andamento
    """
    try:
        success = job_manager.cancel_job(job_id)
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Job não encontrado ou não pode ser cancelado"
            )
        
        return {"message": "Envio de mensagens cancelado com sucesso"}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao cancelar envio: {str(e)}"
        )


@router.post("/pause/{job_id}")
async def pause_message_sending(job_id: str):
    """
    Pausa o envio de mensagens em andamento
    """
    try:
        success = job_manager.pause_job(job_id)
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Job não encontrado ou não pode ser pausado"
            )
        
        return {"message": "Envio de mensagens pausado com sucesso"}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao pausar envio: {str(e)}"
        )


@router.post("/resume/{job_id}")
async def resume_message_sending(job_id: str):
    """
    Retoma o envio de mensagens pausado
    """
    try:
        success = job_manager.resume_job(job_id)
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Job não encontrado ou não pode ser retomado"
            )
        
        return {"message": "Envio de mensagens retomado com sucesso"}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao retomar envio: {str(e)}"
        )


@router.post("/generate-variation")
async def generate_message_variation(message: str):
    """
    Gera uma variação da mensagem usando IA
    """
    try:
        from app.services.message_generator import generate_message_variation
        
        variation = generate_message_variation(message)
        
        return {
            "original": message,
            "variation": variation
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao gerar variação da mensagem: {str(e)}"
        )
