# TeemoFlow Backend - Makefile
.PHONY: help install test test-unit test-integration test-cov format lint check quality clean dev

# Default target
help:
	@echo "🚀 TeemoFlow Backend - Comandos Disponíveis"
	@echo "============================================"
	@echo "📦 install          - Instalar dependências"
	@echo "🧪 test             - Executar todos os testes"
	@echo "🔬 test-unit        - Executar apenas testes unitários"
	@echo "🔗 test-integration - Executar apenas testes de integração"
	@echo "📊 test-cov         - Executar testes com cobertura"
	@echo "✨ format           - Formatar código (black + isort)"
	@echo "🔍 lint             - Verificar estilo de código (flake8)"
	@echo "🔎 check            - Verificar tipos (mypy)"
	@echo "🏆 quality          - Executar todas as verificações de qualidade"
	@echo "🧹 clean            - Limpar arquivos temporários"
	@echo "🚀 dev              - Iniciar servidor de desenvolvimento"

# Install dependencies
install:
	@echo "📦 Instalando dependências..."
	pip install -r requirements.txt

# Run all tests
test:
	@echo "🧪 Executando todos os testes..."
	python run_tests.py

# Run unit tests only
test-unit:
	@echo "🔬 Executando testes unitários..."
	python -m pytest tests/ -m "unit" -v

# Run integration tests only
test-integration:
	@echo "🔗 Executando testes de integração..."
	python -m pytest tests/ -m "integration" -v

# Run tests with coverage
test-cov:
	@echo "📊 Executando testes com cobertura..."
	python -m pytest tests/ --cov=app --cov-report=term-missing --cov-report=html

# Format code
format:
	@echo "✨ Formatando código..."
	python run_quality_checks.py format

# Lint code
lint:
	@echo "🔍 Verificando estilo de código..."
	python -m flake8 app/ tests/

# Type checking
check:
	@echo "🔎 Verificando tipos..."
	python -m mypy app/

# Run all quality checks
quality:
	@echo "🏆 Executando verificações de qualidade..."
	python run_quality_checks.py

# Clean temporary files
clean:
	@echo "🧹 Limpando arquivos temporários..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".mypy_cache" -exec rm -rf {} +
	rm -rf htmlcov/
	rm -rf dist/
	rm -rf build/
	@echo "✅ Limpeza concluída!"

# Start development server
dev:
	@echo "🚀 Iniciando servidor de desenvolvimento..."
	python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Quick development workflow
dev-check: format lint test
	@echo "🎉 Verificação de desenvolvimento concluída!"

# CI/CD workflow
ci: install quality test-cov
	@echo "🏗️ Pipeline CI/CD concluído!"

# Show project info
info:
	@echo "📋 Informações do Projeto"
	@echo "========================="
	@echo "Nome: TeemoFlow Backend"
	@echo "Linguagem: Python"
	@echo "Framework: FastAPI"
	@echo "Testes: pytest"
	@echo "Formatação: black + isort"
	@echo "Linting: flake8"
	@echo "Type Checking: mypy"
