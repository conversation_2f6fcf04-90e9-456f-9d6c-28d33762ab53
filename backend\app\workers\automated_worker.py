"""
Worker para busca automatizada (múltiplas consultas sequenciais)
"""
import time
import os
from datetime import datetime
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from app.workers.base_worker import BaseSeleniumWorker, safe_find_element
from app.services.file_service import export_leads_to_file
from app.models.schemas import JobStatus


class AutomatedSearchWorker(BaseSeleniumWorker):
    """Worker para busca automatizada com múltiplas consultas"""
    
    def __init__(self, job_id: str, queries: list, file_format: str = "excel", 
                 interval_between_queries: int = 5, headless: bool = True):
        super().__init__(job_id, headless)
        self.queries = queries
        self.file_format = file_format
        self.interval_between_queries = interval_between_queries
        self.all_leads = []
        self.current_query_index = 0
        self.total_queries = len(queries)
        self.leads_per_query = {}
    
    def run(self):
        """Executa a busca automatizada"""
        try:
            self.set_status(JobStatus.RUNNING)
            self.log_info(f"Iniciando busca automatizada com {self.total_queries} consultas")
            
            # Configurar driver
            if not self.setup_driver():
                return
            
            # Abrir Google Maps
            self.log_info("Abrindo Google Maps...")
            self.driver.get('https://www.google.com/maps/')
            time.sleep(3)
            
            if self.check_cancellation():
                return
            
            # Executar cada consulta
            for i, query in enumerate(self.queries):
                if self.check_cancellation():
                    break
                
                self.current_query_index = i
                self.log_info(f"Executando consulta {i+1}/{self.total_queries}: {query['search_term']} em {query['location']}")
                
                # Aguardar se pausado
                self.wait_if_paused()
                
                # Executar consulta
                query_leads = self.execute_query(query, i+1)
                
                if query_leads:
                    self.leads_per_query[f"query_{i+1}"] = query_leads
                    self.all_leads.extend(query_leads)
                    self.log_info(f"Consulta {i+1} concluída: {len(query_leads)} leads capturados")
                else:
                    self.log_warning(f"Consulta {i+1} não retornou resultados")
                
                # Atualizar progresso geral
                progress = int(((i + 1) / self.total_queries) * 100)
                self.update_progress(progress)
                
                # Aguardar intervalo entre consultas (exceto na última)
                if i < self.total_queries - 1:
                    self.wait_interval_between_queries()
            
            # Salvar resultados finais
            if self.all_leads:
                self.save_final_results()
            
            # Finalizar
            self.set_status(JobStatus.COMPLETED)
            self.log_info(f"Busca automatizada concluída! Total: {len(self.all_leads)} leads de {self.total_queries} consultas")
            
        except Exception as e:
            self.handle_error(e)
        finally:
            self.cleanup_driver()
    
    def execute_query(self, query: dict, query_number: int) -> list:
        """Executa uma consulta específica"""
        try:
            search_term = query['search_term']
            location = query['location']
            max_results = query.get('max_results', 50)
            
            # Limpar busca anterior
            search_box = safe_find_element(self.driver, By.ID, "searchboxinput")
            if not search_box:
                self.log_error("Não foi possível encontrar a caixa de busca")
                return []
            
            search_box.clear()
            time.sleep(1)
            
            # Construir termo de busca completo
            full_search_term = f"{search_term} {location}"
            
            # Inserir busca
            search_box.send_keys(full_search_term)
            time.sleep(1)
            
            # Clicar no botão de busca
            search_button = safe_find_element(self.driver, By.ID, "searchbox-searchbutton")
            if search_button:
                search_button.click()
            else:
                search_box.send_keys(Keys.RETURN)
            
            time.sleep(5)
            
            if self.check_cancellation():
                return []
            
            # Capturar leads desta consulta
            query_leads = self.capture_leads_for_query(max_results, query_number)
            
            return query_leads
            
        except Exception as e:
            self.log_error(f"Erro ao executar consulta {query_number}: {str(e)}")
            return []
    
    def capture_leads_for_query(self, max_results: int, query_number: int) -> list:
        """Captura leads para uma consulta específica"""
        leads_capturados = []
        contador = 0
        tentativas_scroll = 0
        max_tentativas_scroll = 10
        
        try:
            while contador < max_results and tentativas_scroll < max_tentativas_scroll:
                if self.check_cancellation():
                    break
                
                # Aguardar se pausado
                self.wait_if_paused()
                
                # Encontrar elementos de negócios
                elementos = self.driver.find_elements(By.CSS_SELECTOR, '[data-result-index]')
                
                if not elementos:
                    self.log_warning(f"Nenhum resultado encontrado para consulta {query_number}")
                    break
                
                leads_antes = len(leads_capturados)
                
                # Processar cada elemento
                for i, elemento in enumerate(elementos):
                    if contador >= max_results:
                        break
                    
                    if self.check_cancellation():
                        break
                    
                    try:
                        # Scroll para o elemento
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", elemento)
                        time.sleep(0.5)
                        
                        # Clicar no elemento
                        elemento.click()
                        time.sleep(2)
                        
                        # Extrair dados
                        lead = self.extrair_dados_lead()
                        
                        if lead and not self.lead_ja_capturado(lead, leads_capturados):
                            lead['query_number'] = query_number
                            lead['search_term'] = self.queries[query_number-1]['search_term']
                            lead['location'] = self.queries[query_number-1]['location']
                            
                            leads_capturados.append(lead)
                            contador += 1
                            
                            self.log_info(f"Query {query_number} - Lead {contador}/{max_results}: {lead['nome']}")
                        
                    except Exception as e:
                        self.log_warning(f"Erro ao processar elemento {i} da consulta {query_number}: {str(e)}")
                        continue
                
                # Verificar se capturou novos leads
                if len(leads_capturados) == leads_antes:
                    tentativas_scroll += 1
                    self.log_info(f"Fazendo scroll para carregar mais resultados... (tentativa {tentativas_scroll})")
                    
                    # Scroll para baixo
                    self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                    time.sleep(3)
                else:
                    tentativas_scroll = 0
            
            return leads_capturados
            
        except Exception as e:
            self.log_error(f"Erro ao capturar leads da consulta {query_number}: {str(e)}")
            return leads_capturados
    
    def extrair_dados_lead(self) -> dict:
        """Extrai dados de um lead do Google Maps"""
        try:
            lead = {}
            
            # Nome do negócio
            nome_element = safe_find_element(self.driver, By.CSS_SELECTOR, 'h1[data-attrid="title"]', timeout=3)
            if not nome_element:
                nome_element = safe_find_element(self.driver, By.CSS_SELECTOR, 'h1.DUwDvf', timeout=3)
            
            lead['nome'] = nome_element.text.strip() if nome_element else "N/A"
            
            # Endereço
            endereco_element = safe_find_element(self.driver, By.CSS_SELECTOR, '[data-item-id="address"]', timeout=2)
            if not endereco_element:
                endereco_element = safe_find_element(self.driver, By.CSS_SELECTOR, '.Io6YTe', timeout=2)
            
            lead['endereco'] = endereco_element.text.strip() if endereco_element else "N/A"
            
            # Telefone
            telefone_element = safe_find_element(self.driver, By.CSS_SELECTOR, '[data-item-id*="phone"]', timeout=2)
            if not telefone_element:
                telefone_element = safe_find_element(self.driver, By.CSS_SELECTOR, '.rogA2c .fontBodyMedium', timeout=2)
            
            lead['telefone'] = telefone_element.text.strip() if telefone_element else "N/A"
            
            # Website
            website_element = safe_find_element(self.driver, By.CSS_SELECTOR, '[data-item-id="authority"]', timeout=2)
            if not website_element:
                website_element = safe_find_element(self.driver, By.CSS_SELECTOR, 'a[href*="http"]', timeout=2)
            
            lead['website'] = website_element.get_attribute('href') if website_element else "N/A"
            
            # Avaliação
            rating_element = safe_find_element(self.driver, By.CSS_SELECTOR, '.F7nice', timeout=2)
            lead['avaliacao'] = rating_element.text.strip() if rating_element else "N/A"
            
            # Horário de funcionamento
            horario_element = safe_find_element(self.driver, By.CSS_SELECTOR, '[data-item-id="oh"]', timeout=2)
            lead['horario'] = horario_element.text.strip() if horario_element else "N/A"
            
            # Timestamp
            lead['capturado_em'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            return lead
            
        except Exception as e:
            self.log_warning(f"Erro ao extrair dados do lead: {str(e)}")
            return None
    
    def lead_ja_capturado(self, lead: dict, leads_list: list) -> bool:
        """Verifica se o lead já foi capturado"""
        if not lead or not lead.get('nome'):
            return True
        
        for existing_lead in leads_list:
            if (existing_lead.get('nome') == lead.get('nome') and 
                existing_lead.get('endereco') == lead.get('endereco')):
                return True
        
        return False
    
    def wait_interval_between_queries(self):
        """Aguarda intervalo entre consultas"""
        for remaining in range(self.interval_between_queries, 0, -1):
            if self.check_cancellation():
                break
            
            if remaining % 5 == 0 or remaining <= 3:
                self.log_info(f"Aguardando {remaining}s para próxima consulta...")
            
            time.sleep(1)
            self.wait_if_paused()
    
    def save_final_results(self):
        """Salva os resultados finais"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"busca_automatizada_{timestamp}"
            
            file_path = export_leads_to_file(self.all_leads, filename, self.file_format)
            
            self.set_result_file(file_path)
            self.log_info(f"Resultados salvos em: {os.path.basename(file_path)}")
            
        except Exception as e:
            self.log_error(f"Erro ao salvar resultados: {str(e)}")


async def start_automated_search(job_id: str, queries: list, file_format: str = "excel", 
                                 headless: bool = True, interval_between_queries: int = 5):
    """Função para iniciar busca automatizada em background"""
    worker = AutomatedSearchWorker(job_id, queries, file_format, interval_between_queries, headless)
    worker.run()
