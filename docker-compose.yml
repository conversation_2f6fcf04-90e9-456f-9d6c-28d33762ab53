version: '3.8'

services:
  # Backend FastAPI
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./teemoflow.db
      - REDIS_URL=redis://redis:6379/0
      - ALLOWED_HOSTS=["http://localhost:3000", "http://127.0.0.1:3000"]
    volumes:
      - ./backend:/app
      - ./backend/uploads:/app/uploads
      - ./backend/exports:/app/exports
      - ./backend/logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped

  # Frontend React
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - VITE_API_URL=http://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    restart: unless-stopped

  # Redis para cache e queue
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # Chrome para Selenium (opcional)
  chrome:
    image: selenium/standalone-chrome:latest
    ports:
      - "4444:4444"
    environment:
      - SE_NODE_MAX_SESSIONS=2
      - SE_NODE_SESSION_TIMEOUT=300
    volumes:
      - /dev/shm:/dev/shm
    restart: unless-stopped

volumes:
  redis_data:
