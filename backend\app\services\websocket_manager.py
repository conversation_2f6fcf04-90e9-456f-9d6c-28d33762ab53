"""
Gerenciador de WebSockets para comunicação em tempo real
"""
import json
from datetime import datetime
from typing import Dict, Set, Any
from fastapi import WebSocket
import logging

logger = logging.getLogger(__name__)


class WebSocketManager:
    """Gerenciador de conexões WebSocket"""

    def __init__(self):
        # Conexões por job_id
        self._job_connections: Dict[str, Set[WebSocket]] = {}
        # Conexões globais (recebem atualizações de todos os jobs)
        self._global_connections: Set[WebSocket] = set()

    def add_connection(self, job_id: str, websocket: WebSocket):
        """Adiciona uma conexão WebSocket para um job específico"""
        if job_id not in self._job_connections:
            self._job_connections[job_id] = set()

        self._job_connections[job_id].add(websocket)
        logger.info(f"WebSocket conectado para job {job_id}")

    def remove_connection(self, job_id: str, websocket: WebSocket):
        """Remove uma conexão WebSocket de um job"""
        if job_id in self._job_connections:
            self._job_connections[job_id].discard(websocket)

            # Remove o job se não há mais conexões
            if not self._job_connections[job_id]:
                del self._job_connections[job_id]

        logger.info(f"WebSocket desconectado do job {job_id}")

    def add_global_connection(self, websocket: WebSocket):
        """Adiciona uma conexão global"""
        self._global_connections.add(websocket)
        logger.info("WebSocket global conectado")

    def remove_global_connection(self, websocket: WebSocket):
        """Remove uma conexão global"""
        self._global_connections.discard(websocket)
        logger.info("WebSocket global desconectado")

    async def broadcast_log(self, job_id: str, log_entry: Dict[str, Any]):
        """Envia log para todas as conexões de um job"""
        if job_id not in self._job_connections:
            return

        message = json.dumps({
            "type": "log",
            "job_id": job_id,
            "data": log_entry
        })

        # Lista de conexões a serem removidas (se falharem)
        failed_connections = []

        for websocket in self._job_connections[job_id]:
            try:
                await websocket.send_text(message)
            except Exception as e:
                logger.warning(f"Erro ao enviar log via WebSocket: {e}")
                failed_connections.append(websocket)

        # Remove conexões que falharam
        for websocket in failed_connections:
            self._job_connections[job_id].discard(websocket)

    async def broadcast_job_update(self, job_data: Dict[str, Any]):
        """Envia atualização de job para conexões globais"""
        if not self._global_connections:
            return

        message = json.dumps({
            "type": "job_update",
            "data": job_data
        })

        # Lista de conexões a serem removidas (se falharem)
        failed_connections = []

        for websocket in self._global_connections:
            try:
                await websocket.send_text(message)
            except Exception as e:
                logger.warning(f"Erro ao enviar atualização via WebSocket: {e}")
                failed_connections.append(websocket)

        # Remove conexões que falharam
        for websocket in failed_connections:
            self._global_connections.discard(websocket)

    async def broadcast_progress(self, job_id: str, progress: int):
        """Envia atualização de progresso"""
        message = json.dumps({
            "type": "progress",
            "job_id": job_id,
            "progress": progress
        })

        # Enviar para conexões específicas do job
        if job_id in self._job_connections:
            failed_connections = []

            for websocket in self._job_connections[job_id]:
                try:
                    await websocket.send_text(message)
                except Exception as e:
                    logger.warning(f"Erro ao enviar progresso via WebSocket: {e}")
                    failed_connections.append(websocket)

            # Remove conexões que falharam
            for websocket in failed_connections:
                self._job_connections[job_id].discard(websocket)

        # Enviar para conexões globais
        await self.broadcast_job_update({
            "job_id": job_id,
            "progress": progress
        })

    async def broadcast_status_change(self, job_id: str, status: str):
        """Envia mudança de status"""
        message = json.dumps({
            "type": "status_change",
            "job_id": job_id,
            "status": status
        })

        # Enviar para conexões específicas do job
        if job_id in self._job_connections:
            failed_connections = []

            for websocket in self._job_connections[job_id]:
                try:
                    await websocket.send_text(message)
                except Exception as e:
                    logger.warning(f"Erro ao enviar status via WebSocket: {e}")
                    failed_connections.append(websocket)

            # Remove conexões que falharam
            for websocket in failed_connections:
                self._job_connections[job_id].discard(websocket)

        # Enviar para conexões globais
        await self.broadcast_job_update({
            "job_id": job_id,
            "status": status
        })

    async def broadcast_job_completion(self, job_id: str, success: bool, error_message: str = None):
        """Envia notificação de conclusão de job"""
        message_type = "job_completed" if success else "job_failed"
        message_data = {
            "type": message_type,
            "job_id": job_id,
            "success": success,
            "timestamp": datetime.now().isoformat()
        }

        if not success and error_message:
            message_data["error"] = error_message

        message = json.dumps(message_data)

        # Enviar para conexões globais (notificações)
        failed_connections = []

        for websocket in self._global_connections:
            try:
                await websocket.send_text(message)
            except Exception as e:
                logger.warning(f"Erro ao enviar notificação de conclusão: {e}")
                failed_connections.append(websocket)

        # Remove conexões que falharam
        for websocket in failed_connections:
            self._global_connections.discard(websocket)

    def get_connection_count(self, job_id: str = None) -> int:
        """Retorna o número de conexões ativas"""
        if job_id:
            return len(self._job_connections.get(job_id, set()))
        else:
            total = len(self._global_connections)
            for connections in self._job_connections.values():
                total += len(connections)
            return total


# Instância global do gerenciador de WebSocket
websocket_manager = WebSocketManager()
