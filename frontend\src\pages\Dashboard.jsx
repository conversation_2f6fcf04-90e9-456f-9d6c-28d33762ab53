import React from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
} from '@mui/material'
import {
  Search as SearchIcon,
  Map as MapIcon,
  AutoMode as AutoModeIcon,
  Message as MessageIcon,
  TrendingUp as TrendingUpIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { useQuery } from 'react-query'
import { jobsApi } from '../services/api'

const quickActions = [
  {
    title: 'Busca Flexível',
    description: 'Busque por CEP, cidade, bairro ou coordenadas',
    icon: <SearchIcon sx={{ fontSize: 40 }} />,
    path: '/flexible-search',
    color: '#1976d2',
    featured: true,
  },
  {
    title: 'Google Maps',
    description: 'Busca direta no Google Maps',
    icon: <MapIcon sx={{ fontSize: 40 }} />,
    path: '/gmaps-search',
    color: '#388e3c',
  },
  {
    title: 'Busca Automatizada',
    description: 'Configure múltiplas buscas',
    icon: <AutoModeIcon sx={{ fontSize: 40 }} />,
    path: '/automated-search',
    color: '#f57c00',
  },
  {
    title: 'Mensagens',
    description: 'Envie mensagens via WhatsApp',
    icon: <MessageIcon sx={{ fontSize: 40 }} />,
    path: '/messaging',
    color: '#7b1fa2',
  },
]

function Dashboard() {
  const navigate = useNavigate()

  // Buscar jobs recentes
  const { data: jobsData } = useQuery(
    'recent-jobs',
    () => jobsApi.listJobs({ limit: 5 }),
    {
      refetchInterval: 5000, // Atualizar a cada 5 segundos
    }
  )

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success'
      case 'running':
        return 'info'
      case 'failed':
        return 'error'
      case 'cancelled':
        return 'default'
      default:
        return 'warning'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'pending':
        return 'Pendente'
      case 'running':
        return 'Executando'
      case 'completed':
        return 'Concluído'
      case 'failed':
        return 'Falhou'
      case 'cancelled':
        return 'Cancelado'
      default:
        return status
    }
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Bem-vindo ao TeemoFlow - Sua ferramenta de captura de leads
        </Typography>
      </Box>

      {/* Quick Actions */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {quickActions.map((action) => (
          <Grid item xs={12} sm={6} md={3} key={action.title}>
            <Card
              sx={{
                height: '100%',
                cursor: 'pointer',
                transition: 'transform 0.2s, box-shadow 0.2s',
                border: action.featured ? `2px solid ${action.color}` : 'none',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 4,
                },
              }}
              onClick={() => navigate(action.path)}
            >
              <CardContent sx={{ textAlign: 'center', p: 3 }}>
                <Box
                  sx={{
                    color: action.color,
                    mb: 2,
                  }}
                >
                  {action.icon}
                </Box>
                <Typography variant="h6" component="h2" gutterBottom>
                  {action.title}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {action.description}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Recent Activity */}
      <Grid container spacing={3}>
        {/* Recent Jobs */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <ScheduleIcon sx={{ mr: 1 }} />
              <Typography variant="h6">Jobs Recentes</Typography>
            </Box>

            {jobsData?.jobs?.length > 0 ? (
              <List>
                {jobsData.jobs.map((job) => (
                  <ListItem
                    key={job.job_id}
                    sx={{
                      border: 1,
                      borderColor: 'divider',
                      borderRadius: 1,
                      mb: 1,
                    }}
                  >
                    <ListItemIcon>
                      <TrendingUpIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary={`Job ${job.job_id.slice(0, 8)}...`}
                      secondary={`Criado em ${new Date(job.created_at).toLocaleString()}`}
                    />
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        {job.progress}%
                      </Typography>
                      <Chip
                        label={getStatusText(job.status)}
                        color={getStatusColor(job.status)}
                        size="small"
                      />
                    </Box>
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                Nenhum job encontrado. Comece criando uma nova busca!
              </Typography>
            )}

            <Box sx={{ mt: 2, textAlign: 'center' }}>
              <Button
                variant="outlined"
                onClick={() => navigate('/jobs')}
              >
                Ver Todos os Jobs
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* Statistics */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Estatísticas
            </Typography>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Total de Jobs
              </Typography>
              <Typography variant="h4" color="primary">
                {jobsData?.total || 0}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Jobs Ativos
              </Typography>
              <Typography variant="h4" color="info.main">
                {jobsData?.jobs?.filter(job => job.status === 'running').length || 0}
              </Typography>
            </Box>

            <Box>
              <Typography variant="body2" color="text.secondary">
                Concluídos Hoje
              </Typography>
              <Typography variant="h4" color="success.main">
                {jobsData?.jobs?.filter(job =>
                  job.status === 'completed' &&
                  new Date(job.completed_at).toDateString() === new Date().toDateString()
                ).length || 0}
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  )
}

export default Dashboard
