"""
Endpoint para busca flexível (múltiplos tipos de localização)
"""
import uuid
from fastapi import APIRouter, HTTPException, BackgroundTasks
from app.models.schemas import FlexibleSearchRequest, CEPSearchResponse
from app.services.job_manager import job_manager
from app.workers.flexible_worker import start_flexible_search

router = APIRouter()


@router.post("/start", response_model=CEPSearchResponse)
async def start_flexible_search_endpoint(
    request: FlexibleSearchRequest,
    background_tasks: BackgroundTasks
):
    """
    Inicia uma busca flexível por diferentes tipos de localização
    """
    try:
        # Validar dados baseado no tipo de busca
        if request.search_type == "cep":
            # Validar CEP
            cep_clean = request.location_query.replace("-", "").replace(".", "")
            if len(cep_clean) != 8 or not cep_clean.isdigit():
                raise HTTPException(
                    status_code=400,
                    detail="CEP deve ter 8 dígitos numéricos"
                )
        elif request.search_type == "coordinates":
            # Validar coordenadas (formato: "lat,lng")
            try:
                coords = request.location_query.split(",")
                if len(coords) != 2:
                    raise ValueError()
                lat, lng = float(coords[0].strip()), float(coords[1].strip())
                if not (-90 <= lat <= 90) or not (-180 <= lng <= 180):
                    raise ValueError()
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail="Coordenadas devem estar no formato 'latitude,longitude' (ex: -23.5505,-46.6333)"
                )
        
        # Gerar ID único para o job
        job_id = str(uuid.uuid4())
        
        # Criar job no gerenciador
        job_manager.create_job(
            job_id=job_id,
            job_type="flexible_search",
            parameters={
                "search_type": request.search_type.value,
                "location_query": request.location_query,
                "business_type": request.business_type,
                "quantidade": request.quantidade,
                "radius_km": request.radius_km,
                "headless": request.headless
            }
        )
        
        # Iniciar worker em background
        background_tasks.add_task(
            start_flexible_search,
            job_id=job_id,
            search_type=request.search_type.value,
            location_query=request.location_query,
            business_type=request.business_type,
            quantidade=request.quantidade,
            radius_km=request.radius_km,
            headless=request.headless
        )
        
        # Criar mensagem descritiva baseada no tipo de busca
        location_desc = _get_location_description(request.search_type.value, request.location_query)
        
        return CEPSearchResponse(
            job_id=job_id,
            message=f"Busca iniciada: '{request.business_type}' em {location_desc}"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao iniciar busca: {str(e)}"
        )


@router.post("/cancel/{job_id}")
async def cancel_flexible_search(job_id: str):
    """
    Cancela uma busca flexível em andamento
    """
    try:
        success = job_manager.cancel_job(job_id)
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Job não encontrado ou não pode ser cancelado"
            )
        
        return {"message": "Busca cancelada com sucesso"}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao cancelar busca: {str(e)}"
        )


@router.get("/search-types")
async def get_search_types():
    """
    Retorna os tipos de busca disponíveis com descrições
    """
    return {
        "search_types": [
            {
                "value": "cep",
                "label": "CEP",
                "description": "Busca por CEP específico",
                "placeholder": "00000-000",
                "example": "01310-100"
            },
            {
                "value": "city",
                "label": "Cidade",
                "description": "Busca por cidade",
                "placeholder": "Nome da cidade, Estado",
                "example": "São Paulo, SP"
            },
            {
                "value": "neighborhood",
                "label": "Bairro",
                "description": "Busca por bairro específico",
                "placeholder": "Nome do bairro, Cidade",
                "example": "Vila Madalena, São Paulo"
            },
            {
                "value": "address",
                "label": "Endereço",
                "description": "Busca por endereço específico",
                "placeholder": "Rua, número, bairro",
                "example": "Av. Paulista, 1000, Bela Vista"
            },
            {
                "value": "coordinates",
                "label": "Coordenadas",
                "description": "Busca por coordenadas GPS",
                "placeholder": "latitude,longitude",
                "example": "-23.5505,-46.6333"
            }
        ]
    }


@router.get("/business-suggestions")
async def get_business_suggestions():
    """
    Retorna sugestões de tipos de negócio
    """
    return {
        "categories": [
            {
                "category": "Alimentação",
                "businesses": [
                    "restaurantes", "lanchonetes", "pizzarias", "padarias", 
                    "confeitarias", "sorveterias", "bares", "cafeterias"
                ]
            },
            {
                "category": "Saúde",
                "businesses": [
                    "farmácias", "clínicas", "hospitais", "laboratórios",
                    "consultórios médicos", "dentistas", "fisioterapeutas"
                ]
            },
            {
                "category": "Beleza",
                "businesses": [
                    "salões de beleza", "barbearias", "clínicas de estética",
                    "manicures", "spas", "academias"
                ]
            },
            {
                "category": "Serviços",
                "businesses": [
                    "oficinas", "lavagem de carros", "chaveiros", "eletricistas",
                    "encanadores", "pintores", "marceneiros", "advogados"
                ]
            },
            {
                "category": "Comércio",
                "businesses": [
                    "supermercados", "lojas de roupas", "calçados", "eletrônicos",
                    "móveis", "materiais de construção", "pet shops"
                ]
            },
            {
                "category": "Educação",
                "businesses": [
                    "escolas", "cursos", "universidades", "creches",
                    "escolas de idiomas", "escolas técnicas"
                ]
            }
        ]
    }


def _get_location_description(search_type: str, location_query: str) -> str:
    """Gera descrição amigável da localização"""
    descriptions = {
        "cep": f"CEP {location_query}",
        "city": f"cidade de {location_query}",
        "neighborhood": f"bairro {location_query}",
        "address": f"endereço {location_query}",
        "coordinates": f"coordenadas {location_query}"
    }
    return descriptions.get(search_type, location_query)
