import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Alert,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Chip,
  Divider,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
} from '@mui/material'
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayIcon,
  ExpandMore as ExpandMoreIcon,
  Search as SearchIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material'
import { useMutation } from 'react-query'
import { useSnackbar } from 'notistack'
import { automatedSearchApi } from '../services/api'
import JobProgress from '../components/JobProgress'

function AutomatedSearch() {
  const { enqueueSnackbar } = useSnackbar()
  const [queries, setQueries] = useState([
    { search_term: '', location: '', max_results: 50 }
  ])
  const [settings, setSettings] = useState({
    file_format: 'excel',
    headless: true,
    interval_between_queries: 5
  })
  const [currentJobId, setCurrentJobId] = useState(null)

  // Mutation para iniciar busca automatizada
  const startSearchMutation = useMutation(
    (data) => automatedSearchApi.start(data),
    {
      onSuccess: (response) => {
        setCurrentJobId(response.data.job_id)
        enqueueSnackbar('Busca automatizada iniciada com sucesso!', { variant: 'success' })
      },
      onError: (error) => {
        enqueueSnackbar(
          error.response?.data?.detail || 'Erro ao iniciar busca automatizada',
          { variant: 'error' }
        )
      }
    }
  )

  const addQuery = () => {
    setQueries([...queries, { search_term: '', location: '', max_results: 50 }])
  }

  const removeQuery = (index) => {
    if (queries.length > 1) {
      setQueries(queries.filter((_, i) => i !== index))
    }
  }

  const updateQuery = (index, field, value) => {
    const newQueries = [...queries]
    newQueries[index][field] = value
    setQueries(newQueries)
  }

  const handleStartSearch = () => {
    // Validar consultas
    const validQueries = queries.filter(q => q.search_term.trim() && q.location.trim())

    if (validQueries.length === 0) {
      enqueueSnackbar('Adicione pelo menos uma consulta válida', { variant: 'warning' })
      return
    }

    const searchData = {
      queries: validQueries,
      file_format: settings.file_format,
      headless: settings.headless,
      interval_between_queries: settings.interval_between_queries
    }

    startSearchMutation.mutate(searchData)
  }

  const getTotalResults = () => {
    return queries.reduce((total, query) => total + (query.max_results || 0), 0)
  }

  return (
    <Box>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Busca Automatizada
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Configure múltiplas buscas para execução sequencial automática
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Configuração de Consultas */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <SearchIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">
                  Consultas de Busca
                </Typography>
                <Chip
                  label={`${queries.length} consulta${queries.length !== 1 ? 's' : ''}`}
                  size="small"
                  sx={{ ml: 2 }}
                />
              </Box>

              {queries.map((query, index) => (
                <Accordion key={index} defaultExpanded={index === 0}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">
                      Consulta {index + 1}
                      {query.search_term && (
                        <Typography component="span" variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                          - {query.search_term} em {query.location}
                        </Typography>
                      )}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Termo de Busca"
                          placeholder="Ex: restaurantes, dentistas, lojas"
                          value={query.search_term}
                          onChange={(e) => updateQuery(index, 'search_term', e.target.value)}
                          required
                        />
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          label="Localização"
                          placeholder="Ex: São Paulo, SP"
                          value={query.location}
                          onChange={(e) => updateQuery(index, 'location', e.target.value)}
                          required
                        />
                      </Grid>
                      <Grid item xs={8} md={1.5}>
                        <TextField
                          fullWidth
                          type="number"
                          label="Máx. Resultados"
                          value={query.max_results}
                          onChange={(e) => updateQuery(index, 'max_results', parseInt(e.target.value) || 50)}
                          inputProps={{ min: 1, max: 500 }}
                        />
                      </Grid>
                      <Grid item xs={4} md={0.5}>
                        <IconButton
                          color="error"
                          onClick={() => removeQuery(index)}
                          disabled={queries.length === 1}
                          sx={{ mt: 1 }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Grid>
                    </Grid>
                  </AccordionDetails>
                </Accordion>
              ))}

              <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Button
                  startIcon={<AddIcon />}
                  onClick={addQuery}
                  variant="outlined"
                >
                  Adicionar Consulta
                </Button>

                <Typography variant="body2" color="text.secondary">
                  Total estimado: {getTotalResults()} resultados
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Configurações */}
        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <ScheduleIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">
                  Configurações
                </Typography>
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Formato do Arquivo</InputLabel>
                    <Select
                      value={settings.file_format}
                      label="Formato do Arquivo"
                      onChange={(e) => setSettings({...settings, file_format: e.target.value})}
                    >
                      <MenuItem value="excel">Excel (.xlsx)</MenuItem>
                      <MenuItem value="csv">CSV (.csv)</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Intervalo entre Consultas (segundos)"
                    value={settings.interval_between_queries}
                    onChange={(e) => setSettings({...settings, interval_between_queries: parseInt(e.target.value) || 5})}
                    inputProps={{ min: 1, max: 60 }}
                    helperText="Tempo de espera entre cada consulta"
                  />
                </Grid>

                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.headless}
                        onChange={(e) => setSettings({...settings, headless: e.target.checked})}
                      />
                    }
                    label="Modo Headless"
                  />
                  <Typography variant="caption" display="block" color="text.secondary">
                    Executar navegador em segundo plano (recomendado)
                  </Typography>
                </Grid>
              </Grid>

              <Divider sx={{ my: 3 }} />

              <Button
                fullWidth
                variant="contained"
                size="large"
                startIcon={<PlayIcon />}
                onClick={handleStartSearch}
                disabled={startSearchMutation.isLoading || queries.every(q => !q.search_term.trim() || !q.location.trim())}
              >
                {startSearchMutation.isLoading ? 'Iniciando...' : 'Iniciar Busca Automatizada'}
              </Button>

              {queries.every(q => !q.search_term.trim() || !q.location.trim()) && (
                <Alert severity="warning" sx={{ mt: 2 }}>
                  Preencha pelo menos uma consulta válida para continuar
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Progress do Job */}
        {currentJobId && (
          <Grid item xs={12}>
            <JobProgress
              jobId={currentJobId}
              onComplete={() => setCurrentJobId(null)}
              title="Busca Automatizada em Andamento"
            />
          </Grid>
        )}

        {/* Informações */}
        <Grid item xs={12}>
          <Alert severity="info">
            <Typography variant="body2" fontWeight="bold" gutterBottom>
              Como funciona a Busca Automatizada:
            </Typography>
            <ul style={{ margin: 0, paddingLeft: 20 }}>
              <li>Configure múltiplas consultas com diferentes termos e localizações</li>
              <li>O sistema executará cada consulta sequencialmente</li>
              <li>Aguardará o intervalo configurado entre cada consulta</li>
              <li>Todos os resultados serão consolidados em um único arquivo</li>
              <li>Você pode pausar, retomar ou cancelar a operação a qualquer momento</li>
            </ul>
          </Alert>
        </Grid>
      </Grid>
    </Box>
  )
}

export default AutomatedSearch
