import React from 'react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { renderWithProviders, createMockApiResponse } from '../../test/utils'
import Dashboard from '../Dashboard'
import { useQuery } from 'react-query'

// Mock do react-query
vi.mock('react-query', () => ({
  useQuery: vi.fn(),
  useQueryClient: () => ({
    invalidateQueries: vi.fn(),
  }),
}))

// Mock do react-router-dom
const mockNavigate = vi.fn()
vi.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate,
}))

// Mock dos dados de estatísticas
const mockStats = {
  total_jobs: 25,
  active_jobs: 3,
  completed_jobs: 22,
  total_results: 1250,
  recent_jobs: [
    {
      id: 'job-1',
      type: 'flexible_search',
      status: 'completed',
      created_at: '2024-01-01T10:00:00Z',
      parameters: { location: 'São Paulo' },
      results_count: 50,
    },
    {
      id: 'job-2',
      type: 'cep_search',
      status: 'running',
      created_at: '2024-01-01T11:00:00Z',
      parameters: { cep: '01310-100' },
      progress: 75,
    },
  ],
}

describe('Dashboard', () => {
  const user = userEvent.setup()

  beforeEach(() => {
    vi.clearAllMocks()
    mockNavigate.mockClear()
  })

  describe('Carregamento de dados', () => {
    it('deve mostrar loading enquanto carrega dados', () => {
      useQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      })

      renderWithProviders(<Dashboard />)

      expect(screen.getByRole('progressbar')).toBeInTheDocument()
    })

    it('deve mostrar erro quando falha ao carregar', () => {
      useQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: new Error('Falha ao carregar dados'),
      })

      renderWithProviders(<Dashboard />)

      expect(screen.getByText(/erro ao carregar/i)).toBeInTheDocument()
    })

    it('deve carregar e exibir dados com sucesso', () => {
      useQuery.mockReturnValue({
        data: mockStats,
        isLoading: false,
        error: null,
      })

      renderWithProviders(<Dashboard />)

      expect(screen.getByText('Dashboard')).toBeInTheDocument()
      expect(screen.getByText('25')).toBeInTheDocument() // total_jobs
      expect(screen.getByText('3')).toBeInTheDocument() // active_jobs
    })
  })

  describe('Estatísticas gerais', () => {
    beforeEach(() => {
      useQuery.mockReturnValue({
        data: mockStats,
        isLoading: false,
        error: null,
      })
    })

    it('deve exibir total de jobs', () => {
      renderWithProviders(<Dashboard />)

      expect(screen.getByText('Total de Jobs')).toBeInTheDocument()
      expect(screen.getByText('25')).toBeInTheDocument()
    })

    it('deve exibir jobs ativos', () => {
      renderWithProviders(<Dashboard />)

      expect(screen.getByText('Jobs Ativos')).toBeInTheDocument()
      expect(screen.getByText('3')).toBeInTheDocument()
    })

    it('deve exibir jobs concluídos', () => {
      renderWithProviders(<Dashboard />)

      expect(screen.getByText('Jobs Concluídos')).toBeInTheDocument()
      expect(screen.getByText('22')).toBeInTheDocument()
    })

    it('deve exibir total de resultados', () => {
      renderWithProviders(<Dashboard />)

      expect(screen.getByText('Total de Resultados')).toBeInTheDocument()
      expect(screen.getByText('1.250')).toBeInTheDocument() // formatado
    })
  })

  describe('Jobs recentes', () => {
    beforeEach(() => {
      useQuery.mockReturnValue({
        data: mockStats,
        isLoading: false,
        error: null,
      })
    })

    it('deve exibir seção de jobs recentes', () => {
      renderWithProviders(<Dashboard />)

      expect(screen.getByText('Jobs Recentes')).toBeInTheDocument()
    })

    it('deve listar jobs recentes', () => {
      renderWithProviders(<Dashboard />)

      expect(screen.getByText('Busca Flexível')).toBeInTheDocument()
      expect(screen.getByText('Busca por CEP')).toBeInTheDocument()
    })

    it('deve mostrar status dos jobs', () => {
      renderWithProviders(<Dashboard />)

      expect(screen.getByText('Concluído')).toBeInTheDocument()
      expect(screen.getByText('Em Execução')).toBeInTheDocument()
    })

    it('deve mostrar progresso para jobs em execução', () => {
      renderWithProviders(<Dashboard />)

      // Job em execução deve mostrar progresso
      expect(screen.getByText('75%')).toBeInTheDocument()
    })

    it('deve mostrar contagem de resultados para jobs concluídos', () => {
      renderWithProviders(<Dashboard />)

      // Job concluído deve mostrar resultados
      expect(screen.getByText('50 resultados')).toBeInTheDocument()
    })
  })

  describe('Ações rápidas', () => {
    beforeEach(() => {
      useQuery.mockReturnValue({
        data: mockStats,
        isLoading: false,
        error: null,
      })
    })

    it('deve exibir botões de ação rápida', () => {
      renderWithProviders(<Dashboard />)

      expect(screen.getByText('Nova Busca Flexível')).toBeInTheDocument()
      expect(screen.getByText('Busca por CEP')).toBeInTheDocument()
      expect(screen.getByText('Ver Todos os Jobs')).toBeInTheDocument()
    })

    it('deve navegar para busca flexível', async () => {
      renderWithProviders(<Dashboard />)

      const button = screen.getByText('Nova Busca Flexível')
      await user.click(button)

      expect(mockNavigate).toHaveBeenCalledWith('/flexible-search')
    })

    it('deve navegar para busca por CEP', async () => {
      renderWithProviders(<Dashboard />)

      const button = screen.getByText('Busca por CEP')
      await user.click(button)

      expect(mockNavigate).toHaveBeenCalledWith('/cep-search')
    })

    it('deve navegar para página de jobs', async () => {
      renderWithProviders(<Dashboard />)

      const button = screen.getByText('Ver Todos os Jobs')
      await user.click(button)

      expect(mockNavigate).toHaveBeenCalledWith('/jobs')
    })
  })

  describe('Gráficos e visualizações', () => {
    beforeEach(() => {
      useQuery.mockReturnValue({
        data: mockStats,
        isLoading: false,
        error: null,
      })
    })

    it('deve exibir gráfico de status de jobs', () => {
      renderWithProviders(<Dashboard />)

      // Verificar se há elementos de gráfico
      expect(screen.getByText('Status dos Jobs')).toBeInTheDocument()
    })

    it('deve mostrar distribuição correta no gráfico', () => {
      renderWithProviders(<Dashboard />)

      // Verificar dados do gráfico
      expect(screen.getByText('Ativos: 3')).toBeInTheDocument()
      expect(screen.getByText('Concluídos: 22')).toBeInTheDocument()
    })
  })

  describe('Responsividade', () => {
    beforeEach(() => {
      useQuery.mockReturnValue({
        data: mockStats,
        isLoading: false,
        error: null,
      })
    })

    it('deve ter layout responsivo', () => {
      renderWithProviders(<Dashboard />)

      const container = screen.getByTestId('dashboard-container')
      expect(container).toHaveClass('MuiContainer-root')
    })

    it('deve organizar cards em grid responsivo', () => {
      renderWithProviders(<Dashboard />)

      const grid = screen.getByTestId('stats-grid')
      expect(grid).toHaveClass('MuiGrid-container')
    })
  })

  describe('Atualização de dados', () => {
    it('deve atualizar dados automaticamente', async () => {
      const mockRefetch = vi.fn()
      
      useQuery.mockReturnValue({
        data: mockStats,
        isLoading: false,
        error: null,
        refetch: mockRefetch,
      })

      renderWithProviders(<Dashboard />)

      // Simular atualização automática (se implementada)
      await waitFor(() => {
        expect(useQuery).toHaveBeenCalledWith(
          'dashboard-stats',
          expect.any(Function),
          expect.objectContaining({
            refetchInterval: expect.any(Number),
          })
        )
      })
    })

    it('deve permitir atualização manual', async () => {
      const mockRefetch = vi.fn()
      
      useQuery.mockReturnValue({
        data: mockStats,
        isLoading: false,
        error: null,
        refetch: mockRefetch,
      })

      renderWithProviders(<Dashboard />)

      const refreshButton = screen.getByLabelText(/atualizar/i)
      await user.click(refreshButton)

      expect(mockRefetch).toHaveBeenCalled()
    })
  })

  describe('Estados vazios', () => {
    it('deve mostrar estado vazio quando não há jobs', () => {
      useQuery.mockReturnValue({
        data: {
          ...mockStats,
          total_jobs: 0,
          recent_jobs: [],
        },
        isLoading: false,
        error: null,
      })

      renderWithProviders(<Dashboard />)

      expect(screen.getByText('Nenhum job encontrado')).toBeInTheDocument()
      expect(screen.getByText('Comece criando sua primeira busca')).toBeInTheDocument()
    })

    it('deve mostrar botão para criar primeiro job', async () => {
      useQuery.mockReturnValue({
        data: {
          ...mockStats,
          total_jobs: 0,
          recent_jobs: [],
        },
        isLoading: false,
        error: null,
      })

      renderWithProviders(<Dashboard />)

      const createButton = screen.getByText('Criar Primeira Busca')
      await user.click(createButton)

      expect(mockNavigate).toHaveBeenCalledWith('/flexible-search')
    })
  })

  describe('Formatação de dados', () => {
    beforeEach(() => {
      useQuery.mockReturnValue({
        data: mockStats,
        isLoading: false,
        error: null,
      })
    })

    it('deve formatar números grandes corretamente', () => {
      renderWithProviders(<Dashboard />)

      // 1250 deve ser formatado como 1.250
      expect(screen.getByText('1.250')).toBeInTheDocument()
    })

    it('deve formatar datas corretamente', () => {
      renderWithProviders(<Dashboard />)

      // Verificar se datas estão formatadas
      expect(screen.getByText(/01\/01\/2024/)).toBeInTheDocument()
    })

    it('deve traduzir tipos de job', () => {
      renderWithProviders(<Dashboard />)

      expect(screen.getByText('Busca Flexível')).toBeInTheDocument()
      expect(screen.getByText('Busca por CEP')).toBeInTheDocument()
    })
  })
})
