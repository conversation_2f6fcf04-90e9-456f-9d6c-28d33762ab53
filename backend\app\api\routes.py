"""
Rotas principais da API TeemoFlow
"""
from fastapi import APIRouter
from .endpoints import cep_search, gmaps_search, automated_search, messaging, jobs, files, flexible_search

# Router principal da API
api_router = APIRouter()

# Incluir rotas dos endpoints
api_router.include_router(
    flexible_search.router,
    prefix="/flexible-search",
    tags=["Busca Flexível"]
)

api_router.include_router(
    cep_search.router,
    prefix="/cep-search",
    tags=["Busca por CEP"]
)

api_router.include_router(
    gmaps_search.router,
    prefix="/gmaps-search",
    tags=["Google Maps"]
)

api_router.include_router(
    automated_search.router,
    prefix="/automated-search",
    tags=["Busca Automatizada"]
)

api_router.include_router(
    messaging.router,
    prefix="/messaging",
    tags=["Sistema de Mensagens"]
)

api_router.include_router(
    jobs.router,
    prefix="/jobs",
    tags=["Gerenciamento de Jobs"]
)

api_router.include_router(
    files.router,
    prefix="/files",
    tags=["Gerenciamento de Arquivos"]
)
